<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Auth0 | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Rapidly integrate authentication and authorization for web, mobile, and legacy applications so you can focus on your core business." data-next-head=""/><meta property="og:title" content="Auth0 | Works With Supabase" data-next-head=""/><meta property="og:description" content="Rapidly integrate authentication and authorization for web, mobile, and legacy applications so you can focus on your core business." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/auth0" data-next-head=""/><meta property="og:image" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Auth0" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fauth0%2Fauth0_dark.png%3Ft%3D2023-07-19T19%253A13%253A04.189Z&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fauth0%2Fauth0_dark.png%3Ft%3D2023-07-19T19%253A13%253A04.189Z&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fauth0%2Fauth0_dark.png%3Ft%3D2023-07-19T19%253A13%253A04.189Z&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Auth0</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Using Auth0, developers can connect any application written in any language or stack, and define the external identity providers, as well as integrations, that they want to use. This short Auth0 product demo gives an overview of this process, touching upon Auth0’s unmatched extensibility and its applicability to B2B, B2C, and B2E use cases.</p>
<h2>Documentation</h2>
<p>This guide steps through building a Next.js application with Auth0 and Supabase. We configure Auth0 to handle authenticating users and managing tokens, while writing our authorization logic in Supabase - using Row Level Security policies.</p>
<blockquote>
<p>Note: This guide is heavily inspired by the <a href="https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/">Using Next.js and Auth0 with Supabase</a> article on <a href="https://auth0.com/blog/">Auth0&#x27;s blog</a>. Check it out for a practical step-by-step guide on integrating Auth0 and Supabase.</p>
</blockquote>
<p>The full code example for this guide can be found <a href="https://github.com/dijonmusters/supabase-auth0-example">here</a>.</p>
<p><a href="https://auth0.com/">Auth0</a> is an authentication and authorization platform, offering numerous strategies to authenticate and manage users. It provides fine-grain control over how users sign in to your application, the token that is generated, and what data is stored about your users.</p>
<p><a href="https://nextjs.org/">Next.js</a> is a web application framework built on top of React. We will be using it for this example, as it allows us to write server-side logic within our application. Auth0 have also written a <a href="https://www.npmjs.com/package/@auth0/nextjs-auth0">very well integrated authentication library</a> specifically for Next.js.</p>
<blockquote>
<p>Note: API routes (serverless functions) in Next.js closely resemble the structure of Node server frameworks - such as Express, Koa and Fastify. The server-side logic in this guide could easily be refactored in one of these frameworks and managed as a separate application to the front-end.</p>
</blockquote>
<p>If you don’t have an Auth0 account, create one <a href="https://auth0.com/signup">here</a>.</p>
<p>You will also need a Supabase account, which can be created by signing in <a href="../../dashboard/org.html">here</a>.</p>
<h2>Step 1: Creating an Auth0 tenant</h2>
<p>From the Auth0 dashboard, click the menu to the right of the Auth0 logo, and select <code>Create tenant</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/IYzHxeW.png" alt="Create tenant from Auth0 dashboard"/></p>
<p>Enter a <code>Domain</code> for your tenant - this will need to be unique.</p>
<p>Select a <code>Region</code> - this should be geographically close to the majority of your users.</p>
<p>Select <code>Development</code> for <code>Environment Tag</code> - this should be production when you&#x27;re ready to go live.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/iSA3E0J.png" alt="Auth0 tenant settings"/></p>
<h2>Step 2: Setting up an Auth0 application</h2>
<p>From the sidebar menu, select <code>Applications</code> &gt; <code>Applications</code> and click <code>Create Application</code>.</p>
<p>Give your application a name, select the <code>Regular Web Applications</code> option and click <code>Create</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/ANU4Wez.png" alt="Auth0 application settings"/></p>
<p>Select <code>Settings</code> and navigate to the <code>Application URIs</code> section, and update the following:</p>
<p><code>Allowed Callback URLs</code>: <code>http://localhost:3000/api/auth/callback</code></p>
<p><code>Allowed Logout URLs</code>: <code>http://localhost:3000</code></p>
<p>Scroll to the bottom of the <code>Settings</code> section and reveal the <code>Advanced Settings</code>.</p>
<p>Select <code>OAuth</code> and set <code>JSON Web Token Signature</code> to <code>RS256</code>.</p>
<p>Confirm <code>OIDC Conformant</code> is <code>Enabled</code>.</p>
<p>Click <code>Save</code> to update the settings.</p>
<h2>Step 3: Creating a Supabase project</h2>
<p>From your <a href="../../dashboard/org.html">Supabase dashboard</a>, click <code>New project</code>.</p>
<p>Enter a <code>Name</code> for your Supabase project.</p>
<p>Enter a secure <code>Database Password</code>.</p>
<p>Select the same <code>Region</code> you selected for your Auth0 tenant.</p>
<p>Click <code>Create new project</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/qnmJEU7.png" alt="New Supabase project settings"/></p>
<h2>Step 4: Creating data in Supabase</h2>
<p>From the sidebar menu in the <a href="../../dashboard/org.html">Supabase dashboard</a>, click <code>Table editor</code>, then <code>New table</code>.</p>
<p>Enter <code>todo</code> as the <code>Name</code> field.</p>
<p>Select <code>Enable Row Level Security (RLS)</code>.</p>
<p>Create two new columns:</p>
<ul>
<li><code>title</code> as <code>text</code></li>
<li><code>user_id</code> as <code>text</code></li>
<li><code>is_complete</code> as <code>bool</code> with the default value <code>false</code></li>
</ul>
<p>Click <code>Save</code> to create the new table.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/33kqP4K.png" alt="Todo table"/></p>
<p>From the <code>Table editor</code> view, select the <code>todo</code> table and click <code>Insert row</code>.</p>
<p>Fill out the <code>title</code> field and click <code>Save</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/mEhHAWC.png" alt="New row settings"/></p>
<p>Click <code>Insert row</code> and add a couple of extra todos.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png" alt="List of todos"/></p>
<h2>Step 5: Building a Next.js app</h2>
<p>Create a new Next.js project:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npx create-next-app &lt;name-of-project&gt;</span></div></div><br/></code></div></div>
<p>Create a <code>.env.local</code> file and enter the following values:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>AUTH0_SECRET=any-secure-value</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>AUTH0_BASE_URL=http://localhost:3000</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>AUTH0_ISSUER_BASE_URL=https://&lt;name-of-your-tenant&gt;.&lt;region-you-selected&gt;.auth0.com</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>AUTH0_CLIENT_ID=get-from-auth0-dashboard</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>AUTH0_CLIENT_SECRET=get-from-auth0-dashboard</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>NEXT_PUBLIC_SUPABASE_URL=get-from-supabase-dashboard</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>NEXT_PUBLIC_SUPABASE_ANON_KEY=get-from-supabase-dashboard</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>SUPABASE_JWT_SECRET=get-from-supabase-dashboard</span></div></div><br/></code></div></div>
<blockquote>
<p>Note: Auth0 values can be found under <code>Settings &gt; Basic Information</code> for your application.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/o07FaoV.png" alt="Auth0 settings"/></p>
<blockquote>
<p>Note: Supabase values can be found under <code>Settings &gt; API</code> for your project.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/r1GAfLo.png" alt="Supabase settings"/></p>
<p>Restart your Next.js development server to read in the new values from <code>.env.local</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm run dev</span></div></div><br/></code></div></div>
<h2>Step 6: Install Auth0 Next.js library</h2>
<p>Install the <code>@auth0/nextjs-auth0</code> library.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm i @auth0/nextjs-auth0</span></div></div><br/></code></div></div>
<p>Create a new file <code>pages/api/auth/[...auth0].js</code> and add:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// pages/api/auth/[...auth0].js</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { handleAuth } from &#x27;@auth0/nextjs-auth0&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>export default handleAuth()</span></div></div><br/></code></div></div>
<blockquote>
<p>Note: This will create a few API routes for us. The main ones we will use are <code>/api/auth/login</code> and <code>/api/auth/logout</code> to handle signing users in and out.</p>
</blockquote>
<p>Open <code>pages/_app.js</code> and wrap our <code>Component</code> with the <code>UserProvider</code> from Auth0:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>// pages/_app.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>import React from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>import { UserProvider } from &#x27;@auth0/nextjs-auth0/client&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>const App = ({ Component, pageProps }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>    &lt;UserProvider&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>      &lt;Component {...pageProps} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/UserProvider&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>export default App</span></div></div><br/></code></div></div>
<p>Update <code>pages/index.js</code> to ensure the user is logged in to view the landing page.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>import { withPageAuthRequired } from &#x27;@auth0/nextjs-auth0&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>import Link from &#x27;next/link&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div className={styles.container}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>      &lt;p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>        Welcome {user.name}!{&#x27; &#x27;}</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>        &lt;Link href=&quot;/api/auth/logout&quot;&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>          &lt;a&gt;Logout&lt;/a&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>        &lt;/Link&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>export const getServerSideProps = withPageAuthRequired()</span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->22</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<blockquote>
<p>Note: <code>withPageAuthRequired</code> will automatically redirect the user to <code>/api/auth/login</code> if they are not currently logged in.</p>
</blockquote>
<p>Test this is working by navigating to <code>http://localhost:3000</code> which should redirect you to an Auth0 sign in screen.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/xLRL7S7.png" alt="Auth0 sign in screen"/></p>
<p>Either <code>Sign up</code> for a new account, or click <code>Continue with Google</code> to sign in.</p>
<p>You should now be able to view the landing page.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/YdBKRy6.png" alt="Landing page"/></p>
<h2>Step 7: Sign Auth0 token for Supabase</h2>
<p>Currently, neither Supabase or Auth0 allow for a custom signing secret to be set for their JWT. They also use different <a href="https://auth0.com/docs/configure/applications/signing-algorithms">signing algorithms</a>.</p>
<p>Therefore, we need to extract the bits we need from Auth0&#x27;s JWT, and sign our own to send to Supabase.</p>
<p>We can do that using Auth0&#x27;s <code>afterCallback</code> function, which gets called anytime the user authenticates.</p>
<p>Install the <code>jsonwebtoken</code> library.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm i jsonwebtoken</span></div></div><br/></code></div></div>
<p>Update <code>pages/api/auth/[...auth0].js</code> with the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>// pages/api/auth/[...auth0].js</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>import { handleAuth, handleCallback } from &#x27;@auth0/nextjs-auth0&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>import jwt from &#x27;jsonwebtoken&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>const afterCallback = async (req, res, session) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  const payload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    userId: session.user.sub,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    exp: Math.floor(Date.now() / 1000) + 60 * 60,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  session.user.accessToken = jwt.sign(payload, process.env.SUPABASE_JWT_SECRET)</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  return session</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>export default handleAuth({</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  async callback(req, res) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    try {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      await handleCallback(req, res, { afterCallback })</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    } catch (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      res.status(error.status || 500).end(error.message)</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<p>Our <code>payload</code> for the JWT will contain our user&#x27;s unique identifier from Auth0 - <code>session.user.sub</code> and an expiry of 1 hour.</p>
<p>We are signing this JWT using Supabase&#x27;s signing secret, so Supabase will be able to validate it is authentic and hasn&#x27;t been tampered with in transit.</p>
<blockquote>
<p>Note: We need to sign the user out and back in again to run the <code>afterCallback</code> function, and create our new token.</p>
</blockquote>
<p>Now we just need to send the token along with the request to Supabase.</p>
<h2>Step 8: Requesting data from Supabase</h2>
<p>Create a new file called <code>utils/supabase.js</code> and add the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>// utils/supabase.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>const getSupabase = (access_token) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  const options = {}</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  if (access_token) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    options.global = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      headers: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>        Authorization: `Bearer ${access_token}`,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = createClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_URL,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    options</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  return supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>export { getSupabase }</span></div></div><br/></code></div></div>
<p>This will be our client for talking to Supabase. We can pass it an <code>access_token</code> and it will be attached to our request.</p>
<p>Let&#x27;s load our <code>todos</code> from Supabase in our landing page!</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>import { withPageAuthRequired } from &#x27;@auth0/nextjs-auth0&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>import { getSupabase } from &#x27;../utils/supabase&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>import Link from &#x27;next/link&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>import { useEffect } from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  const [todos, setTodos] = useState([])</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = getSupabase(user.accessToken)</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  useEffect(() =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>    const fetchTodos = async () =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      const { data } = await supabase.from(&#x27;todo&#x27;).select(&#x27;*&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      setTodos(data)</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>    fetchTodos()</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  }, [])</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div className={styles.container}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      &lt;p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>        Welcome {user.name}!{&#x27; &#x27;}</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>        &lt;Link href=&quot;/api/auth/logout&quot;&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>          &lt;a&gt;Logout&lt;/a&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>        &lt;/Link&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      {todos?.length &gt; 0 ? (</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>        todos.map((todo) =&gt; &lt;p key={todo.id}&gt;{todo.content}&lt;/p&gt;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      ) : (</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>        &lt;p&gt;You have completed all todos!&lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>      )}</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>export const getServerSideProps = withPageAuthRequired()</span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->41</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<p>Alternatively, we could fetch todos on the server using the <code>getServerSideProps</code> function.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>import { withPageAuthRequired, getSession } from &#x27;@auth0/nextjs-auth0&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>import { getSupabase } from &#x27;../utils/supabase&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>import Link from &#x27;next/link&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user, todos }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div className={styles.container}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      &lt;p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>        Welcome {user.name}!{&#x27; &#x27;}</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>        &lt;Link href=&quot;/api/auth/logout&quot;&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>          &lt;a&gt;Logout&lt;/a&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>        &lt;/Link&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      {todos?.length &gt; 0 ? (</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>        todos.map((todo) =&gt; &lt;p key={todo.id}&gt;{todo.content}&lt;/p&gt;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      ) : (</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>        &lt;p&gt;You have completed all todos!&lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      )}</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>export const getServerSideProps = withPageAuthRequired({</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>  async getServerSideProps({ req, res }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    const {</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      user: { accessToken },</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    } = await getSession(req, res)</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    const supabase = getSupabase(accessToken)</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    const { data: todos } = await supabase.from(&#x27;todo&#x27;).select(&#x27;*&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>      props: { todos },</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->42</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<p>Either way, when we reload our application, we are still getting the empty state for todos.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/XgEMwnN.png" alt="Empty todo list"/></p>
<p>This is because we enabled Row Level Security, which blocks all requests by default. To enable our user to select their <code>todos</code> we need to write a policy.</p>
<h2>Step 9: Write a policy to allow select</h2>
<p>Our policy will need to know who our currently logged in user is to determine whether or not they should have access. Let&#x27;s create a PostgreSQL function to extract the current user from our new JWT.</p>
<p>Navigate back to the Supabase dashboard, select <code>SQL</code> from the sidebar menu, and click <code>New query</code>. This will create a new query called <code>new sql snippet</code>, which will allow us to run any SQL against our Postgres database.</p>
<p>Write the following and click <code>Run</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create or replace function auth.user_id() returns text as $$</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  select nullif(current_setting(&#x27;request.jwt.claims&#x27;, true)::json-&gt;&gt;&#x27;userId&#x27;, &#x27;&#x27;)::text;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>$$ language sql stable;</span></div></div><br/></code></div></div>
<p>This will create a function called <code>auth.user_id()</code>, which will inspect the <code>userId</code> field of our JWT payload.</p>
<blockquote>
<p>Note: To learn more about PostgreSQL functions, check out <a href="https://www.youtube.com/watch?v=MJZCCpCYEqk">our deep dive video</a>.</p>
</blockquote>
<p>Let&#x27;s create a policy that checks whether this user is the owner of the todo.</p>
<p>Select <code>Authentication</code> from the Supabase sidebar menu, click <code>Policies</code>, and then <code>New Policy</code> on the <code>todo</code> table.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/M7XyhHe.png" alt="Create new policy"/></p>
<p>From the modal, select <code>Create a policy from scratch</code> and add the following.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/wuWz3am.png" alt="Policy settings for SELECT"/></p>
<p>This policy is calling the function we just created to get the currently logged in user&#x27;s ID <code>auth.user_id()</code> and checking whether this matches the <code>user_id</code> column for the current <code>todo</code>. If it does, then it will allow the user to select it, otherwise it will continue to deny.</p>
<p>Click <code>Review</code> and then <code>Save policy</code>.</p>
<blockquote>
<p>Note: To learn more about RLS and policies, check out <a href="https://www.youtube.com/watch?v=Ow_Uzedfohk">our deep dive video</a>.</p>
</blockquote>
<p>The last thing we need to do is update the <code>user_id</code> columns for our existing <code>todos</code>.</p>
<p>Head back to the Supabase dashboard, and select <code>Table editor</code> from the sidebar.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png" alt="User ID null in Supabase Table Editor"/></p>
<p>Each of our <code>user_id</code> columns are set to <code>NULL</code>!</p>
<p>To get the ID for our Auth0 user, head over to the Auth0 dashboard, select <code>User Management</code> from the sidebar, click <code>Users</code> and select your test user.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/GdXS013.png" alt="List of users in Auth0 dashboard"/></p>
<p>Copy their <code>user_id</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tbvd0Uj.png" alt="User ID in Auth0 dashboard"/></p>
<p>Update each row in Supabase.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tPu4Tt8.png" alt="User ID set to Auth0 user"/></p>
<p>Now when we refresh our application, we should finally see our list of <code>todos</code>!</p>
<blockquote>
<p>Note: Check out <a href="https://github.com/dijonmusters/supabase-auth0-example/blob/main/pages/index.js">the repo</a> for an example of writing new <code>todos</code> to Supabase.</p>
</blockquote>
<h2>Resources</h2>
<ul>
<li><a href="https://auth0.com/">Auth0</a> official website.</li>
<li><a href="https://auth0.com/blog/">Auth0 blog</a>.</li>
<li><a href="https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/">Using Next.js and Auth0 with Supabase article</a>.</li>
<li><a href="https://community.auth0.com/">Auth0 community</a>.</li>
<li><a href="https://auth0.com/docs/">Auth0 documentation</a>.</li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Auth0</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#auth">Auth</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://auth0.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">auth0.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://auth0.com/docs" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":83,"slug":"auth0","type":"technology","category":"Auth","developer":"Auth0","title":"Auth0","description":"Rapidly integrate authentication and authorization for web, mobile, and legacy applications so you can focus on your core business.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/auth0_dark.png?t=2023-07-19T19%3A13%3A04.189Z","images":[],"overview":"Using Auth0, developers can connect any application written in any language or stack, and define the external identity providers, as well as integrations, that they want to use. This short Auth0 product demo gives an overview of this process, touching upon Auth0’s unmatched extensibility and its applicability to B2B, B2C, and B2E use cases.\n\n## Documentation\n\nThis guide steps through building a Next.js application with Auth0 and Supabase. We configure Auth0 to handle authenticating users and managing tokens, while writing our authorization logic in Supabase - using Row Level Security policies.\n\n\u003e Note: This guide is heavily inspired by the [Using Next.js and Auth0 with Supabase](https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/) article on [Auth0's blog](https://auth0.com/blog/). Check it out for a practical step-by-step guide on integrating Auth0 and Supabase.\n\nThe full code example for this guide can be found [here](https://github.com/dijonmusters/supabase-auth0-example).\n\n[Auth0](https://auth0.com/) is an authentication and authorization platform, offering numerous strategies to authenticate and manage users. It provides fine-grain control over how users sign in to your application, the token that is generated, and what data is stored about your users.\n\n[Next.js](https://nextjs.org/) is a web application framework built on top of React. We will be using it for this example, as it allows us to write server-side logic within our application. Auth0 have also written a [very well integrated authentication library](https://www.npmjs.com/package/@auth0/nextjs-auth0) specifically for Next.js.\n\n\u003e Note: API routes (serverless functions) in Next.js closely resemble the structure of Node server frameworks - such as Express, Koa and Fastify. The server-side logic in this guide could easily be refactored in one of these frameworks and managed as a separate application to the front-end.\n\nIf you don’t have an Auth0 account, create one [here](https://auth0.com/signup).\n\nYou will also need a Supabase account, which can be created by signing in [here](https://supabase.com/dashboard/).\n\n## Step 1: Creating an Auth0 tenant\n\nFrom the Auth0 dashboard, click the menu to the right of the Auth0 logo, and select `Create tenant`.\n\n![Create tenant from Auth0 dashboard](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/IYzHxeW.png)\n\nEnter a `Domain` for your tenant - this will need to be unique.\n\nSelect a `Region` - this should be geographically close to the majority of your users.\n\nSelect `Development` for `Environment Tag` - this should be production when you're ready to go live.\n\n![Auth0 tenant settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/iSA3E0J.png)\n\n## Step 2: Setting up an Auth0 application\n\nFrom the sidebar menu, select `Applications` \u003e `Applications` and click `Create Application`.\n\nGive your application a name, select the `Regular Web Applications` option and click `Create`.\n\n![Auth0 application settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/ANU4Wez.png)\n\nSelect `Settings` and navigate to the `Application URIs` section, and update the following:\n\n`Allowed Callback URLs`: `http://localhost:3000/api/auth/callback`\n\n`Allowed Logout URLs`: `http://localhost:3000`\n\nScroll to the bottom of the `Settings` section and reveal the `Advanced Settings`.\n\nSelect `OAuth` and set `JSON Web Token Signature` to `RS256`.\n\nConfirm `OIDC Conformant` is `Enabled`.\n\nClick `Save` to update the settings.\n\n## Step 3: Creating a Supabase project\n\nFrom your [Supabase dashboard](https://supabase.com/dashboard/), click `New project`.\n\nEnter a `Name` for your Supabase project.\n\nEnter a secure `Database Password`.\n\nSelect the same `Region` you selected for your Auth0 tenant.\n\nClick `Create new project`.\n\n![New Supabase project settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/qnmJEU7.png)\n\n## Step 4: Creating data in Supabase\n\nFrom the sidebar menu in the [Supabase dashboard](https://supabase.com/dashboard/), click `Table editor`, then `New table`.\n\nEnter `todo` as the `Name` field.\n\nSelect `Enable Row Level Security (RLS)`.\n\nCreate two new columns:\n\n- `title` as `text`\n- `user_id` as `text`\n- `is_complete` as `bool` with the default value `false`\n\nClick `Save` to create the new table.\n\n![Todo table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/33kqP4K.png)\n\nFrom the `Table editor` view, select the `todo` table and click `Insert row`.\n\nFill out the `title` field and click `Save`.\n\n![New row settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/mEhHAWC.png)\n\nClick `Insert row` and add a couple of extra todos.\n\n![List of todos](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png)\n\n## Step 5: Building a Next.js app\n\nCreate a new Next.js project:\n\n```bash\nnpx create-next-app \u003cname-of-project\u003e\n```\n\nCreate a `.env.local` file and enter the following values:\n\n```\nAUTH0_SECRET=any-secure-value\nAUTH0_BASE_URL=http://localhost:3000\nAUTH0_ISSUER_BASE_URL=https://\u003cname-of-your-tenant\u003e.\u003cregion-you-selected\u003e.auth0.com\nAUTH0_CLIENT_ID=get-from-auth0-dashboard\nAUTH0_CLIENT_SECRET=get-from-auth0-dashboard\nNEXT_PUBLIC_SUPABASE_URL=get-from-supabase-dashboard\nNEXT_PUBLIC_SUPABASE_ANON_KEY=get-from-supabase-dashboard\nSUPABASE_JWT_SECRET=get-from-supabase-dashboard\n```\n\n\u003e Note: Auth0 values can be found under `Settings \u003e Basic Information` for your application.\n\n![Auth0 settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/o07FaoV.png)\n\n\u003e Note: Supabase values can be found under `Settings \u003e API` for your project.\n\n![Supabase settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/r1GAfLo.png)\n\nRestart your Next.js development server to read in the new values from `.env.local`.\n\n```bash\nnpm run dev\n```\n\n## Step 6: Install Auth0 Next.js library\n\nInstall the `@auth0/nextjs-auth0` library.\n\n```bash\nnpm i @auth0/nextjs-auth0\n```\n\nCreate a new file `pages/api/auth/[...auth0].js` and add:\n\n```jsx\n// pages/api/auth/[...auth0].js\n\nimport { handleAuth } from '@auth0/nextjs-auth0'\n\nexport default handleAuth()\n```\n\n\u003e Note: This will create a few API routes for us. The main ones we will use are `/api/auth/login` and `/api/auth/logout` to handle signing users in and out.\n\nOpen `pages/_app.js` and wrap our `Component` with the `UserProvider` from Auth0:\n\n```jsx\n// pages/_app.js\n\nimport React from 'react'\nimport { UserProvider } from '@auth0/nextjs-auth0/client'\n\nconst App = ({ Component, pageProps }) =\u003e {\n  return (\n    \u003cUserProvider\u003e\n      \u003cComponent {...pageProps} /\u003e\n    \u003c/UserProvider\u003e\n  )\n}\n\nexport default App\n```\n\nUpdate `pages/index.js` to ensure the user is logged in to view the landing page.\n\n```jsx\n// pages/index.js\n\nimport styles from '../styles/Home.module.css'\nimport { withPageAuthRequired } from '@auth0/nextjs-auth0'\nimport Link from 'next/link'\n\nconst Index = ({ user }) =\u003e {\n  return (\n    \u003cdiv className={styles.container}\u003e\n      \u003cp\u003e\n        Welcome {user.name}!{' '}\n        \u003cLink href=\"/api/auth/logout\"\u003e\n          \u003ca\u003eLogout\u003c/a\u003e\n        \u003c/Link\u003e\n      \u003c/p\u003e\n    \u003c/div\u003e\n  )\n}\n\nexport const getServerSideProps = withPageAuthRequired()\n\nexport default Index\n```\n\n\u003e Note: `withPageAuthRequired` will automatically redirect the user to `/api/auth/login` if they are not currently logged in.\n\nTest this is working by navigating to `http://localhost:3000` which should redirect you to an Auth0 sign in screen.\n\n![Auth0 sign in screen](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/xLRL7S7.png)\n\nEither `Sign up` for a new account, or click `Continue with Google` to sign in.\n\nYou should now be able to view the landing page.\n\n![Landing page](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/YdBKRy6.png)\n\n## Step 7: Sign Auth0 token for Supabase\n\nCurrently, neither Supabase or Auth0 allow for a custom signing secret to be set for their JWT. They also use different [signing algorithms](https://auth0.com/docs/configure/applications/signing-algorithms).\n\nTherefore, we need to extract the bits we need from Auth0's JWT, and sign our own to send to Supabase.\n\nWe can do that using Auth0's `afterCallback` function, which gets called anytime the user authenticates.\n\nInstall the `jsonwebtoken` library.\n\n```bash\nnpm i jsonwebtoken\n```\n\nUpdate `pages/api/auth/[...auth0].js` with the following:\n\n```jsx\n// pages/api/auth/[...auth0].js\n\nimport { handleAuth, handleCallback } from '@auth0/nextjs-auth0'\nimport jwt from 'jsonwebtoken'\n\nconst afterCallback = async (req, res, session) =\u003e {\n  const payload = {\n    userId: session.user.sub,\n    exp: Math.floor(Date.now() / 1000) + 60 * 60,\n  }\n\n  session.user.accessToken = jwt.sign(payload, process.env.SUPABASE_JWT_SECRET)\n\n  return session\n}\n\nexport default handleAuth({\n  async callback(req, res) {\n    try {\n      await handleCallback(req, res, { afterCallback })\n    } catch (error) {\n      res.status(error.status || 500).end(error.message)\n    }\n  },\n})\n```\n\nOur `payload` for the JWT will contain our user's unique identifier from Auth0 - `session.user.sub` and an expiry of 1 hour.\n\nWe are signing this JWT using Supabase's signing secret, so Supabase will be able to validate it is authentic and hasn't been tampered with in transit.\n\n\u003e Note: We need to sign the user out and back in again to run the `afterCallback` function, and create our new token.\n\nNow we just need to send the token along with the request to Supabase.\n\n## Step 8: Requesting data from Supabase\n\nCreate a new file called `utils/supabase.js` and add the following:\n\n```jsx\n// utils/supabase.js\n\nimport { createClient } from '@supabase/supabase-js'\n\nconst getSupabase = (access_token) =\u003e {\n  const options = {}\n\n  if (access_token) {\n    options.global = {\n      headers: {\n        Authorization: `Bearer ${access_token}`,\n      },\n    }\n  }\n\n  const supabase = createClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n    options\n  )\n\n  return supabase\n}\n\nexport { getSupabase }\n```\n\nThis will be our client for talking to Supabase. We can pass it an `access_token` and it will be attached to our request.\n\nLet's load our `todos` from Supabase in our landing page!\n\n```jsx\n// pages/index.js\n\nimport styles from '../styles/Home.module.css'\nimport { withPageAuthRequired } from '@auth0/nextjs-auth0'\nimport { getSupabase } from '../utils/supabase'\nimport Link from 'next/link'\nimport { useEffect } from 'react'\n\nconst Index = ({ user }) =\u003e {\n  const [todos, setTodos] = useState([])\n  const supabase = getSupabase(user.accessToken)\n\n  useEffect(() =\u003e {\n    const fetchTodos = async () =\u003e {\n      const { data } = await supabase.from('todo').select('*')\n      setTodos(data)\n    }\n\n    fetchTodos()\n  }, [])\n\n  return (\n    \u003cdiv className={styles.container}\u003e\n      \u003cp\u003e\n        Welcome {user.name}!{' '}\n        \u003cLink href=\"/api/auth/logout\"\u003e\n          \u003ca\u003eLogout\u003c/a\u003e\n        \u003c/Link\u003e\n      \u003c/p\u003e\n      {todos?.length \u003e 0 ? (\n        todos.map((todo) =\u003e \u003cp key={todo.id}\u003e{todo.content}\u003c/p\u003e)\n      ) : (\n        \u003cp\u003eYou have completed all todos!\u003c/p\u003e\n      )}\n    \u003c/div\u003e\n  )\n}\n\nexport const getServerSideProps = withPageAuthRequired()\n\nexport default Index\n```\n\nAlternatively, we could fetch todos on the server using the `getServerSideProps` function.\n\n```jsx\n// pages/index.js\n\nimport styles from '../styles/Home.module.css'\nimport { withPageAuthRequired, getSession } from '@auth0/nextjs-auth0'\nimport { getSupabase } from '../utils/supabase'\nimport Link from 'next/link'\n\nconst Index = ({ user, todos }) =\u003e {\n  return (\n    \u003cdiv className={styles.container}\u003e\n      \u003cp\u003e\n        Welcome {user.name}!{' '}\n        \u003cLink href=\"/api/auth/logout\"\u003e\n          \u003ca\u003eLogout\u003c/a\u003e\n        \u003c/Link\u003e\n      \u003c/p\u003e\n      {todos?.length \u003e 0 ? (\n        todos.map((todo) =\u003e \u003cp key={todo.id}\u003e{todo.content}\u003c/p\u003e)\n      ) : (\n        \u003cp\u003eYou have completed all todos!\u003c/p\u003e\n      )}\n    \u003c/div\u003e\n  )\n}\n\nexport const getServerSideProps = withPageAuthRequired({\n  async getServerSideProps({ req, res }) {\n    const {\n      user: { accessToken },\n    } = await getSession(req, res)\n\n    const supabase = getSupabase(accessToken)\n\n    const { data: todos } = await supabase.from('todo').select('*')\n\n    return {\n      props: { todos },\n    }\n  },\n})\n\nexport default Index\n```\n\nEither way, when we reload our application, we are still getting the empty state for todos.\n\n![Empty todo list](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/XgEMwnN.png)\n\nThis is because we enabled Row Level Security, which blocks all requests by default. To enable our user to select their `todos` we need to write a policy.\n\n## Step 9: Write a policy to allow select\n\nOur policy will need to know who our currently logged in user is to determine whether or not they should have access. Let's create a PostgreSQL function to extract the current user from our new JWT.\n\nNavigate back to the Supabase dashboard, select `SQL` from the sidebar menu, and click `New query`. This will create a new query called `new sql snippet`, which will allow us to run any SQL against our Postgres database.\n\nWrite the following and click `Run`.\n\n```sql\ncreate or replace function auth.user_id() returns text as $$\n  select nullif(current_setting('request.jwt.claims', true)::json-\u003e\u003e'userId', '')::text;\n$$ language sql stable;\n```\n\nThis will create a function called `auth.user_id()`, which will inspect the `userId` field of our JWT payload.\n\n\u003e Note: To learn more about PostgreSQL functions, check out [our deep dive video](https://www.youtube.com/watch?v=MJZCCpCYEqk).\n\nLet's create a policy that checks whether this user is the owner of the todo.\n\nSelect `Authentication` from the Supabase sidebar menu, click `Policies`, and then `New Policy` on the `todo` table.\n\n![Create new policy](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/M7XyhHe.png)\n\nFrom the modal, select `Create a policy from scratch` and add the following.\n\n![Policy settings for SELECT](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/wuWz3am.png)\n\nThis policy is calling the function we just created to get the currently logged in user's ID `auth.user_id()` and checking whether this matches the `user_id` column for the current `todo`. If it does, then it will allow the user to select it, otherwise it will continue to deny.\n\nClick `Review` and then `Save policy`.\n\n\u003e Note: To learn more about RLS and policies, check out [our deep dive video](https://www.youtube.com/watch?v=Ow_Uzedfohk).\n\nThe last thing we need to do is update the `user_id` columns for our existing `todos`.\n\nHead back to the Supabase dashboard, and select `Table editor` from the sidebar.\n\n![User ID null in Supabase Table Editor](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png)\n\nEach of our `user_id` columns are set to `NULL`!\n\nTo get the ID for our Auth0 user, head over to the Auth0 dashboard, select `User Management` from the sidebar, click `Users` and select your test user.\n\n![List of users in Auth0 dashboard](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/GdXS013.png)\n\nCopy their `user_id`.\n\n![User ID in Auth0 dashboard](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tbvd0Uj.png)\n\nUpdate each row in Supabase.\n\n![User ID set to Auth0 user](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tPu4Tt8.png)\n\nNow when we refresh our application, we should finally see our list of `todos`!\n\n\u003e Note: Check out [the repo](https://github.com/dijonmusters/supabase-auth0-example/blob/main/pages/index.js) for an example of writing new `todos` to Supabase.\n\n## Resources\n\n- [Auth0](https://auth0.com/) official website.\n- [Auth0 blog](https://auth0.com/blog/).\n- [Using Next.js and Auth0 with Supabase article](https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/).\n- [Auth0 community](https://community.auth0.com/).\n- [Auth0 documentation](https://auth0.com/docs/).\n","website":"https://auth0.com/","docs":"https://auth0.com/docs","contact":167,"approved":true,"created_at":"2023-07-19T19:12:34.77653+00:00","tsv":"'/)':169C,214C,1976C '/).':1998C '/api/auth/login':865C,959C '/api/auth/logout':867C '/blog/).':135C,1983C '/blog/using-nextjs-and-auth0-with-supabase/)':127C '/blog/using-nextjs-and-auth0-with-supabase/).':1993C '/dashboard/),':530C,583C '/dashboard/).':342C '/dijonmusters/supabase-auth0-example).':165C '/dijonmusters/supabase-auth0-example/blob/main/pages/index.js)':1962C '/docs/).':2003C '/docs/configure/applications/signing-algorithms).':1055C '/package/@auth0/nextjs-auth0)':258C '/signup).':324C '/storage/v1/object/public/images/integrations/auth0/documentation/33kqp4k.png)':633C '/storage/v1/object/public/images/integrations/auth0/documentation/anu4wez.png)':460C '/storage/v1/object/public/images/integrations/auth0/documentation/dlovhdq.png)':676C,1868C '/storage/v1/object/public/images/integrations/auth0/documentation/gdxs013.png)':1914C '/storage/v1/object/public/images/integrations/auth0/documentation/isa3e0j.png)':422C '/storage/v1/object/public/images/integrations/auth0/documentation/iyzhxew.png)':374C '/storage/v1/object/public/images/integrations/auth0/documentation/m7xyhhe.png)':1734C '/storage/v1/object/public/images/integrations/auth0/documentation/mehhawc.png)':660C '/storage/v1/object/public/images/integrations/auth0/documentation/o07faov.png)':779C '/storage/v1/object/public/images/integrations/auth0/documentation/qnmjeu7.png)':566C '/storage/v1/object/public/images/integrations/auth0/documentation/r1gaflo.png)':796C '/storage/v1/object/public/images/integrations/auth0/documentation/tbvd0uj.png)':1926C '/storage/v1/object/public/images/integrations/auth0/documentation/tpu4tt8.png)':1940C '/storage/v1/object/public/images/integrations/auth0/documentation/wuwz3am.png)':1754C '/storage/v1/object/public/images/integrations/auth0/documentation/xgemwnn.png)':1522C '/storage/v1/object/public/images/integrations/auth0/documentation/xlrl7s7.png)':992C '/storage/v1/object/public/images/integrations/auth0/documentation/ydbkry6.png)':1022C '/styles/home.module.css':925C,1344C,1430C '/utils/supabase':1352C,1439C '/watch?v=mjzccpcyeqk).':1695C '/watch?v=ow_uzedfohk).':1828C '0':1394C,1457C '1':344C,1184C '1000':1134C '2':424C '3':519C '3000':483C,713C,975C '3000/api/auth/callback':478C '4':568C '5':678C '500':1162C '6':815C '60':1135C,1136C '7':1024C '8':1251C '9':1552C 'abl':1012C,1200C 'access':1274C,1279C,1285C,1318C,1580C 'accesstoken':1479C,1487C 'account':318C,331C,999C 'add':665C,836C,1263C,1745C 'advanc':495C 'aftercallback':1084C,1122C,1157C,1229C 'algorithm':1052C 'allow':235C,474C,479C,1035C,1557C,1624C,1794C 'along':1244C 'also':248C,327C,1048C 'altern':1413C 'anon':747C,1297C 'any-secure-valu':705C 'anytim':1089C 'api':263C,788C,854C 'app':682C,693C,897C,905C 'applic':12B,27C,68C,84C,197C,218C,245C,305C,429C,435C,436C,440C,443C,450C,456C,467C,774C,1507C,1946C 'articl':128C,1990C 'async':1123C,1148C,1375C,1473C 'attach':1324C 'auth':2004 'auth.user':1645C,1668C,1773C 'auth0':1A,22C,51C,62C,86C,91C,122C,130C,149C,166C,246C,317C,347C,351C,361C,370C,417C,428C,455C,554C,703C,709C,714C,719C,725C,727C,733C,763C,775C,817C,833C,839C,885C,982C,986C,1026C,1034C,1066C,1082C,1103C,1110C,1178C,1885C,1891C,1910C,1922C,1936C,1973C,1979C,1987C,1994C,1999C,2005 'auth0.com':126C,134C,168C,323C,718C,1054C,1975C,1982C,1992C,2002C 'auth0.com/)':167C,1974C 'auth0.com/blog/).':133C,1981C 'auth0.com/blog/using-nextjs-and-auth0-with-supabase/)':125C 'auth0.com/blog/using-nextjs-and-auth0-with-supabase/).':1991C 'auth0.com/docs/).':2001C 'auth0.com/docs/configure/applications/signing-algorithms).':1053C 'auth0.com/signup).':322C 'auth0/nextjs-auth0':822C,827C,844C,929C,1116C,1348C,1435C 'auth0/nextjs-auth0/client':895C 'authent':4B,94C,172C,180C,254C,1092C,1205C,1713C 'author':6B,102C,174C,1283C 'automat':954C 'await':1153C,1378C,1480C,1491C 'b2b':70C 'b2c':71C 'b2e':73C 'back':1223C,1597C,1847C 'base':710C,716C 'bash':688C,810C,824C,1097C 'basic':770C 'bearer':1284C 'bit':1062C 'block':1532C 'blog':132C,1980C 'bool':616C 'bottom':487C 'build':81C,679C 'built':220C 'busi':20B 'call':1088C,1260C,1618C,1667C,1758C 'callback':475C,1149C 'case':75C 'catch':1158C 'check':136C,1687C,1702C,1776C,1820C,1956C 'classnam':939C,1387C,1450C 'click':353C,438C,453C,512C,531C,556C,584C,622C,644C,653C,661C,1001C,1609C,1638C,1719C,1806C,1899C 'client':720C,728C,1308C 'close':269C,394C 'code':154C 'column':605C,1783C,1841C,1874C 'communiti':1995C 'community.auth0.com':1997C 'community.auth0.com/).':1996C 'complet':614C,1403C,1466C 'compon':880C,898C,901C 'configur':90C 'confirm':507C 'conform':509C 'connect':25C 'const':896C,934C,945C,1121C,1127C,1272C,1276C,1287C,1361C,1364C,1368C,1373C,1376C,1407C,1444C,1470C,1477C,1484C,1488C 'contain':1171C 'continu':1002C,1803C 'control':189C 'copi':1915C 'core':19B 'could':291C,1415C 'coupl':667C 'creat':319C,335C,345C,365C,367C,439C,454C,520C,557C,569C,602C,625C,683C,691C,694C,828C,851C,1232C,1256C,1583C,1614C,1641C,1664C,1698C,1729C,1739C,1763C 'create-next-app':690C 'createcli':1269C,1289C 'current':964C,1030C,1567C,1590C,1652C,1767C,1786C 'custom':1038C 'dashboard':352C,371C,527C,580C,726C,734C,743C,753C,761C,1601C,1851C,1892C,1911C,1923C 'data':205C,570C,1253C,1377C,1383C,1489C 'databas':544C,1633C 'date.now':1133C 'deep':1690C,1823C 'default':619C,846C,904C,949C,1146C,1411C,1499C,1536C 'defin':35C 'demo':53C 'deni':1805C 'determin':1573C 'dev':813C 'develop':23C,402C,800C 'differ':1050C 'div':938C,1386C,1449C 'dive':1691C,1824C 'document':76C,2000C 'domain':377C 'easili':292C 'editor':586C,637C,1855C,1865C 'either':993C,1501C 'empti':1513C,1517C 'enabl':511C,597C,1527C,1538C 'end':310C,1163C 'ensur':909C 'enter':375C,534C,541C,590C,699C 'env.local':696C,809C 'environ':404C 'error':1159C 'error.message':1164C 'error.status':1161C 'exampl':155C,232C,1965C 'exist':1844C 'exp':1131C 'expiri':1182C 'export':845C,903C,944C,948C,1145C,1302C,1406C,1410C,1469C,1498C 'express':279C 'extens':65C 'extern':37C 'extra':669C 'extract':1060C,1588C 'fals':621C 'fastifi':282C 'fetch':1416C 'fetchtodo':1374C,1384C 'field':595C,651C,1675C 'file':697C,831C,1259C 'fill':647C 'final':1949C 'fine':187C 'fine-grain':186C 'focus':16B 'follow':473C,701C,1107C,1265C,1636C,1747C 'found':161C,767C,785C 'framework':219C,276C,299C 'front':309C 'front-end':308C 'full':153C 'function':266C,1085C,1230C,1424C,1586C,1644C,1666C,1686C,1760C 'generat':202C 'geograph':393C 'get':723C,731C,740C,750C,758C,1087C,1511C,1765C,1880C 'get-from-auth0-dashboard':722C,730C 'get-from-supabase-dashboard':739C,749C,757C 'getserversideprop':946C,1408C,1423C,1471C,1474C 'getsess':1433C,1481C 'getsupabas':1273C,1303C,1350C,1370C,1437C,1486C 'github.com':164C,1961C 'github.com/dijonmusters/supabase-auth0-example).':163C 'github.com/dijonmusters/supabase-auth0-example/blob/main/pages/index.js)':1960C 'give':54C,441C 'go':415C 'googl':1004C 'grain':188C 'guid':78C,113C,146C,158C,290C 'handl':93C,869C 'handleauth':842C,847C,1113C,1147C 'handlecallback':1114C,1154C 'hasn':1207C 'head':1846C,1887C 'header':1282C 'heavili':115C 'hour':1185C 'id':610C,721C,1646C,1669C,1772C,1774C,1782C,1840C,1860C,1873C,1882C,1918C,1920C,1933C 'ident':38C 'identifi':1176C 'import':841C,888C,892C,922C,926C,930C,1112C,1117C,1268C,1341C,1345C,1349C,1353C,1357C,1427C,1431C,1436C,1440C 'index':935C,950C,1362C,1412C,1445C,1500C 'inform':771C 'insert':645C,662C 'inspect':1672C 'inspir':116C 'instal':816C,820C,1093C 'integr':3B,43C,148C,253C 'issuer':715C 'js':834C,840C,1104C,1111C 'json':501C,1656C 'jsonwebtoken':1095C,1100C,1120C 'jsx':837C,886C,920C,1108C,1266C,1339C,1425C 'jwt':755C,1046C,1068C,1118C,1141C,1169C,1190C,1595C,1678C 'jwt.sign':1138C 'key':748C,1298C,1398C,1461C 'know':1564C 'koa':280C 'land':918C,1016C,1018C,1337C 'languag':31C,1659C 'last':1830C 'learn':1682C,1814C 'legaci':11B 'length':1393C,1456C 'let':1328C,1581C,1696C 'level':108C,599C,1529C 'librari':255C,819C,823C,1096C 'link':931C,1354C,1441C 'list':671C,1519C,1906C,1952C 'live':416C 'load':1330C 'localhost':477C,482C,712C,974C 'log':913C,965C,1568C,1768C 'logic':103C,242C,287C 'logo':362C 'logout':480C,943C,1391C,1454C 'main':859C 'major':397C 'manag':97C,182C,301C,1895C 'match':1779C 'math.floor':1132C 'menu':355C,433C,576C,1607C,1718C 'mobil':9B 'modal':1737C 'name':445C,536C,594C 'navig':464C,972C,1596C 'need':328C,383C,1058C,1064C,1216C,1239C,1546C,1562C,1833C 'neither':1031C 'new':532C,558C,560C,588C,604C,627C,655C,685C,806C,830C,998C,1234C,1258C,1594C,1610C,1616C,1619C,1723C,1730C,1968C 'next':692C,735C,744C 'next.js':83C,120C,211C,261C,268C,681C,686C,799C,818C,1985C 'next/link':933C,1356C,1443C 'nextjs.org':213C 'nextjs.org/)':212C 'node':274C 'note':111C,262C,762C,780C,848C,951C,1214C,1680C,1812C,1955C 'npm':811C,825C,1098C 'npx':689C 'null':1861C,1878C 'nullif':1651C 'numer':177C 'oauth':498C 'obuldanrptloktxcffvn.supabase.co':373C,421C,459C,565C,632C,659C,675C,778C,795C,991C,1021C,1521C,1733C,1753C,1867C,1913C,1925C,1939C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/33kqp4k.png)':631C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/anu4wez.png)':458C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dlovhdq.png)':674C,1866C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/gdxs013.png)':1912C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/isa3e0j.png)':420C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/iyzhxew.png)':372C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/m7xyhhe.png)':1732C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/mehhawc.png)':658C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/o07faov.png)':777C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/qnmjeu7.png)':564C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/r1gaflo.png)':794C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tbvd0uj.png)':1924C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tpu4tt8.png)':1938C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/wuwz3am.png)':1752C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/xgemwnn.png)':1520C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/xlrl7s7.png)':990C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/ydbkry6.png)':1020C 'offer':176C 'offici':1977C 'oidc':508C 'one':296C,320C,860C 'open':875C 'option':451C,1277C,1299C 'options.global':1281C 'otherwis':1800C 'overview':56C 'owner':1708C 'p':1397C,1460C 'page':919C,1017C,1019C,1338C 'pageprop':899C,902C 'pages/_app.js':876C,887C 'pages/api/auth':832C,838C,1102C,1109C 'pages/index.js':907C,921C,1340C,1426C 'pass':1315C 'password':545C 'payload':1128C,1139C,1166C,1679C 'platform':175C 'polici':110C,1550C,1555C,1560C,1700C,1720C,1724C,1731C,1741C,1748C,1756C,1811C,1819C 'postgr':1632C 'postgresql':1585C,1685C 'practic':141C 'process':59C 'process.env.next':1290C,1294C 'process.env.supabase':1140C 'product':52C,409C 'project':523C,533C,540C,559C,562C,687C,791C 'prop':1496C 'provid':39C,185C 'public':736C,745C,1291C,1295C 'queri':1611C,1617C 'rapid':2B 're':412C 'react':224C,889C,891C,1360C 'read':803C 'readi':413C 'redirect':955C,978C 'refactor':294C 'refresh':1944C 'region':389C,549C 'regular':448C 'reload':1505C 'replac':1643C 'repo':1959C 'req':1124C,1150C,1155C,1475C,1482C 'request':1247C,1252C,1327C,1534C 'request.jwt.claims':1654C 'res':1125C,1151C,1156C,1476C,1483C 'res.status':1160C 'resembl':270C 'resourc':1972C 'restart':797C 'return':900C,937C,1143C,1300C,1385C,1448C,1495C,1647C 'reveal':493C 'review':1807C 'right':358C 'rls':601C,1817C 'rout':264C,855C 'row':107C,598C,646C,656C,663C,1528C,1929C 'rs256':506C 'run':812C,1227C,1627C,1639C 'save':513C,623C,654C,1810C 'scratch':1743C 'screen':985C,989C 'scroll':484C 'secret':704C,729C,756C,1040C,1142C,1195C 'section':469C,491C 'secur':109C,543C,600C,707C,1530C 'see':1950C 'select':364C,387C,401C,434C,446C,461C,497C,546C,551C,596C,639C,1381C,1494C,1542C,1558C,1602C,1650C,1712C,1738C,1751C,1798C,1853C,1893C,1902C 'send':1074C,1241C 'separ':304C 'server':240C,275C,285C,801C,1420C 'server-sid':239C,284C 'serverless':265C 'session':1126C,1144C 'session.user.accesstoken':1137C 'session.user.sub':1130C,1179C 'set':419C,425C,457C,462C,490C,496C,500C,517C,563C,657C,769C,776C,787C,793C,1043C,1653C,1749C,1876C,1934C 'settodo':1366C,1382C 'short':50C 'side':241C,286C 'sidebar':432C,575C,1606C,1717C,1858C,1898C 'sign':193C,337C,870C,983C,987C,994C,1006C,1025C,1039C,1051C,1070C,1188C,1194C,1218C 'signatur':504C 'snippet':1621C 'specif':259C 'sql':1603C,1620C,1629C,1640C,1660C 'stabl':1661C 'stack':33C 'state':1514C 'step':79C,143C,145C,343C,423C,518C,567C,677C,814C,1023C,1250C,1551C 'step-by-step':142C 'still':1510C 'store':207C 'strategi':178C 'structur':272C 'style':923C,1342C,1428C 'styles.container':940C,1388C,1451C 'supabas':88C,105C,124C,151C,330C,522C,526C,539C,561C,572C,579C,737C,742C,746C,752C,754C,760C,781C,792C,1029C,1032C,1076C,1192C,1197C,1249C,1255C,1288C,1292C,1296C,1301C,1312C,1334C,1369C,1485C,1600C,1716C,1850C,1863C,1931C,1971C,1989C 'supabase.com':341C,529C,582C 'supabase.com/dashboard/),':528C,581C 'supabase.com/dashboard/).':340C 'supabase.from':1379C,1492C 'supabase/supabase-js':1271C 'tabl':585C,589C,628C,630C,636C,642C,1728C,1854C,1864C 'tag':405C 'talk':1310C 'tamper':1210C 'tenant':348C,366C,368C,380C,418C,555C 'test':967C,1904C 'text':608C,612C,1648C,1658C 'therefor':1056C 'thing':1831C 'titl':606C,650C 'todo':591C,629C,641C,670C,673C,1332C,1365C,1380C,1392C,1396C,1405C,1417C,1447C,1455C,1459C,1468C,1490C,1493C,1497C,1516C,1518C,1544C,1711C,1727C,1787C,1845C,1954C,1969C 'todo.content':1400C,1463C 'todo.id':1399C,1462C 'todos.map':1395C,1458C 'token':98C,199C,503C,1027C,1235C,1243C,1275C,1280C,1286C,1319C 'top':222C 'touch':60C 'transit':1213C 'tri':1152C 'true':1655C 'two':603C 'uniqu':386C,1175C 'unmatch':64C 'updat':471C,515C,906C,1101C,1837C,1927C 'upon':61C 'uri':468C 'url':476C,481C,711C,717C,738C,1293C 'us':236C,857C,1625C 'use':21C,48C,74C,106C,119C,228C,863C,1049C,1081C,1191C,1421C,1984C 'useeffect':1358C,1372C 'user':95C,183C,192C,210C,400C,609C,871C,911C,936C,957C,1091C,1173C,1220C,1363C,1446C,1478C,1540C,1570C,1591C,1705C,1770C,1781C,1796C,1839C,1859C,1872C,1886C,1894C,1900C,1905C,1908C,1917C,1919C,1932C,1937C 'user.accesstoken':1371C 'user.name':942C,1390C,1453C 'userid':1129C,1657C,1674C 'userprovid':883C,893C 'usest':1367C 'utils/supabase.js':1261C,1267C 'valid':1202C 'valu':620C,702C,708C,764C,782C,807C 'video':1692C,1825C 'view':638C,916C,1014C 'want':46C 'way':1502C 'web':8B,217C,449C,502C 'websit':1978C 'welcom':941C,1389C,1452C 'well':41C,252C 'whether':1574C,1703C,1777C 'within':243C 'withpageauthrequir':927C,947C,952C,1346C,1409C,1432C,1472C 'work':970C 'wrap':878C 'write':100C,238C,1548C,1553C,1634C,1967C 'written':28C,249C 'www.npmjs.com':257C 'www.npmjs.com/package/@auth0/nextjs-auth0)':256C 'www.youtube.com':1694C,1827C 'www.youtube.com/watch?v=mjzccpcyeqk).':1693C 'www.youtube.com/watch?v=ow_uzedfohk).':1826C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    blockquote: \"blockquote\",\n    a: \"a\",\n    code: \"code\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Using Auth0, developers can connect any application written in any language or stack, and define the external identity providers, as well as integrations, that they want to use. This short Auth0 product demo gives an overview of this process, touching upon Auth0’s unmatched extensibility and its applicability to B2B, B2C, and B2E use cases.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide steps through building a Next.js application with Auth0 and Supabase. We configure Auth0 to handle authenticating users and managing tokens, while writing our authorization logic in Supabase - using Row Level Security policies.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: This guide is heavily inspired by the \", _jsx(_components.a, {\n          href: \"https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/\",\n          children: \"Using Next.js and Auth0 with Supabase\"\n        }), \" article on \", _jsx(_components.a, {\n          href: \"https://auth0.com/blog/\",\n          children: \"Auth0's blog\"\n        }), \". Check it out for a practical step-by-step guide on integrating Auth0 and Supabase.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The full code example for this guide can be found \", _jsx(_components.a, {\n        href: \"https://github.com/dijonmusters/supabase-auth0-example\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://auth0.com/\",\n        children: \"Auth0\"\n      }), \" is an authentication and authorization platform, offering numerous strategies to authenticate and manage users. It provides fine-grain control over how users sign in to your application, the token that is generated, and what data is stored about your users.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://nextjs.org/\",\n        children: \"Next.js\"\n      }), \" is a web application framework built on top of React. We will be using it for this example, as it allows us to write server-side logic within our application. Auth0 have also written a \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/@auth0/nextjs-auth0\",\n        children: \"very well integrated authentication library\"\n      }), \" specifically for Next.js.\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"Note: API routes (serverless functions) in Next.js closely resemble the structure of Node server frameworks - such as Express, Koa and Fastify. The server-side logic in this guide could easily be refactored in one of these frameworks and managed as a separate application to the front-end.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you don’t have an Auth0 account, create one \", _jsx(_components.a, {\n        href: \"https://auth0.com/signup\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You will also need a Supabase account, which can be created by signing in \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Creating an Auth0 tenant\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the Auth0 dashboard, click the menu to the right of the Auth0 logo, and select \", _jsx(_components.code, {\n        children: \"Create tenant\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/IYzHxeW.png\",\n        alt: \"Create tenant from Auth0 dashboard\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a \", _jsx(_components.code, {\n        children: \"Domain\"\n      }), \" for your tenant - this will need to be unique.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select a \", _jsx(_components.code, {\n        children: \"Region\"\n      }), \" - this should be geographically close to the majority of your users.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Development\"\n      }), \" for \", _jsx(_components.code, {\n        children: \"Environment Tag\"\n      }), \" - this should be production when you're ready to go live.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/iSA3E0J.png\",\n        alt: \"Auth0 tenant settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Setting up an Auth0 application\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the sidebar menu, select \", _jsx(_components.code, {\n        children: \"Applications\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"Applications\"\n      }), \" and click \", _jsx(_components.code, {\n        children: \"Create Application\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Give your application a name, select the \", _jsx(_components.code, {\n        children: \"Regular Web Applications\"\n      }), \" option and click \", _jsx(_components.code, {\n        children: \"Create\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/ANU4Wez.png\",\n        alt: \"Auth0 application settings\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Settings\"\n      }), \" and navigate to the \", _jsx(_components.code, {\n        children: \"Application URIs\"\n      }), \" section, and update the following:\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"Allowed Callback URLs\"\n      }), \": \", _jsx(_components.code, {\n        children: \"http://localhost:3000/api/auth/callback\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"Allowed Logout URLs\"\n      }), \": \", _jsx(_components.code, {\n        children: \"http://localhost:3000\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Scroll to the bottom of the \", _jsx(_components.code, {\n        children: \"Settings\"\n      }), \" section and reveal the \", _jsx(_components.code, {\n        children: \"Advanced Settings\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"OAuth\"\n      }), \" and set \", _jsx(_components.code, {\n        children: \"JSON Web Token Signature\"\n      }), \" to \", _jsx(_components.code, {\n        children: \"RS256\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Confirm \", _jsx(_components.code, {\n        children: \"OIDC Conformant\"\n      }), \" is \", _jsx(_components.code, {\n        children: \"Enabled\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Save\"\n      }), \" to update the settings.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Creating a Supabase project\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \", click \", _jsx(_components.code, {\n        children: \"New project\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a \", _jsx(_components.code, {\n        children: \"Name\"\n      }), \" for your Supabase project.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a secure \", _jsx(_components.code, {\n        children: \"Database Password\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select the same \", _jsx(_components.code, {\n        children: \"Region\"\n      }), \" you selected for your Auth0 tenant.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Create new project\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/qnmJEU7.png\",\n        alt: \"New Supabase project settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Creating data in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the sidebar menu in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \", click \", _jsx(_components.code, {\n        children: \"Table editor\"\n      }), \", then \", _jsx(_components.code, {\n        children: \"New table\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter \", _jsx(_components.code, {\n        children: \"todo\"\n      }), \" as the \", _jsx(_components.code, {\n        children: \"Name\"\n      }), \" field.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Enable Row Level Security (RLS)\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Create two new columns:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"title\"\n        }), \" as \", _jsx(_components.code, {\n          children: \"text\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"user_id\"\n        }), \" as \", _jsx(_components.code, {\n          children: \"text\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"is_complete\"\n        }), \" as \", _jsx(_components.code, {\n          children: \"bool\"\n        }), \" with the default value \", _jsx(_components.code, {\n          children: \"false\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Save\"\n      }), \" to create the new table.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/33kqP4K.png\",\n        alt: \"Todo table\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the \", _jsx(_components.code, {\n        children: \"Table editor\"\n      }), \" view, select the \", _jsx(_components.code, {\n        children: \"todo\"\n      }), \" table and click \", _jsx(_components.code, {\n        children: \"Insert row\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Fill out the \", _jsx(_components.code, {\n        children: \"title\"\n      }), \" field and click \", _jsx(_components.code, {\n        children: \"Save\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/mEhHAWC.png\",\n        alt: \"New row settings\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Insert row\"\n      }), \" and add a couple of extra todos.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png\",\n        alt: \"List of todos\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 5: Building a Next.js app\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Create a new Next.js project:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npx \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"create-next-app \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"name-of-projec\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"t\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a \", _jsx(_components.code, {\n        children: \".env.local\"\n      }), \" file and enter the following values:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"AUTH0_SECRET=any-secure-value\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"AUTH0_BASE_URL=http://localhost:3000\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"AUTH0_ISSUER_BASE_URL=https://\u003cname-of-your-tenant\u003e.\u003cregion-you-selected\u003e.auth0.com\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"AUTH0_CLIENT_ID=get-from-auth0-dashboard\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"AUTH0_CLIENT_SECRET=get-from-auth0-dashboard\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL=get-from-supabase-dashboard\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"NEXT_PUBLIC_SUPABASE_ANON_KEY=get-from-supabase-dashboard\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"SUPABASE_JWT_SECRET=get-from-supabase-dashboard\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: Auth0 values can be found under \", _jsx(_components.code, {\n          children: \"Settings \u003e Basic Information\"\n        }), \" for your application.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/o07FaoV.png\",\n        alt: \"Auth0 settings\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: Supabase values can be found under \", _jsx(_components.code, {\n          children: \"Settings \u003e API\"\n        }), \" for your project.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/r1GAfLo.png\",\n        alt: \"Supabase settings\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Restart your Next.js development server to read in the new values from \", _jsx(_components.code, {\n        children: \".env.local\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"run dev\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 6: Install Auth0 Next.js library\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Install the \", _jsx(_components.code, {\n        children: \"@auth0/nextjs-auth0\"\n      }), \" library.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"i @auth0/nextjs-auth0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new file \", _jsx(_components.code, {\n        children: \"pages/api/auth/[...auth0].js\"\n      }), \" and add:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/api/auth/[...auth0].js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { handleAuth } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handleAuth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: This will create a few API routes for us. The main ones we will use are \", _jsx(_components.code, {\n          children: \"/api/auth/login\"\n        }), \" and \", _jsx(_components.code, {\n          children: \"/api/auth/logout\"\n        }), \" to handle signing users in and out.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Open \", _jsx(_components.code, {\n        children: \"pages/_app.js\"\n      }), \" and wrap our \", _jsx(_components.code, {\n        children: \"Component\"\n      }), \" with the \", _jsx(_components.code, {\n        children: \"UserProvider\"\n      }), \" from Auth0:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/_app.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" React \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { UserProvider } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0/client'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"App \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Component\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"pageProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"UserProvider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Component \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"{...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"pageProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"UserProvider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"App\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Update \", _jsx(_components.code, {\n        children: \"pages/index.js\"\n      }), \" to ensure the user is logged in to view the landing page.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { withPageAuthRequired } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/link'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.container\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Welcome \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user.name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"' '\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"href\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"/api/auth/logout\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eLogout\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"withPageAuthRequired\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: \", _jsx(_components.code, {\n          children: \"withPageAuthRequired\"\n        }), \" will automatically redirect the user to \", _jsx(_components.code, {\n          children: \"/api/auth/login\"\n        }), \" if they are not currently logged in.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Test this is working by navigating to \", _jsx(_components.code, {\n        children: \"http://localhost:3000\"\n      }), \" which should redirect you to an Auth0 sign in screen.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/xLRL7S7.png\",\n        alt: \"Auth0 sign in screen\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Either \", _jsx(_components.code, {\n        children: \"Sign up\"\n      }), \" for a new account, or click \", _jsx(_components.code, {\n        children: \"Continue with Google\"\n      }), \" to sign in.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You should now be able to view the landing page.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/YdBKRy6.png\",\n        alt: \"Landing page\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 7: Sign Auth0 token for Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Currently, neither Supabase or Auth0 allow for a custom signing secret to be set for their JWT. They also use different \", _jsx(_components.a, {\n        href: \"https://auth0.com/docs/configure/applications/signing-algorithms\",\n        children: \"signing algorithms\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Therefore, we need to extract the bits we need from Auth0's JWT, and sign our own to send to Supabase.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We can do that using Auth0's \", _jsx(_components.code, {\n        children: \"afterCallback\"\n      }), \" function, which gets called anytime the user authenticates.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Install the \", _jsx(_components.code, {\n        children: \"jsonwebtoken\"\n      }), \" library.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"i jsonwebtoken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Update \", _jsx(_components.code, {\n        children: \"pages/api/auth/[...auth0].js\"\n      }), \" with the following:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/api/auth/[...auth0].js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { handleAuth, handleCallback } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'jsonwebtoken'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"afterCallback \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"payload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    userId: session.user.sub,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    exp: Math.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"floor\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(Date.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"now\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"/ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"1000\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"+ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  session.user.accessToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"sign\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload, process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_JWT_SECRET\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handleAuth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  async \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"callback\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    try\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      await \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handleCallback\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res, { afterCallback })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"catch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"status\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error.status \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"|| \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"500\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"end\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error.message)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our \", _jsx(_components.code, {\n        children: \"payload\"\n      }), \" for the JWT will contain our user's unique identifier from Auth0 - \", _jsx(_components.code, {\n        children: \"session.user.sub\"\n      }), \" and an expiry of 1 hour.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We are signing this JWT using Supabase's signing secret, so Supabase will be able to validate it is authentic and hasn't been tampered with in transit.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: We need to sign the user out and back in again to run the \", _jsx(_components.code, {\n          children: \"afterCallback\"\n        }), \" function, and create our new token.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now we just need to send the token along with the request to Supabase.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 8: Requesting data from Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new file called \", _jsx(_components.code, {\n        children: \"utils/supabase.js\"\n      }), \" and add the following:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// utils/supabase.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"access_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"options \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (access_token) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    options.global \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      headers: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Authorization: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"`Bearer ${\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"access_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}`\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    options\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This will be our client for talking to Supabase. We can pass it an \", _jsx(_components.code, {\n        children: \"access_token\"\n      }), \" and it will be attached to our request.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Let's load our \", _jsx(_components.code, {\n        children: \"todos\"\n      }), \" from Supabase in our landing page!\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { withPageAuthRequired } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/link'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { useEffect } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"todos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setTodos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useState\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"([])\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(user.accessToken)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  useEffect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"fetchTodos \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" () \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'todo'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'*'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      setTodos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(data)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    fetchTodos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }, [])\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.container\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Welcome \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user.name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"' '\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"href\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"/api/auth/logout\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eLogout\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todos?.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        todos.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"((\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"todo\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todo.id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todo.content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      ) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eYou have completed all todos!\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"withPageAuthRequired\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Alternatively, we could fetch todos on the server using the \", _jsx(_components.code, {\n        children: \"getServerSideProps\"\n      }), \" function.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { withPageAuthRequired, getSession } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@auth0/nextjs-auth0'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/link'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"todos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.container\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Welcome \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user.name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"' '\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"href\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"/api/auth/logout\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eLogout\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todos?.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        todos.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"((\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"todo\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todo.id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"todo.content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      ) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eYou have completed all todos!\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"withPageAuthRequired\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  async \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"accessToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(accessToken)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"todos\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'todo'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'*'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      props: { todos },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Either way, when we reload our application, we are still getting the empty state for todos.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/XgEMwnN.png\",\n        alt: \"Empty todo list\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This is because we enabled Row Level Security, which blocks all requests by default. To enable our user to select their \", _jsx(_components.code, {\n        children: \"todos\"\n      }), \" we need to write a policy.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 9: Write a policy to allow select\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our policy will need to know who our currently logged in user is to determine whether or not they should have access. Let's create a PostgreSQL function to extract the current user from our new JWT.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Navigate back to the Supabase dashboard, select \", _jsx(_components.code, {\n        children: \"SQL\"\n      }), \" from the sidebar menu, and click \", _jsx(_components.code, {\n        children: \"New query\"\n      }), \". This will create a new query called \", _jsx(_components.code, {\n        children: \"new sql snippet\"\n      }), \", which will allow us to run any SQL against our Postgres database.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Write the following and click \", _jsx(_components.code, {\n        children: \"Run\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create or replace function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"auth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"returns text as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" $$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"nullif\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(current_setting(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'request.jwt.claims'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", true)::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json-\u003e\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'userId'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"''\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"$$ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"language sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stable;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This will create a function called \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \", which will inspect the \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \" field of our JWT payload.\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: To learn more about PostgreSQL functions, check out \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=MJZCCpCYEqk\",\n          children: \"our deep dive video\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's create a policy that checks whether this user is the owner of the todo.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Authentication\"\n      }), \" from the Supabase sidebar menu, click \", _jsx(_components.code, {\n        children: \"Policies\"\n      }), \", and then \", _jsx(_components.code, {\n        children: \"New Policy\"\n      }), \" on the \", _jsx(_components.code, {\n        children: \"todo\"\n      }), \" table.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/M7XyhHe.png\",\n        alt: \"Create new policy\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the modal, select \", _jsx(_components.code, {\n        children: \"Create a policy from scratch\"\n      }), \" and add the following.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/wuWz3am.png\",\n        alt: \"Policy settings for SELECT\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This policy is calling the function we just created to get the currently logged in user's ID \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \" and checking whether this matches the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column for the current \", _jsx(_components.code, {\n        children: \"todo\"\n      }), \". If it does, then it will allow the user to select it, otherwise it will continue to deny.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Review\"\n      }), \" and then \", _jsx(_components.code, {\n        children: \"Save policy\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: To learn more about RLS and policies, check out \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=Ow_Uzedfohk\",\n          children: \"our deep dive video\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The last thing we need to do is update the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" columns for our existing \", _jsx(_components.code, {\n        children: \"todos\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Head back to the Supabase dashboard, and select \", _jsx(_components.code, {\n        children: \"Table editor\"\n      }), \" from the sidebar.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/dLOvhdq.png\",\n        alt: \"User ID null in Supabase Table Editor\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Each of our \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" columns are set to \", _jsx(_components.code, {\n        children: \"NULL\"\n      }), \"!\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To get the ID for our Auth0 user, head over to the Auth0 dashboard, select \", _jsx(_components.code, {\n        children: \"User Management\"\n      }), \" from the sidebar, click \", _jsx(_components.code, {\n        children: \"Users\"\n      }), \" and select your test user.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/GdXS013.png\",\n        alt: \"List of users in Auth0 dashboard\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Copy their \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tbvd0Uj.png\",\n        alt: \"User ID in Auth0 dashboard\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Update each row in Supabase.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/auth0/documentation/tPu4Tt8.png\",\n        alt: \"User ID set to Auth0 user\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now when we refresh our application, we should finally see our list of \", _jsx(_components.code, {\n        children: \"todos\"\n      }), \"!\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: Check out \", _jsx(_components.a, {\n          href: \"https://github.com/dijonmusters/supabase-auth0-example/blob/main/pages/index.js\",\n          children: \"the repo\"\n        }), \" for an example of writing new \", _jsx(_components.code, {\n          children: \"todos\"\n        }), \" to Supabase.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://auth0.com/\",\n          children: \"Auth0\"\n        }), \" official website.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://auth0.com/blog/\",\n          children: \"Auth0 blog\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/\",\n          children: \"Using Next.js and Auth0 with Supabase article\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://community.auth0.com/\",\n          children: \"Auth0 community\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://auth0.com/docs/\",\n          children: \"Auth0 documentation\"\n        }), \".\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"auth0"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>