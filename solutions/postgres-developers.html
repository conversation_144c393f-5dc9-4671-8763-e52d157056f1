<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/og/supabase-og.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase for Postgres Developers</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Get started in seconds with a complete Postgres development platform" data-next-head=""/><meta property="og:title" content="Supabase for Postgres Developers" data-next-head=""/><meta property="og:description" content="Get started in seconds with a complete Postgres development platform" data-next-head=""/><meta property="og:url" content="https://supabase.com/solutions/postgres-developers" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=16&amp;q=100 16w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=32&amp;q=100 32w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=48&amp;q=100 48w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=64&amp;q=100 64w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=96&amp;q=100 96w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=128&amp;q=100 128w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=256&amp;q=100 256w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=384&amp;q=100 384w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=640&amp;q=100 640w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=750&amp;q=100 750w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=828&amp;q=100 828w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=1080&amp;q=100 1080w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=1200&amp;q=100 1200w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=1920&amp;q=100 1920w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=2048&amp;q=100 2048w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=3840&amp;q=100 3840w" imageSizes="100%" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=16&amp;q=100 16w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=32&amp;q=100 32w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=48&amp;q=100 48w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=64&amp;q=100 64w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=96&amp;q=100 96w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=128&amp;q=100 128w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=256&amp;q=100 256w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=384&amp;q=100 384w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=640&amp;q=100 640w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=750&amp;q=100 750w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=828&amp;q=100 828w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=1080&amp;q=100 1080w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=1200&amp;q=100 1200w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=1920&amp;q=100 1920w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=2048&amp;q=100 2048w, /_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=3840&amp;q=100 3840w" imageSizes="100%" data-next-head=""/><link rel="preload" href="../images/index/products/auth.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/auth-light.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/edge-functions-dark.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/edge-functions-light.svg" as="image" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/693.c16636466ee02b80.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3714.dc4ad7fea361268b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9038.6ba958f0b4341f77.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7416.fc232284fd9a30e8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8101.13e26d6e3f0cdb6f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1825.ae58eb66bb3dd32a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4411.0fd157ed82e50d29.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9918.1dbeebc3728233cd.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4427.b26ea872389ed424.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5610.c0ef96cfded700da.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7168.d03b059ebde0ad20.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5748.7a50054c9a9490b7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/631.227545a3e47b56a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7228-c12374c5a4df09a1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/449-e7d43ec1b4d8786b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9574-107ef30653f23d0d.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/solutions/postgres-developers-9e1df966454454f6.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen overflow-visible"><div class="absolute inset-0 z-20 h-full w-full pointer-events-none"><nav class="sticky z-30 flex items-center bg-background/90 w-full border-b backdrop-blur-sm pointer-events-auto top-[65px]"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !p-2 flex items-start md:hidden"><button data-size="tiny" type="button" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 h-[26px] w-full min-w-[200px] flex justify-between items-center py-2" id="radix-:R2hkla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed"> <span class="truncate">Postgres Devs</span> <div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 hidden md:flex gap-3 items-center"><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="ai-builders.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot h-4 w-4"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg><p>AI Builders</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="no-code.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pointer h-4 w-4"><path d="M22 14a8 8 0 0 1-8 8"></path><path d="M18 11v-1a2 2 0 0 0-2-2a2 2 0 0 0-2 2"></path><path d="M14 10V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1"></path><path d="M10 9.5V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v10"></path><path d="M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"></path></svg><p>No Code</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="beginners.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-puzzle h-4 w-4"><path d="M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z"></path></svg><p>Beginners</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="developers.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-xml h-4 w-4"><path d="m18 16 4-4-4-4"></path><path d="m6 8-4 4 4 4"></path><path d="m14.5 4-5 16"></path></svg><p>Developers</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b text-sm hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600 border-foreground-light text-foreground" href="postgres-developers.html"><svg width="26" height="25" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"><path d="M13.553 14.5434C14.5936 16.6636 16.7499 18.0071 19.1117 18.0073H25.1729C25.5138 18.0076 25.7898 18.2842 25.7898 18.6252C25.7896 18.9659 25.5137 19.2427 25.1729 19.243H22.3584C22.3941 19.3211 22.415 19.4075 22.4151 19.499C22.4151 22.1237 20.0255 23.8922 17.3294 23.8923C14.9326 23.8923 12.924 22.2346 12.3854 20.0036L12.3382 19.7854L12.3267 19.6605C12.3294 19.3734 12.5331 19.1177 12.826 19.0605C13.1604 18.9955 13.4843 19.2139 13.5498 19.5483C13.8955 21.3196 15.4573 22.6565 17.3294 22.6565C19.5686 22.6564 21.1804 21.2361 21.1804 19.499C21.1804 19.4074 21.2012 19.3212 21.237 19.243H19.1117C16.2786 19.2429 13.6923 17.6302 12.4441 15.0868L13.553 14.5434Z" fill="currentColor"></path><path d="M11.9396 0.59251H13.0442C18.2192 0.592582 22.4148 4.78736 22.4151 9.96235V15.3952C22.4151 15.7363 22.1383 16.013 21.7972 16.0131C21.4562 16.0129 21.1804 15.7362 21.1804 15.3952V9.96235C21.1801 5.46961 17.537 1.82728 13.0442 1.82721H11.8137C11.7855 1.8272 11.758 1.82249 11.7308 1.81881H5.92134C5.01109 1.81884 4.19788 2.38681 3.88414 3.24128L1.93402 8.55247C1.59885 9.46536 1.86315 10.4909 2.59804 11.1278L3.93554 12.287C4.51736 12.7912 4.85232 13.5238 4.85239 14.2938V16.4254C4.85236 18.283 6.35795 19.7885 8.21554 19.7885C8.79044 19.7884 9.25714 19.3228 9.25722 18.7479V9.25112C9.25724 7.29523 10.0377 5.41991 11.4266 4.04273C11.6688 3.80262 12.0603 3.80473 12.3004 4.04693C12.5404 4.28914 12.5383 4.67959 12.2962 4.91972C11.1413 6.06493 10.4919 7.62466 10.4919 9.25112V18.7479C10.4918 20.005 9.47269 21.0242 8.21554 21.0243C5.67569 21.0242 3.61661 18.9652 3.61664 16.4254V14.2938C3.61658 13.8821 3.43783 13.4902 3.12675 13.2206L1.7882 12.0614C0.665609 11.0884 0.261983 9.52225 0.7738 8.12762L2.72393 2.81538C3.21621 1.47459 4.49303 0.583094 5.92134 0.583069H11.9396V0.59251Z" fill="currentColor"></path><path d="M12.7263 14.2602C13.0325 14.11 13.4027 14.2372 13.553 14.5434L12.4441 15.0868C12.2941 14.7806 12.4202 14.4104 12.7263 14.2602Z" fill="currentColor"></path><path d="M17.5298 8.99516C18.0167 8.99532 18.4118 9.38945 18.412 9.87633C18.412 10.3634 18.0168 10.7584 17.5298 10.7586C17.0426 10.7586 16.6476 10.3635 16.6476 9.87633C16.6478 9.38935 17.0428 8.99516 17.5298 8.99516Z" fill="currentColor"></path></svg><p>Postgres Devs</p></a></div></nav></div><div class="h-[53px] not-sr-only"></div><div class="w-full max-w-full relative mx-auto py-16 lg:py-24 overflow-hidden [&amp;_h1]:2xl:!text-5xl bg-default border-0 lg:pb-8 [&amp;_.ph-footer]:mt-0 [&amp;_.ph-footer]:lg:mt-16 [&amp;_.ph-footer]:xl:mt-32"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 grid grid-cols-12 lg:gap-4"><div class="relative z-10 col-span-12 gap-8 lg:col-span-5"><div><div class="mb-4 flex items-center gap-3"><span class="text-brand-600 dark:text-brand font-mono uppercase">Supabase for Postgres Developers</span></div><h1 class="h1 text-3xl md:!text-4xl lg:!text-4xl 2xl:!text-6xl tracking-[-.15px]">Get started in seconds with a complete Postgres development platform</h1></div><div class="mb-4 md:mb-8"><p class="p lg:text-lg max-w-lg lg:max-w-none">Supabase is the open-source Postgres development platform. It includes a developer experience designed for speed, an integrated suite of backend services, and a scalable, trusted foundation for building powerful applications. But if all you want is Postgres, Supabase is that, too.</p></div><div class="flex flex-row md:flex-row md:items-center gap-2 mt-2"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div><div class="ph-footer relative z-10 mt-4 md:mt-8 lg:mt-20 xl:mt-32 col-span-12"><div class="pb-14 md:pb-24 mt-12"><div class="max-w-md md:max-w-lg lg:max-w-2xl mx-auto"><div class="relative w-full mx-auto max-w-4xl opacity-90 dark:opacity-70 overflow-hidden before:content[&#x27;&#x27;] before:absolute before:inset-0 before:w-full before:bg-[linear-gradient(to_right,hsl(var(--background-default))_0%,transparent_10%,transparent_90%,hsl(var(--background-default))_100%)] before:z-10 flex flex-nowrap px-5 lg:px-12 justify-center gap-4 lg:gap-8"><div class="gap-4 lg:gap-8 flex flex-nowrap w-fit animate-[marquee_90000ms_linear_both_infinite] will-change-transform motion-reduce:animate-none motion-reduce:will-change-none"><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mozilla.svg" alt="mozilla" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/github.svg" alt="github" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/1password.svg" alt="1password" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pwc.svg" alt="pwc" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pika.svg" alt="pika" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/humata.svg" alt="humata" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/udio.svg" alt="udio" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/langchain.svg" alt="langchain" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/resend.svg" alt="resend" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/loops.svg" alt="loops" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mobbin.svg" alt="mobbin" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/gopuff.svg" alt="gopuff" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/chatbase.svg" alt="chatbase" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/betashares.svg" alt="betashares" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/submagic.svg" alt="submagic" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div></div><div class="gap-4 lg:gap-8 flex flex-nowrap w-fit animate-[marquee_90000ms_linear_both_infinite] will-change-transform motion-reduce:animate-none motion-reduce:will-change-none"><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mozilla.svg" alt="mozilla" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/github.svg" alt="github" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/1password.svg" alt="1password" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pwc.svg" alt="pwc" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pika.svg" alt="pika" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/humata.svg" alt="humata" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/udio.svg" alt="udio" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/langchain.svg" alt="langchain" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/resend.svg" alt="resend" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/loops.svg" alt="loops" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mobbin.svg" alt="mobbin" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/gopuff.svg" alt="gopuff" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/chatbase.svg" alt="chatbase" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/betashares.svg" alt="betashares" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/submagic.svg" alt="submagic" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div></div><div class="gap-4 lg:gap-8 flex flex-nowrap w-fit animate-[marquee_90000ms_linear_both_infinite] will-change-transform motion-reduce:animate-none motion-reduce:will-change-none"><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mozilla.svg" alt="mozilla" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/github.svg" alt="github" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/1password.svg" alt="1password" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pwc.svg" alt="pwc" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pika.svg" alt="pika" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/humata.svg" alt="humata" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/udio.svg" alt="udio" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/langchain.svg" alt="langchain" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/resend.svg" alt="resend" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/loops.svg" alt="loops" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mobbin.svg" alt="mobbin" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/gopuff.svg" alt="gopuff" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/chatbase.svg" alt="chatbase" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/betashares.svg" alt="betashares" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/submagic.svg" alt="submagic" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div></div><div class="gap-4 lg:gap-8 flex flex-nowrap w-fit animate-[marquee_90000ms_linear_both_infinite] will-change-transform motion-reduce:animate-none motion-reduce:will-change-none"><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mozilla.svg" alt="mozilla" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/github.svg" alt="github" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/1password.svg" alt="1password" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pwc.svg" alt="pwc" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/pika.svg" alt="pika" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/humata.svg" alt="humata" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/udio.svg" alt="udio" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/langchain.svg" alt="langchain" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/resend.svg" alt="resend" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/loops.svg" alt="loops" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/mobbin.svg" alt="mobbin" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/gopuff.svg" alt="gopuff" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/chatbase.svg" alt="chatbase" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/betashares.svg" alt="betashares" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div><div class="h-12 lg:h-12 w-max !inline-block"><img src="../images/logos/publicity/submagic.svg" alt="submagic" class="h-12 lg:h-12 !min-h-12 lg:!min-h-12 w-auto block" draggable="false"/></div></div></div></div></div></div></div><div class="image-container relative min-h-[300px] col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"><figure class="absolute inset-0 z-0 group [&amp;&gt;span]:!w-[120%] [&amp;&gt;span]:!h-[120%] [&amp;&gt;span]:!inset-auto [&amp;&gt;span]:!-left-10 [&amp;&gt;span]:sm:!-left-20 [&amp;&gt;span]:lg:!-left-10 [&amp;&gt;span]:!-top-16 [&amp;&gt;span]:md:!-top-28 [&amp;&gt;span]:lg:!-top-52" role="img" aria-label="Supabase Postgres database visual composition"><span class="absolute group w-full md:w-auto h-full md:aspect-square flex items-end md:items-center justify-center md:justify-end right-0 left-0 md:left-auto xl:-right-12 2xl:right-0 top-12 md:top-0 md:bottom-0 my-auto"><img alt="Supabase Postgres database" draggable="false" decoding="async" data-nimg="fill" class="hidden dark:block absolute antialiased inset-0 object-contain object-center z-0 w-full md:w-auto h-full transition-opacity group-hover:opacity-80" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="../_next/database-dark.png 16w, ../_next/database-dark.png 32w, ../_next/database-dark.png 48w, ../_next/database-dark.png 64w, ../_next/database-dark.png 96w, ../_next/database-dark.png 128w, ../_next/database-dark.png 256w, ../_next/database-dark.png 384w, ../_next/database-dark.png 640w, ../_next/database-dark.png 750w, ../_next/database-dark.png 828w, https://supabase.com/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-dark.png&amp;w=1200&amp;q=100 1200w, ../_next/database-dark.png 1920w, ../_next/database-dark.png 2048w, ../_next/database-dark.png 3840w" src="../_next/database-dark.png"/><img alt="Supabase Postgres database" draggable="false" decoding="async" data-nimg="fill" class="dark:hidden absolute antialiased inset-0 object-contain object-center z-0 w-full md:w-auto h-full transition-opacity group-hover:opacity-80" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="../_next/database-light.png 16w, ../_next/database-light.png 32w, ../_next/database-light.png 48w, https://supabase.com/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=64&amp;q=100 64w, https://supabase.com/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=96&amp;q=100 96w, ../_next/database-light.png 128w, ../_next/database-light.png 256w, ../_next/database-light.png 384w, ../_next/database-light.png 640w, ../_next/database-light.png 750w, ../_next/database-light.png 828w, ../_next/database-light.png 1080w, https://supabase.com/_next/image?url=%2Fimages%2Findex%2Fproducts%2Fdatabase-light.png&amp;w=1200&amp;q=100 1200w, ../_next/database-light.png 1920w, ../_next/database-light.png 2048w, ../_next/database-light.png 3840w" src="../_next/database-light.png"/><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 390 430" class="absolute w-full h-full z-10 m-auto will-change-transform opacity-0 transition-opacity group-hover:opacity-100"><g stroke="hsl(var(--foreground-lighter))" filter="url(#filter5_bd_467_4905)"><path d="M192.144 125.816h-53.465c-8.506 0-16.159 5.17-19.334 13.061L99.0045 189.43c-3.0613 7.608-1.3448 16.306 4.3775 22.181l10.232 10.506c4.792 4.919 7.474 11.516 7.474 18.384l-.001 14.473c0 20.197 16.373 36.569 36.569 36.569 6.16 0 11.154-4.993 11.154-11.153l.001-86.241c0-18.629 7.441-36.486 20.668-49.602 2.746-2.723 7.178-2.704 9.9.041 2.722 2.745 2.703 7.178-.042 9.9-10.577 10.488-16.526 24.766-16.526 39.661l-.001 86.241c0 13.892-11.262 25.153-25.154 25.153-27.928 0-50.569-22.64-50.569-50.569l.001-14.474c0-3.218-1.257-6.309-3.503-8.615L93.353 221.38c-9.5904-9.847-12.4673-24.424-7.3366-37.176l20.3406-50.553c5.308-13.192 18.101-21.835 32.322-21.835h55.729v.084h10.339c49.104 0 88.91 39.806 88.91 88.91v50.842c0 3.866-3.134 7-7 7s-7-3.134-7-7V200.81c0-41.372-33.538-74.91-74.91-74.91H193.23c-.37 0-.732-.029-1.086-.084Z"></path><path d="M210.03 283.94c0-3.866-3.134-7-7-7s-7 3.134-7 7v3.113c0 26.959 21.854 48.814 48.813 48.814 26.351 0 47.825-20.879 48.781-46.996h24.614c3.866 0 7-3.134 7-7s-3.134-7-7-7h-26.841c-30.744 0-60.256-12.083-82.173-33.643-2.756-2.711-7.188-2.675-9.899.081-2.711 2.756-2.675 7.188.081 9.9 21.725 21.371 50.116 34.423 80.228 37.134-.679 18.629-15.995 33.524-34.791 33.524-19.227 0-34.813-15.587-34.813-34.814v-3.113ZM238.03 202.145c0 4.792 3.885 8.677 8.677 8.677s8.676-3.885 8.676-8.677-3.884-8.676-8.676-8.676-8.677 3.884-8.677 8.676Z"></path></g><path stroke="url(#a)" d="M192.144 125.816h-53.465c-8.506 0-16.159 5.17-19.334 13.061L99.0045 189.43c-3.0613 7.608-1.3448 16.306 4.3775 22.181l10.232 10.506c4.792 4.919 7.474 11.516 7.474 18.384l-.001 14.473c0 20.197 16.373 36.569 36.569 36.569 6.16 0 11.154-4.993 11.154-11.153l.001-86.241c0-18.629 7.441-36.486 20.668-49.602 2.746-2.723 7.178-2.704 9.9.041 2.722 2.745 2.703 7.178-.042 9.9-10.577 10.488-16.526 24.766-16.526 39.661l-.001 86.241c0 13.892-11.262 25.153-25.154 25.153-27.928 0-50.569-22.64-50.569-50.569l.001-14.474c0-3.218-1.257-6.309-3.503-8.615L93.353 221.38c-9.5904-9.847-12.4673-24.424-7.3366-37.176l20.3406-50.553c5.308-13.192 18.101-21.835 32.322-21.835h55.729v.084h10.339c49.104 0 88.91 39.806 88.91 88.91v50.842c0 3.866-3.134 7-7 7s-7-3.134-7-7V200.81c0-41.372-33.538-74.91-74.91-74.91H193.23c-.37 0-.732-.029-1.086-.084Z"></path><path stroke="url(#b)" d="M210.03 283.94c0-3.866-3.134-7-7-7s-7 3.134-7 7v3.113c0 26.959 21.854 48.814 48.813 48.814 26.351 0 47.825-20.879 48.781-46.996h24.614c3.866 0 7-3.134 7-7s-3.134-7-7-7h-26.841c-30.744 0-60.256-12.083-82.173-33.643-2.756-2.711-7.188-2.675-9.899.081-2.711 2.756-2.675 7.188.081 9.9 21.725 21.371 50.116 34.423 80.228 37.134-.679 18.629-15.995 33.524-34.791 33.524-19.227 0-34.813-15.587-34.813-34.814v-3.113Z"></path><path stroke="url(#c)" d="M238.03 202.145c0 4.792 3.885 8.677 8.677 8.677s8.676-3.885 8.676-8.677-3.884-8.676-8.676-8.676-8.677 3.884-8.677 8.676Z"></path><defs><radialGradient id="a" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0) rotate(45) scale(166 180)"><stop stop-color="hsl(var(--brand-default))"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))"></stop></radialGradient><radialGradient id="b" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0) rotate(45) scale(166 180)"><stop stop-color="hsl(var(--brand-default))"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))"></stop></radialGradient><radialGradient id="c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0) rotate(45) scale(166 180)"><stop stop-color="hsl(var(--brand-default))"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))"></stop></radialGradient></defs></svg></span></figure></div></div></div><div id="why-supabase" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 md:gap-8"><div class="flex flex-col gap-2 max-w-xl"><span class="label"></span><h2 class="h2 text-foreground-lighter">Why <span class="text-foreground">Postrges developers</span> choose Supabase</h2><p class="text-foreground-lighter mb-8">Supabase provides full access to Postgres. Use additional components, or just use the database. No proprietary tooling, no vendor lock-in. Supabase is Postgres the way you want it.</p></div><ul class="grid grid-cols-1 gap-4 gap-y-10 md:grid-cols-3 md:gap-12 xl:gap-20"><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg width="23" height="23" viewBox="0 0 40 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-1 mb-2 text-current w-7 h-7"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.43881 3.75378C4.10721 1.93324 5.84055 0.723145 7.77992 0.723145H15.6033V0.734736H17.0394C23.8756 0.734736 29.4173 6.27652 29.4173 13.1127V20.1749C29.4173 20.7272 28.9696 21.1749 28.4173 21.1749C27.8651 21.1749 27.4173 20.7272 27.4173 20.1749V13.1127C27.4173 7.38109 22.771 2.73474 17.0394 2.73474H15.4396C15.3877 2.73474 15.3366 2.73078 15.2868 2.72314H7.77992C6.6793 2.72314 5.6956 3.40989 5.31627 4.44308L2.7812 11.3479C2.37375 12.4577 2.69516 13.7038 3.58855 14.4781L5.32807 15.9856C6.12772 16.6786 6.58711 17.6847 6.58709 18.7428L6.58706 21.5134C6.58702 23.8192 8.45627 25.6885 10.7621 25.6885C11.4007 25.6885 11.9184 25.1708 11.9184 24.5322L11.9185 12.1874C11.9185 9.59233 12.955 7.10481 14.7977 5.27761C15.1899 4.88873 15.823 4.8914 16.2119 5.28357C16.6008 5.67574 16.5981 6.3089 16.2059 6.69777C14.742 8.14943 13.9185 10.1257 13.9185 12.1874L13.9184 24.5323C13.9184 26.2754 12.5053 27.6885 10.7621 27.6885C7.35169 27.6885 4.58701 24.9238 4.58706 21.5134L4.58709 18.7428C4.5871 18.2647 4.37953 17.8101 4.01822 17.497L2.27871 15.9894C0.757203 14.6708 0.209829 12.5486 0.90374 10.6586L3.43881 3.75378ZM16.539 18.5225C17.0348 18.2791 17.634 18.4838 17.8773 18.9796C19.1969 21.6686 21.9313 23.3727 24.9267 23.3726L32.8043 23.3726C33.3566 23.3725 33.8043 23.8203 33.8043 24.3725C33.8044 24.9248 33.3566 25.3725 32.8044 25.3726L29.4081 25.3726C29.4142 25.4172 29.4173 25.4628 29.4173 25.5091C29.4173 29.0627 26.1868 31.4165 22.6091 31.4165C19.2966 31.4165 16.5385 29.0518 15.9271 25.9188C15.8213 25.3767 16.175 24.8516 16.717 24.7458C17.2591 24.64 17.7843 24.9936 17.89 25.5357C18.3217 27.7475 20.2716 29.4165 22.6091 29.4165C25.447 29.4165 27.4173 27.6256 27.4173 25.5091C27.4173 25.4628 27.4205 25.4172 27.4266 25.3726L24.9267 25.3726C21.1684 25.3727 17.7375 23.2346 16.0818 19.8607C15.8385 19.3649 16.0432 18.7658 16.539 18.5225Z" fill="currentColor"></path><path d="M21.7224 13.0006C21.7224 13.6338 22.2358 14.1472 22.869 14.1472C23.5022 14.1472 24.0156 13.6338 24.0156 13.0006C24.0156 12.3674 23.5022 11.854 22.869 11.854C22.2358 11.854 21.7224 12.3674 21.7224 13.0006Z" fill="currentColor"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Full Postgres</h4><p class="text-foreground-lighter text-sm">Connect to Supabase via sql, pgAdmin, or any standard Postgres client. Use native Postgres extensions. Employ Row-Level Security, not a bolt-on permission system.</p></li><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="stroke-1 mb-2 text-current w-7 h-7"><path stroke-linecap="round" stroke-linejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Complete development platform</h4><p class="text-foreground-lighter text-sm">Supabase offers a fully integrated suite of tools including authentication, storage, edge functions, real-time subscriptions, and vector search. Developers don’t have to stitch together multiple services. Use one or all.</p></li><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-timer stroke-1 mb-2 text-current w-7 h-7"><line x1="10" x2="14" y1="2" y2="2"></line><line x1="12" x2="15" y1="14" y2="11"></line><circle cx="12" cy="14" r="8"></circle></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Scalable and dependable</h4><p class="text-foreground-lighter text-sm">Keep your workflows, tools, and database expertise. Self-host if you need it. Full deployment if you prefer, including automated backups, Point-in-Time Recovery (PITR), and fine-tuned observability via query logs, pg_stat_activity, and EXPLAIN ANALYZE.</p></li></ul></div><div id="postgres-platform" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-12 py-16 md:py-24 [&amp;_div.grid&gt;div:first-child]:sm:!border-l-0 [&amp;_div.grid&gt;div]:sm:-m-px [&amp;_div.grid&gt;div:nth-child(2n+1)]:sm:!border-l [&amp;_div.grid&gt;div:nth-child(2n+2)]:sm:!border-t [&amp;_div.grid&gt;div:nth-child(3n+3)]:lg:!border-l-0 [&amp;_div.grid&gt;div:nth-child(2)]:lg:!border-t-0"><div class="flex flex-col gap-4 max-w-lg"><h2 class="text-2xl md:text-3xl text-foreground-lighter font-normal">Supabase is the <span class="text-foreground">Postgres platform</span> you control</h2><p class="text-foreground-lighter text-base md:text-lg">Supabase includes everything you need to create the perfect app for your brand, business, or just for fun.</p></div><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-0 sm:divide-x divide-y rounded-md border border-default overflow-hidden"><div class="relative overflow-hidden flex-1 flex items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground sm:col-span-2 flex-col px-4 lg:pr-0 lg:flex-row"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:h-full ml-0 md:justify-start md:text-left md:items-start lg:max-w-[47%]"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.18625 8.66531H19.5035V15.331H5.18625V8.66531Z M4 17.0007C4 16.0804 4.7461 15.3343 5.66645 15.3343H18.9984C19.9187 15.3343 20.6648 16.0804 20.6648 17.0007V20.3335C20.6648 21.2539 19.9187 22 18.9984 22H5.66646C4.7461 22 4 21.2539 4 20.3335V17.0007Z M4 3.66646C4 2.7461 4.7461 2 5.66645 2H18.9984C19.9187 2 20.6648 2.7461 20.6648 3.66645V6.99926C20.6648 7.91962 19.9187 8.66572 18.9984 8.66572H5.66646C4.7461 8.66572 4 7.91962 4 6.99926V3.66646Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Database</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">A fully managed Postgres database.</span><br/> No forks: 100% pure Postgres.</p><span class="hidden lg:block text-foreground md:block"><ul class="hidden lg:flex flex-col gap-1 text-sm"><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> 100% portable</li><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> Built-in Auth with RLS</li><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> Easy to extend</li></ul></span></div></div><div class="relative w-full max-w-xl pt-8"><div class="w-full h-full rounded-t-lg lg:rounded-tr-none overflow-hidden border-t border-l border-r lg:border-r-0 bg-surface-75"><table class="min-w-full m-0"><thead class="p-0"><tr class="border-b"><th class="py-2 px-4 text-left text-xs font-mono font-normal tracking-widest text-[#A0A0A0]">NAME</th><th class="py-2 px-4 text-left text-xs font-mono font-normal tracking-widest text-[#A0A0A0]">PUBLICATION</th></tr></thead><tbody class="bg-surface-100"><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Jon Meyers</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Chris Martin</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Amy Quek</td><td class="py-2 px-4 whitespace-nowrap">No</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Riccardo Bussetti</td><td class="py-2 px-4 whitespace-nowrap">No</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Beng Eu</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Tyler Hillery</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr></tbody></table></div><div class=" absolute pointer-events-none w-full h-full inset-0 top-auto bg-[linear-gradient(to_bottom,transparent_0%,hsl(var(--background-default))_100%)] "></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l sm:!border-t-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.03305 15.8071H12.7252M5.03305 15.8071V18.884H12.7252V15.8071M5.03305 15.8071V12.7302H12.7252V15.8071M15.0419 8.15385V5.07692C15.0419 3.37759 13.6643 2 11.965 2C10.2657 2 8.88814 3.37759 8.88814 5.07692V8.15385M5 11.2307L5 18.9231C5 20.6224 6.37757 22 8.07689 22H15.769C17.4683 22 18.8459 20.6224 18.8459 18.9231V11.2307C18.8459 9.53142 17.4683 8.15385 15.769 8.15385L8.07689 8.15385C6.37757 8.15385 5 9.53142 5 11.2307Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Authentication</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">Secure authentication</span> with email/password, magic links, OAuth (Google, GitHub, Twitter, etc.), SAML, SSO, and phone/SMS OTP.</p></div></div><figure class="group absolute inset-0 z-0 -top-16 xl:top-0 xl:bottom-0 2xl:!-bottom-20" role="img" aria-label="Supabase Authentication provides Row Level Security which enables you to define custom Policies to restrict access to your database"><img draggable="false" alt="Supabase Authentication user db rows" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center xl:object-bottom" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth.svg"/><img draggable="false" alt="Supabase Authentication user db rows" aria-hidden="true" loading="lazy" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center xl:object-bottom opacity-0 group-hover:opacity-100 transition-opacity" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-active.svg"/><img draggable="false" alt="Supabase Authentication user db rows" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center xl:object-bottom" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-light.svg"/><img draggable="false" alt="Supabase Authentication user db rows" aria-hidden="true" loading="lazy" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center xl:object-bottom opacity-0 group-hover:opacity-100 transition-opacity" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-active-light.svg"/></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.6874 22.888V20.3886C17.6874 17.5888 15.4178 15.3192 12.618 15.3192C9.8182 15.3192 7.54852 17.5888 7.54852 20.3886V22.888M21.5531 11.5235C21.8189 14.1669 20.9393 16.9038 18.9141 18.9289C18.5359 19.3072 18.1328 19.6455 17.7101 19.9438M20.8038 8.70448C20.3598 7.71036 19.7299 6.77911 18.9141 5.96334C15.3338 2.38299 9.52889 2.38299 5.94855 5.96334C4.17501 7.73687 3.28 10.0562 3.26352 12.3807M24.0875 13.1161L23.2046 12.2332C22.3264 11.355 20.9026 11.355 20.0244 12.2332L19.1415 13.1161M0.875198 10.9503L1.75809 11.8331C2.63629 12.7113 4.06012 12.7113 4.93832 11.8331L5.82121 10.9503M7.49904 20.4919C5.77226 19.4557 4.37848 17.8555 3.62143 15.8584M15.6799 12.1942C15.6799 13.9201 14.2808 15.3192 12.5549 15.3192C10.829 15.3192 9.42993 13.9201 9.42993 12.1942C9.42993 10.4683 10.829 9.06917 12.5549 9.06917C14.2808 9.06917 15.6799 10.4683 15.6799 12.1942Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Role-Based Access Control</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Secure your data properly.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.15928 1.94531V5.84117M6.24345 5.84117L2.91385 2.40977M6.24345 8.53673H2.4248M16.7998 16.496L21.9988 15.2019C22.7217 15.022 22.8065 14.0285 22.1246 13.7286L9.73411 8.28034C9.08269 7.99391 8.41873 8.65652 8.70383 9.30851L14.0544 21.5445C14.3518 22.2247 15.341 22.1456 15.5266 21.4269L16.7998 16.496Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Realtime</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Postgres replication enables<!-- --> <span class="text-foreground">live sync functionality</span> for collaborative applications.</p></div></div><figure class="absolute inset-0 xl:-bottom-2 2xl:bottom-0 z-0 w-full overflow-hidden pointer-events-auto [&amp;_.visual-overlay]:bg-[linear-gradient(to_top,transparent_0%,transparent_50%,hsl(var(--background-default))_75%)]" role="img" aria-label="Supabase Realtime multiplayer app demo"><img alt="Supabase Realtime" loading="lazy" decoding="async" data-nimg="fill" class="hidden dark:block absolute object-cover xl:object-center inset-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/realtime-dark.svg"/><img alt="Supabase Realtime" loading="lazy" decoding="async" data-nimg="fill" class="dark:hidden absolute object-cover xl:object-center inset-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/realtime-light.svg"/><div class="absolute will-change-transform" style="position:absolute;top:60%;left:30%;transform:translate(0px, 0px) translate(-50%, -50%);transition:transform 0.75s ease-out"><svg width="30" height="38" viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.58385 1.69742C2.57836 0.865603 1.05859 1.58076 1.05859 2.88572V35.6296C1.05859 37.1049 2.93111 37.7381 3.8265 36.5656L12.5863 25.0943C12.6889 24.96 12.8483 24.8812 13.0173 24.8812H27.3245C28.7697 24.8812 29.4211 23.0719 28.3076 22.1507L3.58385 1.69742Z" fill="hsl(var(--background-surface-200))" stroke="hsl(var(--foreground-lighter))" stroke-linejoin="round"></path></svg><div class="!w-[66.70px] !h-[33.35px] absolute left-full flex items-center justify-center gap-1 -top-6 border border-foreground-lighter/70 rounded-full bg-surface-100"><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class="absolute will-change-transform scale-[80%]" style="position:absolute;top:80%;left:65%;transform:translate(0px, 0px) translate(-50%, -50%);transition:transform 1s ease-out"><svg width="20" height="28" viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.58385 1.69742C2.57836 0.865603 1.05859 1.58076 1.05859 2.88572V35.6296C1.05859 37.1049 2.93111 37.7381 3.8265 36.5656L12.5863 25.0943C12.6889 24.96 12.8483 24.8812 13.0173 24.8812H27.3245C28.7697 24.8812 29.4211 23.0719 28.3076 22.1507L3.58385 1.69742Z" fill="hsl(var(--background-surface-200))" stroke="hsl(var(--foreground-lighter))" stroke-linejoin="round"></path></svg><div class="!w-[55px] !h-[28px] absolute left-full flex items-center justify-center gap-1 -top-6 border border-foreground-muted rounded-full bg-surface-100 opacity-0 group-hover:opacity-100 transition-opacity"><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class="absolute will-change-transform w-1 h-1 opacity-0 motion-safe:group-hover:opacity-100 delay-0 duration-75 group-hover:duration-300 transition-opacity" style="position:absolute;top:0;left:0;transform:translate(0px, 0px) translate(-50%, -50%)"><div class="w-auto h-auto px-2.5 py-1.5 absolute left-full flex items-center justify-center gap-1 -top-6 border border-brand rounded-full bg-brand-300"><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class=" visual-overlay absolute pointer-events-none w-full h-full max-h-[400px] lg:max-h-none inset-0 top-auto bg-[linear-gradient(to_top,transparent_0%,transparent_50%,hsl(var(--background-surface-75))_85%)] "></div></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 lg:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.4997 12.1386V9.15811L14.8463 3.53163H6.43717C5.57423 3.53163 4.87467 4.23119 4.87467 5.09413V9.78087M20.4447 9.13199L14.844 3.53125L14.844 7.56949C14.844 8.43243 15.5436 9.13199 16.4065 9.13199L20.4447 9.13199ZM7.12729 9.78087H4.83398C3.97104 9.78087 3.27148 10.4804 3.27148 11.3434V19.1559C3.27148 20.8818 4.67059 22.2809 6.39648 22.2809H18.8965C20.6224 22.2809 22.0215 20.8818 22.0215 19.1559V13.7011C22.0215 12.8381 21.3219 12.1386 20.459 12.1386H10.8032C10.3933 12.1386 9.99969 11.9774 9.70743 11.6899L8.22312 10.2296C7.93086 9.94202 7.53729 9.78087 7.12729 9.78087Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Storage</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">Scalable S3-compatible</span> object storage for managing files, images, and videos.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l lg:!border-l-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.6594 21.8201C8.10788 22.5739 9.75418 23 11.5 23C17.299 23 22 18.299 22 12.5C22 10.7494 21.5716 9.09889 20.8139 7.64754M16.4016 3.21191C14.9384 2.43814 13.2704 2 11.5 2C5.70101 2 1 6.70101 1 12.5C1 14.287 1.44643 15.9698 2.23384 17.4428M2.23384 17.4428C1.81058 17.96 1.55664 18.6211 1.55664 19.3416C1.55664 20.9984 2.89979 22.3416 4.55664 22.3416C6.21349 22.3416 7.55664 20.9984 7.55664 19.3416C7.55664 17.6847 6.21349 16.3416 4.55664 16.3416C3.62021 16.3416 2.78399 16.7706 2.23384 17.4428ZM21.5 5.64783C21.5 7.30468 20.1569 8.64783 18.5 8.64783C16.8432 8.64783 15.5 7.30468 15.5 5.64783C15.5 3.99097 16.8432 2.64783 18.5 2.64783C20.1569 2.64783 21.5 3.99097 21.5 5.64783ZM18.25 12.5C18.25 16.2279 15.2279 19.25 11.5 19.25C7.77208 19.25 4.75 16.2279 4.75 12.5C4.75 8.77208 7.77208 5.75 11.5 5.75C15.2279 5.75 18.25 8.77208 18.25 12.5Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Edge Functions</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Serverless functions <span class="text-foreground">powered by Deno</span>, deployed globally for low-latency execution.</p></div></div><figure class="absolute inset-0 z-20" role="img" aria-label="Supabase Edge Functions visual composition"><img alt="Supabase Edge Functions globe" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/edge-functions-dark.svg"/><img alt="Supabase Edge Functions globe" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/edge-functions-light.svg"/><div class=" absolute inset-0 top-[48%] xl:top-[45%] w-full max-w-[200px] h-fit mx-auto px-2.5 py-1.5 flex items-center justify-start rounded-full bg-surface-100 border border-strong text-xs text-foreground-lighter text-left "><span class="mr-2">$</span>supabase<span class="ml-1 text-brand inline-block">functions <span>deploy</span></span></div></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 lg:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.13477 12.8129C4.13477 14.1481 4.43245 15.4138 4.96506 16.5471M12.925 4.02271C11.5644 4.02271 10.276 4.33184 9.12614 4.88371M21.7152 12.8129C21.7152 11.4644 21.4115 10.1867 20.8688 9.0447M12.925 21.6032C14.2829 21.6032 15.5689 21.2952 16.717 20.7454M16.717 20.7454C17.2587 21.5257 18.1612 22.0366 19.1831 22.0366C20.84 22.0366 22.1831 20.6935 22.1831 19.0366C22.1831 17.3798 20.84 16.0366 19.1831 16.0366C17.5263 16.0366 16.1831 17.3798 16.1831 19.0366C16.1831 19.6716 16.3804 20.2605 16.717 20.7454ZM4.96506 16.5471C4.16552 17.086 3.63965 17.9999 3.63965 19.0366C3.63965 20.6935 4.98279 22.0366 6.63965 22.0366C8.2965 22.0366 9.63965 20.6935 9.63965 19.0366C9.63965 17.3798 8.2965 16.0366 6.63965 16.0366C6.01951 16.0366 5.44333 16.2248 4.96506 16.5471ZM9.12614 4.88371C8.58687 4.08666 7.67444 3.56274 6.63965 3.56274C4.98279 3.56274 3.63965 4.90589 3.63965 6.56274C3.63965 8.2196 4.98279 9.56274 6.63965 9.56274C8.2965 9.56274 9.63965 8.2196 9.63965 6.56274C9.63965 5.94069 9.45032 5.36285 9.12614 4.88371ZM20.8688 9.0447C21.6621 8.50486 22.1831 7.59464 22.1831 6.56274C22.1831 4.90589 20.84 3.56274 19.1831 3.56274C17.5263 3.56274 16.1831 4.90589 16.1831 6.56274C16.1831 8.2196 17.5263 9.56274 19.1831 9.56274C19.8081 9.56274 20.3884 9.37165 20.8688 9.0447Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Vectors</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">pgvector extension</span>for AI/ML applications, enabling fast semantic search and embedding storage.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><h3 class="">Row Level Security</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">Granular access control policies</span> to secure data at the row level.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground sm:!border-l-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><h3 class="">Full SQL access</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Supabase supports<!-- --> <span class="text-foreground">CTEs, triggers, foreign keys, JSONB, full-text search</span>, and more.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><h3 class="">Postgres functions</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Run backend logic in the database if you prefer using stored procedures, PL/pgSQL, and custom functions.</p></div></div><div class="w-full p-4 lg:p-6 xl:p-8 h-full flex items-center justify-center"><div class="left-4 lg:left-6 xl:left-8 absolute w-full md:w-[600px] bg-surface-100 border border-overlay flex flex-col overflow-hidden rounded shadow-sm"><div class="border-b border-overlay flex justify-between w-full py-3 px-5"><div class="max-w-[85%] flex items-center space-x-3 truncate"><div class="h-5 m-0 text-sm truncate cursor-pointer text-foreground">Add a new Function</div></div></div><div class="border-b mb-8 py-4 px-5 flex flex-col md:flex-row gap-4"><p class="text-sm text-foreground-light">Name of function</p><div class="flex flex-col gap-2"><input placeholder="Name of function" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]"/><span class="text-foreground-muted text-sm">Name will also be used for the function name in postgres</span></div></div></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><h3 class="">Postgres extensions</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Tap into the <span class="text-foreground">full Posgres ecosystem</span>, including pgvector, PostGIS, pg_stat_statements, and over XX more Postgres extensions.</p></div></div><div class="w-full p-4 lg:p-6 xl:p-8 h-full flex items-center justify-center"><div class="w-full bg-surface-100 border border-overlay flex flex-col overflow-hidden rounded shadow-sm"><div class="border-b border-overlay flex justify-between w-full py-3 px-5"><div class="max-w-[85%] flex items-center space-x-3 truncate"><h3 class="h-5 m-0 text-sm truncate cursor-pointer text-foreground">PGTAP</h3></div><button type="button" role="switch" aria-checked="true" data-state="checked" value="on" class="peer inline-flex shrink-0 cursor-pointer items-center rounded-full border transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-brand data-[state=checked]:hover:bg-brand-600/90 data-[state=unchecked]:bg-control data-[state=unchecked]:hover:bg-border h-[20px] w-[34px]"><span data-state="checked" class="pointer-events-none block rounded-full bg-foreground-lighter data-[state=checked]:bg-white shadow-lg ring-0 transition-transform h-[16px] w-[16px] data-[state=checked]:translate-x-[15px] data-[state=unchecked]:translate-x-[1px]"></span></button><input type="checkbox" aria-hidden="true" style="transform:translateX(-100%);position:absolute;pointer-events:none;opacity:0;margin:0" tabindex="-1" checked="" value="on"/></div><div class="py-2 px-5"><p class="text-foreground-light text-sm">Unit testing for PostreSQL</p></div></div></div></div></div></div><div id="developer-experience" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-12 py-16 md:py-24 [&amp;_h2]:!max-w-sm"><div class="flex flex-col gap-4 max-w-lg"><h2 class="text-2xl md:text-3xl font-normal text-foreground-lighter">Developers can <span class="text-foreground">build faster</span> with Supabase</h2><p class="text-foreground-light text-base md:text-lg">Features that help developers move quickly and focus.</p></div><div class=" grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-0 rounded-md border-default overflow-hidden sm:divide-x divide-y border [&amp;&gt;div:nth-child(2n+1)]:sm:!border-l-0 [&amp;&gt;div:nth-child(2)]:sm:!border-t-0 [&amp;&gt;div:nth-child(3)]:lg:!border-t-0 [&amp;&gt;div:nth-child(3n)]:lg:!border-l [&amp;&gt;div:nth-child(4n)]:lg:!border-l-0 [&amp;&gt;div:nth-child(3n-1)]:lg:!border-l "><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.8949 2.39344C12.5051 1.78324 13.4944 1.78324 14.1046 2.39344L22.9106 11.1994C23.5208 11.8096 23.5208 12.7989 22.9106 13.4091L14.1046 22.2151C13.4944 22.8253 12.5051 22.8253 11.8949 22.2151L3.08892 13.4091C2.47872 12.7989 2.47872 11.8096 3.08892 11.1994L11.8949 2.39344Z M16.5408 12.3043C16.5408 14.2597 14.9556 15.8449 13.0002 15.8449C11.0448 15.8449 9.45961 14.2597 9.45961 12.3043C9.45961 10.3489 11.0448 8.76371 13.0002 8.76371C14.9556 8.76371 16.5408 10.3489 16.5408 12.3043Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">AI Assistant</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground">A single panel that persists across the Supabase Dashboard and maintains<!-- --> <span class="text-foreground">context across AI prompts</span>.</p></div></div><div class="w-full ml-4 md:ml-6 2xl:ml-8 max-w-[430px] rounded-tl-lg border-t border-l bg-default text-foreground"><div class="flex items-center gap-3 p-2 lg:p-4 border-b"><svg width="24" height="24" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="hover:rotate-12 transition-transform duration-300"><path d="M11.8949 2.39344C12.5051 1.78324 13.4944 1.78324 14.1046 2.39344L22.9106 11.1994C23.5208 11.8096 23.5208 12.7989 22.9106 13.4091L14.1046 22.2151C13.4944 22.8253 12.5051 22.8253 11.8949 22.2151L3.08892 13.4091C2.47872 12.7989 2.47872 11.8096 3.08892 11.1994L11.8949 2.39344Z M16.5408 12.3043C16.5408 14.2597 14.9556 15.8449 13.0002 15.8449C11.0448 15.8449 9.45961 14.2597 9.45961 12.3043C9.45961 10.3489 11.0448 8.76371 13.0002 8.76371C14.9556 8.76371 16.5408 10.3489 16.5408 12.3043Z" stroke="hsl(var(--brand-default))" stroke-miterlimit="10" stroke-linejoin="bevel" stroke-linecap="square" stroke-width="1.5"></path></svg><div class="flex items-center gap-1"><h3 class="text-sm font-medium">AI Assistant</h3><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info w-3 h-3 text-foreground-lighter"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg></div></div><div class="space-y-2 p-4 pr-0 2xl:py-8 xl:pl-12 xl:ml-1 text-sm text-[#808080]"><p>Entity: Auth</p><p>Schema:</p><p class="text-[#808080]">Issue: We have detected that you have enabled the email provider with an expiry time of more than an hour. It is recommended to set this value to less th...</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 5L22 2M2 22L5 19M7.5 13.5L10 11M10.5 16.5L13 14M6.3 20.3C6.52297 20.5237 6.78791 20.7013 7.07963 20.8224C7.37136 20.9435 7.68413 21.0059 8 21.0059C8.31587 21.0059 8.62864 20.9435 8.92036 20.8224C9.21209 20.7013 9.47703 20.5237 9.7 20.3L12 18L6 12L3.7 14.3C3.47626 14.523 3.29873 14.7879 3.17759 15.0796C3.05646 15.3714 2.99411 15.6841 2.99411 16C2.99411 16.3159 3.05646 16.6286 3.17759 16.9204C3.29873 17.2121 3.47626 17.477 3.7 17.7L6.3 20.3ZM12 6L18 12L20.3 9.7C20.5237 9.47703 20.7013 9.21209 20.8224 8.92036C20.9435 8.62864 21.0059 8.31587 21.0059 8C21.0059 7.68413 20.9435 7.37136 20.8224 7.07963C20.7013 6.78791 20.5237 6.52297 20.3 6.3L17.7 3.7C17.477 3.47626 17.2121 3.29873 16.9204 3.17759C16.6286 3.05646 16.3159 2.99411 16 2.99411C15.6841 2.99411 15.3714 3.05646 15.0796 3.17759C14.7879 3.29873 14.523 3.47626 14.3 3.7L12 6Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">MCP Server</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground">Connect your <span class="text-foreground">favorite AI tools</span> such as Cursor or Claude directly with Supabase.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.13477 12.8129C4.13477 14.1481 4.43245 15.4138 4.96506 16.5471M12.925 4.02271C11.5644 4.02271 10.276 4.33184 9.12614 4.88371M21.7152 12.8129C21.7152 11.4644 21.4115 10.1867 20.8688 9.0447M12.925 21.6032C14.2829 21.6032 15.5689 21.2952 16.717 20.7454M16.717 20.7454C17.2587 21.5257 18.1612 22.0366 19.1831 22.0366C20.84 22.0366 22.1831 20.6935 22.1831 19.0366C22.1831 17.3798 20.84 16.0366 19.1831 16.0366C17.5263 16.0366 16.1831 17.3798 16.1831 19.0366C16.1831 19.6716 16.3804 20.2605 16.717 20.7454ZM4.96506 16.5471C4.16552 17.086 3.63965 17.9999 3.63965 19.0366C3.63965 20.6935 4.98279 22.0366 6.63965 22.0366C8.2965 22.0366 9.63965 20.6935 9.63965 19.0366C9.63965 17.3798 8.2965 16.0366 6.63965 16.0366C6.01951 16.0366 5.44333 16.2248 4.96506 16.5471ZM9.12614 4.88371C8.58687 4.08666 7.67444 3.56274 6.63965 3.56274C4.98279 3.56274 3.63965 4.90589 3.63965 6.56274C3.63965 8.2196 4.98279 9.56274 6.63965 9.56274C8.2965 9.56274 9.63965 8.2196 9.63965 6.56274C9.63965 5.94069 9.45032 5.36285 9.12614 4.88371ZM20.8688 9.0447C21.6621 8.50486 22.1831 7.59464 22.1831 6.56274C22.1831 4.90589 20.84 3.56274 19.1831 3.56274C17.5263 3.56274 16.1831 4.90589 16.1831 6.56274C16.1831 8.2196 17.5263 9.56274 19.1831 9.56274C19.8081 9.56274 20.3884 9.37165 20.8688 9.0447Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Auto-generated APIs</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground"><span class="text-foreground">Learn SQL when you&#x27;re ready.</span> In the meantime, Supabase generates automatic APIs to make coding a lot easier.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.2805 18.2121C11.2419 18.6711 12.3325 18.8932 13.4711 18.8084C15.2257 18.6776 16.7596 17.843 17.8169 16.6015M8.21496 8.36469C9.27117 7.14237 10.7928 6.322 12.5311 6.19248C13.7196 6.10392 14.8558 6.34979 15.8474 6.85054M17.8169 16.6015L20.5242 19.3223C22.1857 17.5141 23.1562 15.1497 23.1562 12.5005C23.1562 6.89135 18.6091 2.34424 13 2.34424C10.9595 2.34424 9.16199 2.87659 7.57035 3.91232C8.35717 3.56865 9.22613 3.37801 10.1396 3.37801C12.6236 3.37801 14.7783 4.78762 15.8474 6.85054M17.8169 16.6015V16.6015C16.277 15.059 16.3448 12.5527 16.5387 10.3817C16.5557 10.191 16.5644 9.99794 16.5644 9.80282C16.5644 8.73844 16.3056 7.73451 15.8474 6.85054M13 22.6567C7.39086 22.6567 2.84375 18.1096 2.84375 12.5005C2.84375 9.84123 3.8026 7.48969 5.4753 5.67921L8.21496 8.42354V8.42354C9.76942 9.98064 9.69844 12.5133 9.51947 14.7062C9.50526 14.8803 9.49802 15.0564 9.49802 15.2341C9.49802 18.7705 12.3648 21.6373 15.9012 21.6373C16.8116 21.6373 17.6776 21.4473 18.4618 21.1048C16.8609 22.1588 15.06 22.6567 13 22.6567Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Foreign Data Wrappers</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground">Connect Supabase to <span class="text-foreground">Redshift, BigQuery, MySQL</span>, and external APIs for seamless integrations.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.5 1.5625C6.45939 1.5625 1.5625 6.45939 1.5625 12.5C1.5625 18.5406 6.45939 23.4375 12.5 23.4375C18.5406 23.4375 23.4375 18.5406 23.4375 12.5C23.4375 9.90692 22.5351 7.52461 21.0273 5.64995L11.6145 15.0627L9.61957 13.0677M12.6068 5.82237C8.92939 5.82237 5.94826 8.80351 5.94826 12.4809C5.94826 16.1583 8.92939 19.1395 12.6068 19.1395C16.2842 19.1395 19.2654 16.1583 19.2654 12.4809C19.2654 11.1095 18.8507 9.83483 18.14 8.77557" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Instant and secure deployment</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground"><span class="text-foreground">No need to set up servers</span>, manage DevOps, or tweak security settings.</p></div></div><div class=" absolute pointer-events-none w-full h-full inset-0 top-auto bg-[linear-gradient(to_top,transparent_0%,transparent_50%,hsl(var(--background-default))_75%)] "></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start justify-between bg-default w-full h-full min-h-[330px] sm:min-h-[360px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground"><div class="relative z-10 p-4 md:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start text-left lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.1404 7.66537C11.1404 5.18146 13.1541 3.16785 15.638 3.16785H17.3775C19.8614 3.16785 21.875 5.18146 21.875 7.66537V17.3776C21.875 19.8615 19.8614 21.8751 17.3775 21.8751H15.638C13.1541 21.8751 11.1404 19.8615 11.1404 17.3776V7.66537Z M3.125 14.7821C3.125 13.4015 4.24419 12.2823 5.62477 12.2823C7.00536 12.2823 8.12454 13.4015 8.12454 14.7821V19.3754C8.12454 20.7559 7.00536 21.8751 5.62477 21.8751C4.24419 21.8751 3.125 20.7559 3.125 19.3754V14.7821Z M3.125 5.58522C3.125 4.20463 4.24419 3.08545 5.62477 3.08545C7.00536 3.08545 8.12454 4.20463 8.12454 5.58522V6.95164C8.12454 8.33223 7.00536 9.45142 5.62477 9.45142C4.24419 9.45142 3.125 8.33223 3.125 6.95164V5.58522Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Observability</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm [&amp;_strong]:!text-foreground">Built-in logs, query performance tools, and security insights for<!-- --> <span class="text-foreground">easy debugging</span>.</p></div></div></div></div></div><div id="results" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 relative"><div class="relative z-10 flex flex-col gap-4 md:gap-8 pb-20"><div class="flex flex-col gap-2 max-w-xl"><h2 class="h2 !m-0">Top performance,<br/>at any scale</h2><p class="p !text-foreground-lighter">Supabase ensures optimal database performance at any scale, so you can focus on innovating and growing without worrying about infrastructure limitations — whether you&#x27;re handling high-traffic applications, complex queries, or massive data volumes.</p></div><div class="flex flex-wrap gap-4 md:gap-12"><li class="flex flex-col gap-2 text-sm"><span class="label">databases managed</span><p class="text-foreground text-xl md:text-3xl">6,500,000+</p></li><li class="flex flex-col gap-2 text-sm"><span class="label">databases launched daily</span><p class="text-foreground text-xl md:text-3xl">35,000+</p></li></div></div><div class="relative xl:absolute z-0 inset-0 mt-4 -mb-8 sm:mt-0 sm:-mb-20 md:-mt-20 md:-mb-36 xl:mt-0 xl:top-10 w-full aspect-[2.15/1]"><div class="absolute z-10 inset-0 left-auto right-[20%] -top-8 md:top-0 xl:top-[15%] w-fit h-[200px] lg:h-[400px] flex flex-col items-center gap-1"><div class="w-fit text-foreground bg-alternative p-4 rounded-lg border flex flex-col gap-1"><span class="label !text-[10px] !leading-3">Users</span><div class="flex items-center gap-2"><span class="text-foreground-light text-2xl">230,550</span><div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 py-0.5 text-xs h-[24px] px-2">+13.4%</div></div></div><div class="relative w-2 h-2 min-w-2 min-h-2 rounded-full border-2 border-stronger after:absolute after:inset-0 after:top-full after:mx-auto after:w-[2px] after:h-[150px] after:lg:h-[250px] after:bg-gradient-to-b after:from-border-stronger after:to-transparent"></div></div><svg width="100%" height="100%" viewBox="0 0 1403 599" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute inset-0 w-full h-full"><path d="M1402.27 0.744141C896.689 410.854 286.329 492.876 0.476562 492.876V598.744H1402.27V0.744141Z" fill="url(#paint0_linear_585_9420)"></path><path d="M11.4209 492.744C295.041 492.744 900.636 410.744 1402.27 0.744141" stroke="hsl(var(--foreground-lighter))"></path><defs><linearGradient id="paint0_linear_585_9420" x1="701.374" y1="170.846" x2="701.374" y2="561.839" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--border-overlay))"></stop><stop offset="1" stop-color="hsl(var(--border-overlay))" stop-opacity="0"></stop></linearGradient></defs></svg><div class="absolute inset-0 w-full h-full bg-[radial-gradient(50%_50%_at_50%_50%,_transparent_0%,_hsl(var(--background-default))_100%)]"></div></div></div><div id="database-features" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-12 py-16 md:py-24"><div class=" grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 sm:divide-x divide-y border border-default rounded-md overflow-hidden [&amp;&gt;div:nth-child(2n+1)]:sm:!border-l-0 [&amp;&gt;div:nth-child(2)]:sm:!border-t-0 [&amp;&gt;div:nth-child(3)]:lg:!border-t-0 [&amp;&gt;div:nth-child(3n)]:lg:!border-l [&amp;&gt;div:nth-child(4n)]:lg:!border-l-0 [&amp;&gt;div:nth-child(3n-1)]:lg:!border-l "><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.2689 14.9229C14.04 16.494 15.6379 17.4892 17.3881 17.4893H22.0892C22.4726 17.4893 22.7843 17.8003 22.7845 18.1836C22.7845 18.5671 22.4728 18.8789 22.0892 18.8789H20.1664C20.1564 21.0605 18.171 22.4853 16.0052 22.4854C14.044 22.4854 12.4009 21.1292 11.9603 19.3037L11.9213 19.126L11.9086 18.9854C11.9116 18.6624 12.1408 18.3748 12.4701 18.3105C12.7994 18.2463 13.1203 18.4265 13.2445 18.7246L13.2845 18.8594L13.3412 19.0947C13.6746 20.251 14.742 21.0967 16.0052 21.0967C17.6551 21.0966 18.7655 20.0649 18.7758 18.8789H17.3881C15.108 18.8788 13.0263 17.5811 12.0218 15.5342L13.2689 14.9229ZM18.7767 15.6787V11.4639C18.7766 8.09738 16.0476 5.36816 12.681 5.36816H11.7269C11.7032 5.36816 11.6797 5.36364 11.6566 5.36133H7.15564C6.5783 5.36133 6.05835 5.69927 5.82068 6.21777L5.77673 6.32422L4.26404 10.4443C4.03486 11.0686 4.21563 11.7696 4.71814 12.2051L5.75622 13.1045L5.93298 13.2754C6.32193 13.694 6.54138 14.2468 6.54138 14.8242V16.4775L6.5531 16.7227C6.67574 17.9298 7.69544 18.8721 8.93493 18.8721C9.2213 18.8721 9.45986 18.6685 9.51501 18.3984L9.52771 18.2793V10.9121C9.52772 9.33737 10.1566 7.82755 11.2748 6.71875L11.3842 6.63086C11.6543 6.45411 12.0199 6.48475 12.2562 6.72266C12.5263 6.995 12.5247 7.43503 12.2523 7.70508L12.097 7.86816C11.3396 8.69814 10.9164 9.78304 10.9164 10.9121V18.2793L10.9056 18.4814C10.8044 19.4807 9.96094 20.2607 8.93493 20.2607C6.91113 20.2607 5.25814 18.6714 5.15661 16.6729L5.15173 16.4775V14.8242C5.15173 14.5993 5.06693 14.3838 4.9154 14.2207L4.84607 14.1543L3.80798 13.2549C2.86934 12.4414 2.53223 11.1318 2.96033 9.96582L4.47302 5.84473L4.55798 5.63867C5.02039 4.62971 6.03224 3.97266 7.15564 3.97266H11.8246V3.97949H12.681C16.8146 3.97949 20.1662 7.33032 20.1664 11.4639V15.6787C20.1664 16.0622 19.8546 16.373 19.4711 16.373C19.0877 16.3728 18.7767 16.0621 18.7767 15.6787ZM12.3392 14.6055C12.6835 14.4365 13.1 14.5785 13.2689 14.9229L12.0218 15.5342C11.8532 15.1901 11.9953 14.7745 12.3392 14.6055Z M14.4779 10.7135C14.4779 11.1278 14.8137 11.4635 15.2279 11.4635C15.6421 11.4635 15.9779 11.1278 15.9779 10.7135C15.9779 10.2993 15.6421 9.96354 15.2279 9.96354C14.8137 9.96354 14.4779 10.2993 14.4779 10.7135Z" stroke="none" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Postgres at its core</h3></div><p class="text-base">ACID-compliant, battle-tested database<!-- --> <span class="text-foreground">trusted by enterprises and startups</span>.</p></div><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.2847 11.1404V7.8447C14.2847 5.36078 12.2711 3.34717 9.7872 3.34717H7.84476C5.36084 3.34717 3.34723 5.36078 3.34723 7.8447V9.78714C3.34723 12.2711 5.36084 14.2847 7.84476 14.2847H11.1253M8.63752 8.65306L18.4524 18.468M19.1282 14.068V16.5986C19.1282 17.8405 18.1214 18.8474 16.8794 18.8474H14.2847M15.6573 22.0972H17.5997C20.0836 22.0972 22.0972 20.0836 22.0972 17.5996V15.6572C22.0972 13.1733 20.0836 11.1597 17.5997 11.1597H15.6573C13.1733 11.1597 11.1597 13.1733 11.1597 15.6572V17.5996C11.1597 20.0836 13.1733 22.0972 15.6573 22.0972Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Horizontal &amp; Vertical Scaling</h3></div><p class="text-base">Scale compute and storage independently, including support for<!-- --> <span class="text-foreground">read replicas</span>.</p></div><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.48462 3.05339C6.79298 3.58819 5.33457 4.64831 4.30037 6.0436C3.4029 7.25444 2.82613 8.71636 2.69516 10.306H6.77142C6.83771 8.01994 7.22916 5.93809 7.84745 4.36313C8.03485 3.88578 8.24723 3.44433 8.48462 3.05339ZM10.9999 1.27832C7.79633 1.27832 4.95467 2.82842 3.18457 5.21656C1.98658 6.83284 1.2778 8.83471 1.2778 11.0001C1.2778 13.1781 1.99476 15.1906 3.20527 16.8117C4.97675 19.1842 7.80877 20.7225 10.9999 20.7225C14.191 20.7225 17.023 19.1841 18.7944 16.8117C20.005 15.1906 20.722 13.1781 20.722 11.0001C20.722 8.83471 20.0132 6.83284 18.8152 5.21656L18.7944 5.18864C17.0229 2.81635 14.1909 1.27832 10.9999 1.27832ZM10.9999 2.66721C10.768 2.66721 10.4732 2.78413 10.1294 3.15462C9.78466 3.52602 9.44227 4.10142 9.14028 4.87067C8.596 6.2571 8.22699 8.16013 8.16092 10.306H13.8389C13.7728 8.16013 13.4038 6.2571 12.8595 4.87067C12.5575 4.10142 12.2151 3.52602 11.8704 3.15462C11.5265 2.78413 11.2318 2.66721 10.9999 2.66721ZM15.2284 10.306C15.1621 8.01994 14.7706 5.93809 14.1523 4.36313C13.9649 3.88578 13.7525 3.44433 13.5152 3.05339C15.1971 3.58512 16.6485 4.63618 17.6816 6.01966L17.6994 6.0436C18.5969 7.25443 19.1737 8.71636 19.3046 10.306H15.2284ZM13.8389 11.6949H8.16092C8.22699 13.8407 8.596 15.7437 9.14028 17.1301C9.44227 17.8994 9.78466 18.4748 10.1294 18.8462C10.4732 19.2167 10.768 19.3336 10.9999 19.3336C11.2318 19.3336 11.5265 19.2167 11.8704 18.8462C12.2151 18.4748 12.5575 17.8994 12.8595 17.1301C13.4038 15.7437 13.7728 13.8407 13.8389 11.6949ZM13.5152 18.9473C13.7526 18.5564 13.965 18.115 14.1523 17.6377C14.7706 16.0627 15.1621 13.9809 15.2284 11.6949H19.3046C19.1727 13.2947 18.5892 14.7653 17.6816 15.9807C16.6485 17.3643 15.1971 18.4155 13.5152 18.9473ZM8.48458 18.9474C8.24721 18.5564 8.03484 18.115 7.84745 17.6377C7.22916 16.0627 6.83771 13.9809 6.77142 11.6949H2.6952C2.82712 13.2947 3.41061 14.7653 4.31815 15.9808C5.35126 17.3644 6.80264 18.4156 8.48458 18.9474Z" stroke="none" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Multi-region Deployments</h3></div><p class="text-base">Deploy databases across multiple regions for<!-- --> <span class="text-foreground">global availability</span>.</p></div><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.3046 3.24514C15.3004 2.91279 14.2268 2.73291 13.1111 2.73291C7.50197 2.73291 2.95486 7.28002 2.95486 12.8892C2.95486 18.4983 7.50197 23.0454 13.1111 23.0454C18.7203 23.0454 23.2674 18.4983 23.2674 12.8892C23.2674 10.5703 22.4902 8.4329 21.1822 6.72328L12.2253 15.5572L10.2303 13.5622M13.2175 6.31682C9.54013 6.31682 6.55899 9.29795 6.55899 12.4809C6.55899 16.1583 9.54013 19.1395 13.2175 19.1395C16.895 19.1395 19.8761 16.1583 19.8761 12.4809C19.8761 11.1095 19.4615 9.83483 18.7507 8.77557" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">High Availability Architecture</h3></div><p class="text-base">Enterprise plans offer<!-- --> <span class="text-foreground">automatic failover and redundancy</span> for mission-critical applications.</p></div><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.3784 13.3407C3.1413 10.4689 4.12132 7.51558 6.31845 5.31845C10.2847 1.35219 16.7153 1.35219 20.6816 5.31845C24.6478 9.28471 24.6478 15.7153 20.6816 19.6816C16.7153 23.6478 10.2847 23.6478 6.31845 19.6816C5.3819 18.745 4.6665 17.671 4.17224 16.5246M0.706939 11.443L2.28117 13.0172C2.89137 13.6274 3.88069 13.6274 4.49088 13.0172L6.06512 11.443M10.761 17.5453L16.0995 17.5453C16.9625 17.5453 17.662 16.8458 17.662 15.9828V15.7328C17.662 14.8699 16.9625 14.1703 16.0995 14.1703L10.761 14.1703C9.89806 14.1703 9.1985 14.8699 9.1985 15.7328L9.1985 15.9828C9.1985 16.8458 9.89806 17.5453 10.761 17.5453ZM11.1648 14.1711L15.6537 14.1711C16.5167 14.1711 17.2162 13.4716 17.2162 12.6086L17.2162 12.3586C17.2162 11.4956 16.5167 10.7961 15.6537 10.7961L11.1648 10.7961C10.3019 10.7961 9.60234 11.4956 9.60234 12.3586L9.60234 12.6086C9.60234 13.4716 10.3019 14.1711 11.1648 14.1711ZM10.7606 10.7963L16.0991 10.7963C16.9621 10.7963 17.6616 10.0967 17.6616 9.2338V8.98375C17.6616 8.1208 16.9621 7.42125 16.0991 7.42125L10.7606 7.42125C9.89765 7.42125 9.19809 8.12081 9.19809 8.98375L9.19809 9.2338C9.19809 10.0967 9.89765 10.7963 10.7606 10.7963Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Point-in-Time Recovery</h3></div><p class="text-base">Restore your database <span class="text-foreground">to any point in time</span> <!-- -->for disaster recovery.</p></div><div class="text-sm bg-default p-4 md:p-6 flex flex-col gap-2 md:gap-4 text-foreground-lighter"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M22.375 5.7085C22.375 7.43439 18.1777 8.8335 13 8.8335C7.82233 8.8335 3.625 7.43439 3.625 5.7085M22.375 5.7085C22.375 3.98261 18.1777 2.5835 13 2.5835C7.82233 2.5835 3.625 3.98261 3.625 5.7085M22.375 5.7085V10.1877M3.625 5.7085L3.625 20.2918C3.62434 20.9675 4.28075 21.6251 5.49583 22.166C6.71091 22.7069 8.41919 23.1019 10.3646 23.2918M3.625 13.0002C3.6235 13.5826 4.11036 14.1536 5.03066 14.6487C5.95095 15.1438 7.26805 15.5434 8.83334 15.8022M13 13.0002V17.1668M13 17.1668H17.1667M13 17.1668L15.1771 14.9897C16.0833 14.0835 17.3438 13.521 18.7292 13.521C19.9724 13.521 21.1647 14.0149 22.0437 14.8939C22.9228 15.773 23.4167 16.9653 23.4167 18.2085C23.4167 19.3016 23.0727 20.3671 22.4336 21.2539C21.7944 22.1407 20.8924 22.8039 19.8554 23.1496C18.8183 23.4952 17.6988 23.5059 16.6554 23.1799C15.612 22.854 14.6975 22.208 14.0417 21.3335" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Automatic Backups</h3></div><p class="text-base"><span class="text-foreground">Daily backups</span> with retention policies for added security.</p></div></div></div><div id="security" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col xl:flex-row justify-between gap-4 md:gap-8"><div class="flex flex-col gap-2 max-w-xl"><span class="label">Security</span><h2 class="h2 !m-0">Trusted for medical records, missions to the moon, and everything in between</h2><p class="p !text-foreground-lighter">Keep your data secure with SOC 2, HIPAA, and GDPR compliance. Your customers&#x27; data is encrypted at rest and in transit, with built-in tools for monitoring and managing security threats.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-2" target="_self" href="../security.html"><div class="group flex items-center gap-1"><span class="sr-only">Learn about security about /security</span><span>Learn about security</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></div><ul class="grid grid-cols-2 sm:grid-cols-2 gap-4 md:gap-x-20 h-fit xl:grid-cols-2 mt-4 xl:mt-8"><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-check w-4 h-4 stroke-1"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="m9 12 2 2 4-4"></path></svg></figure><p>SOC 2 Type II certified</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart-pulse w-4 h-4 stroke-1"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path><path d="M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27"></path></svg></figure><p>HIPAA compliance</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-alert w-4 h-4 stroke-1"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg></figure><p>DDoS Protection</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-4 h-4 stroke-1"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></figure><p>Multi-factor Authentication</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check w-4 h-4 stroke-1"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><path d="m9 14 2 2 4-4"></path></svg></figure><p>Vulnerability Management</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 stroke-1"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></figure><p>Role-based access control</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list w-4 h-4 stroke-1"><line x1="8" x2="21" y1="6" y2="6"></line><line x1="8" x2="21" y1="12" y2="12"></line><line x1="8" x2="21" y1="18" y2="18"></line><line x1="3" x2="3.01" y1="6" y2="6"></line><line x1="3" x2="3.01" y1="12" y2="12"></line><line x1="3" x2="3.01" y1="18" y2="18"></line></svg></figure><p>Database Audit Logs</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lightbulb w-4 h-4 stroke-1"><path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d="M9 18h6"></path><path d="M10 22h4"></path></svg></figure><p>Security Advisors</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-lock w-4 h-4 stroke-1"><rect width="8" height="5" x="14" y="17" rx="1"></rect><path d="M10 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v2.5"></path><path d="M20 17v-2a2 2 0 1 0-4 0v2"></path></svg></figure><p>Encrypted Storage</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-x w-4 h-4 stroke-1"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><line x1="17" x2="22" y1="8" y2="13"></line><line x1="22" x2="17" y1="8" y2="13"></line></svg></figure><p>Network restrictions</p></li></ul></div><div id="platform-starter" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-16 md:py-24"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16"><div class="col-left space-y-6 lg:pr-10"><h2 class="h2 text-foreground-lighter"><span class="text-foreground block">Choose your platform</span> to start building in seconds</h2><div class="grid grid-cols-5 divide-x divide-y rounded-lg overflow-hidden border"><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/reactjs.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M45.74 23.6983C45.2739 23.5379 44.7909 23.3861 44.2937 23.2426C44.3754 22.909 44.4504 22.5798 44.5171 22.2561C45.6119 16.9418 44.8961 12.6605 42.4518 11.2509C40.1079 9.89927 36.2748 11.3085 32.4035 14.6776C32.0313 15.0016 31.6579 15.3446 31.2848 15.704C31.0362 15.4662 30.7879 15.2364 30.5403 15.0165C26.4831 11.4141 22.4164 9.89599 19.9744 11.3097C17.6329 12.6652 16.9394 16.69 17.9249 21.7265C18.0201 22.2129 18.1313 22.7097 18.2571 23.2148C17.6816 23.3782 17.1259 23.5524 16.5943 23.7377C11.8376 25.3961 8.7998 27.9952 8.7998 30.6911C8.7998 33.4755 12.0609 36.2683 17.0153 37.9617C17.4063 38.0953 17.812 38.2217 18.2301 38.3416C18.0944 38.8879 17.9763 39.4232 17.8773 39.9454C16.9376 44.8944 17.6714 48.8242 20.0068 50.1711C22.4189 51.5622 26.4673 50.1324 30.4093 46.6865C30.7209 46.4141 31.0336 46.1253 31.3469 45.8225C31.7529 46.2135 32.1582 46.5835 32.5615 46.9306C36.3798 50.2164 40.151 51.5432 42.4842 50.1925C44.894 48.7975 45.6772 44.576 44.6604 39.4399C44.5828 39.0476 44.4924 38.6469 44.3909 38.239C44.6752 38.155 44.9543 38.0682 45.2265 37.978C50.3771 36.2715 53.7282 33.5127 53.7282 30.6911C53.7282 27.9854 50.5924 25.3688 45.74 23.6983ZM44.6228 36.1561C44.3772 36.2375 44.1251 36.3161 43.8682 36.3923C43.2996 34.5922 42.5322 32.6781 41.5931 30.7005C42.4893 28.7699 43.227 26.8803 43.7797 25.0919C44.2393 25.2249 44.6854 25.3651 45.1152 25.5132C49.2728 26.9444 51.8089 29.0605 51.8089 30.6911C51.8089 32.4279 49.07 34.6826 44.6228 36.1561ZM42.7776 39.8126C43.2272 42.0837 43.2914 44.1371 42.9936 45.7423C42.726 47.1847 42.1878 48.1463 41.5225 48.5315C40.1066 49.351 37.0787 48.2857 33.8132 45.4757C33.4388 45.1535 33.0618 44.8096 32.6835 44.4455C33.9495 43.061 35.2147 41.4514 36.4495 39.6638C38.6215 39.4711 40.6735 39.156 42.5344 38.7258C42.626 39.0955 42.7074 39.4581 42.7776 39.8126ZM24.1169 48.3898C22.7336 48.8784 21.6318 48.8924 20.9658 48.5084C19.5486 47.691 18.9594 44.5358 19.7631 40.3033C19.8551 39.8186 19.9647 39.3207 20.091 38.8118C21.9314 39.2187 23.9684 39.5116 26.1456 39.6881C27.3887 41.4373 28.6905 43.0452 30.0024 44.453C29.7157 44.7297 29.4302 44.9931 29.1463 45.2413C27.4032 46.7651 25.6564 47.8461 24.1169 48.3898ZM17.6361 36.1455C15.4453 35.3967 13.6361 34.4235 12.396 33.3616C11.2817 32.4073 10.7191 31.4599 10.7191 30.6911C10.7191 29.0551 13.1581 26.9684 17.226 25.5501C17.7196 25.378 18.2363 25.2158 18.7725 25.0635C19.3347 26.8923 20.0722 28.8043 20.9623 30.7378C20.0607 32.7 19.3128 34.6425 18.745 36.4927C18.3628 36.3829 17.9924 36.2672 17.6361 36.1455ZM19.8085 21.3579C18.9642 17.0428 19.5249 13.7876 20.936 12.9708C22.4391 12.1006 25.7628 13.3413 29.2659 16.4518C29.4898 16.6506 29.7146 16.8587 29.9401 17.074C28.6347 18.4756 27.3448 20.0714 26.1127 21.8103C23.9997 22.0061 21.977 22.3208 20.1174 22.742C20.0004 22.2717 19.8969 21.8097 19.8085 21.3579ZM39.1886 26.1433C38.744 25.3754 38.2876 24.6257 37.8223 23.8964C39.2558 24.0777 40.6293 24.3182 41.9191 24.6126C41.5318 25.8536 41.0492 27.1511 40.4811 28.4813C40.0735 27.7076 39.6425 26.9275 39.1886 26.1433ZM31.2854 18.4456C32.1707 19.4047 33.0573 20.4756 33.9293 21.6374C33.0506 21.5959 32.161 21.5743 31.264 21.5743C30.3755 21.5743 29.4925 21.5954 28.6192 21.6362C29.4921 20.4852 30.3863 19.4158 31.2854 18.4456ZM23.3317 26.1566C22.8876 26.9267 22.4645 27.7025 22.0634 28.4799C21.5045 27.1543 21.0263 25.8509 20.6357 24.5923C21.9176 24.3054 23.2846 24.0709 24.7089 23.8931C24.2371 24.6291 23.7769 25.3843 23.3317 26.1564V26.1566ZM24.75 37.626C23.2783 37.4618 21.8908 37.2394 20.6093 36.9604C21.0061 35.6793 21.4948 34.3481 22.0655 32.994C22.4677 33.7707 22.8925 34.5469 23.3393 35.3187H23.3393C23.7945 36.1049 24.266 36.875 24.75 37.626ZM31.3385 43.0719C30.4289 42.0904 29.5215 41.0047 28.6353 39.8368C29.4956 39.8706 30.3726 39.8879 31.264 39.8879C32.1798 39.8879 33.085 39.8672 33.9761 39.8276C33.1012 41.0164 32.2178 42.1038 31.3385 43.0719ZM40.4994 32.9249C41.0999 34.2937 41.6061 35.618 42.0081 36.8772C40.7054 37.1744 39.2989 37.4138 37.8171 37.5916C38.2835 36.8525 38.7439 36.0899 39.1963 35.3055C39.6539 34.5118 40.0885 33.717 40.4994 32.9249ZM37.5337 34.3466C36.8314 35.5643 36.1104 36.7268 35.3784 37.8241C34.0452 37.9194 32.6678 37.9685 31.264 37.9685C29.8659 37.9685 28.5058 37.9251 27.1962 37.8401C26.4347 36.7284 25.698 35.5625 25.0002 34.3571H25.0004C24.3044 33.155 23.6638 31.9427 23.0834 30.7372C23.6636 29.5289 24.3025 28.3152 24.9945 27.1152L24.9944 27.1155C25.6882 25.9123 26.4184 24.7521 27.1729 23.6473C28.509 23.5463 29.8792 23.4936 31.2639 23.4936H31.264C32.655 23.4936 34.0269 23.5467 35.3626 23.6486C36.1056 24.7453 36.8308 25.9017 37.5274 27.1051C38.2319 28.3219 38.879 29.5275 39.4642 30.7099C38.8808 31.9126 38.2351 33.1303 37.5337 34.3466ZM41.4931 12.9137C42.9976 13.7813 43.5826 17.2804 42.6374 21.8688C42.5771 22.1615 42.5092 22.4597 42.4354 22.762C40.5715 22.3319 38.5474 22.0118 36.4282 21.813C35.1937 20.055 33.9143 18.4567 32.6302 17.0731C32.9755 16.741 33.3202 16.4243 33.6636 16.1254C36.9805 13.2388 40.0806 12.0991 41.4931 12.9137ZM31.264 26.6791C33.4797 26.6791 35.276 28.4753 35.276 30.6911C35.276 32.9068 33.4797 34.703 31.264 34.703C29.0483 34.703 27.252 32.9068 27.252 30.6911C27.252 28.4753 29.0483 26.6791 31.264 26.6791Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">React</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/nextjs.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42.3148 48.6796C38.9009 50.9525 34.8014 52.2771 30.3924 52.2771C18.4957 52.2771 8.85156 42.6329 8.85156 30.7362C8.85156 18.8395 18.4957 9.19531 30.3924 9.19531C42.2891 9.19531 51.9333 18.8395 51.9333 30.7362C51.9333 37.1564 49.1245 42.9207 44.6688 46.8671L39.5552 40.2803V21.8278H36.584V36.4531L25.2299 21.8278H21.4808V39.6473H24.4801V25.6368L42.3148 48.6796Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Next.js</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/redwoodjs.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21.7716 14.6475L31.2847 21.1184C31.4993 21.2609 31.7501 21.3389 32.0076 21.3434C32.2654 21.3415 32.5167 21.2633 32.7304 21.1184L42.2508 14.6257C42.6207 14.3618 42.8247 13.9218 42.7876 13.4678C42.7505 13.0139 42.4779 12.6131 42.0701 12.4131L32.5569 7.71949C32.1961 7.545 31.7757 7.545 31.4147 7.71949L21.9306 12.4131C21.5154 12.6141 21.2392 13.0227 21.2063 13.4841C21.1735 13.9455 21.3891 14.3893 21.7716 14.6475ZM35.2389 23.1497C35.2396 23.5789 35.4504 23.9801 35.8027 24.2233L43.4291 29.4176C43.9173 29.7529 44.5705 29.7111 45.0123 29.316L51.4098 23.614C51.6948 23.3601 51.8531 22.9925 51.8419 22.6102C51.8309 22.2279 51.6513 21.8702 51.3519 21.6335L45.2436 16.7658C44.7995 16.4143 44.1802 16.3908 43.711 16.7078L35.8027 22.0978C35.4566 22.3366 35.2466 22.7283 35.2389 23.1497ZM16.2704 30.2155C16.5786 30.4914 16.7371 30.8984 16.6969 31.311C16.6569 31.7258 16.4164 32.0946 16.0536 32.2975L11.4994 35.0179C11.0377 35.2929 10.4548 35.2533 10.0344 34.9183C9.61395 34.5834 9.44327 34.0226 9.60544 33.509L11.2898 28.2278C11.4219 27.8093 11.7568 27.4869 12.1789 27.3718C12.6007 27.2494 13.0555 27.3567 13.3789 27.6547L16.2704 30.2155ZM40.9712 30.7668L32.7377 25.1519C32.3001 24.8585 31.7295 24.8585 31.2919 25.1519L23.0582 30.7668C22.7308 30.9927 22.5234 31.3557 22.4945 31.7534C22.4731 32.154 22.6323 32.5428 22.9282 32.8126L31.1545 40.1468C31.3918 40.3577 31.6979 40.4738 32.0148 40.4732C32.3315 40.4731 32.6375 40.3571 32.875 40.1468L41.1014 32.8126C41.3981 32.544 41.5553 32.1535 41.5279 31.7534C41.5049 31.3557 41.2993 30.9912 40.9712 30.7668ZM19.0101 29.316L12.6199 23.614C12.3323 23.3537 12.1736 22.9795 12.1861 22.5911C12.1931 22.2083 12.3708 21.8488 12.6705 21.6118L18.7788 16.715C19.2259 16.3645 19.8465 16.3411 20.3185 16.657L28.2197 22.047C28.5863 22.2867 28.8075 22.696 28.8075 23.1352C28.8075 23.5743 28.5863 23.9837 28.2197 24.2233L20.6005 29.4175C20.1093 29.7514 19.4552 29.7097 19.0101 29.316ZM51.8218 37.5062L45.3158 33.625C44.8326 33.3349 44.2176 33.3937 43.7977 33.7702L35.8461 40.8432C35.5106 41.1418 35.3531 41.5933 35.4296 42.0366C35.5063 42.4798 35.8059 42.8518 36.2219 43.0196L47.2531 47.4738C47.4044 47.5365 47.5666 47.5686 47.7302 47.5681C48.2422 47.5697 48.7069 47.2683 48.9158 46.7991L52.335 39.1675C52.6132 38.5636 52.3917 37.8462 51.8218 37.5062ZM52.7253 28.2278L54.4097 33.509H54.3952C54.5198 33.904 54.449 34.3351 54.2047 34.669C53.9604 35.0031 53.572 35.2 53.159 35.1993C52.9275 35.2 52.7 35.1374 52.5013 35.018L47.9399 32.2975C47.5844 32.0904 47.3524 31.722 47.3182 31.311C47.2755 30.8981 47.4344 30.49 47.7447 30.2155L50.6362 27.6474C50.9629 27.3558 51.4143 27.2493 51.8362 27.3645C52.258 27.4846 52.5918 27.8088 52.7253 28.2278ZM28.5955 42.033C28.6733 41.5915 28.5174 41.1412 28.1835 40.8432L20.2318 33.7702C19.8119 33.3937 19.1969 33.3349 18.7138 33.625L12.2078 37.5062C11.6445 37.8477 11.4216 38.556 11.6873 39.1603L15.1138 46.7919C15.3977 47.4283 16.1316 47.7261 16.7764 47.4666L27.8004 43.0124C28.2165 42.846 28.5173 42.4755 28.5955 42.033ZM32.4991 44.2093L41.3472 47.7785C41.7799 47.963 42.0787 48.3684 42.128 48.8376C42.184 49.3127 41.9722 49.7795 41.5785 50.0491L32.7232 56.1791C32.511 56.3277 32.2591 56.4086 32.0003 56.4113C31.7418 56.4073 31.4903 56.3265 31.2774 56.1791L22.4294 50.0491C22.0343 49.7802 21.82 49.3139 21.8728 48.8376C21.9318 48.3636 22.2414 47.9586 22.6824 47.7785L31.5305 44.2093C31.8416 44.0856 32.188 44.0856 32.4991 44.2093Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">RedwoodJS</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/flutter.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5067 10.3828L34.3509 10.3962L14.75 29.9971L20.7974 36.0519L26.1125 30.7666L46.5067 10.3828Z M34.6996 28.4653C34.5272 28.4573 34.3493 28.4491 34.2378 28.5965L23.7856 39.0471L29.7894 45.0142L29.7825 45.021L34.079 49.3212C34.1072 49.3462 34.1352 49.3741 34.1637 49.4026C34.2813 49.5201 34.4074 49.6462 34.5895 49.6055C36.5743 49.601 38.5591 49.6017 40.544 49.6025C42.529 49.6032 44.5142 49.604 46.4998 49.5995L35.9333 39.0234L46.4963 28.467L34.906 28.464C34.8415 28.4719 34.7711 28.4686 34.6996 28.4653Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Flutter</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/kotlin.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M51.7395 51.7398H12.2598V12.2601H51.7395L31.591 31.7137L51.7395 51.7398Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Kotlin</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/sveltekit.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M29.9094 11.2292C35.956 7.37668 44.3187 9.17299 48.553 15.2334H48.5532C50.5831 18.0746 51.3826 21.614 50.771 25.0519C50.4778 26.677 49.8581 28.2259 48.9493 29.6047C50.2752 32.1335 50.7201 35.0322 50.2136 37.8422C49.6086 41.2154 47.6106 44.1777 44.7096 46.0024L34.0903 52.7707C28.0445 56.623 19.6818 54.8274 15.4466 48.7665C13.4171 45.9251 12.6176 42.3859 13.2288 38.948C13.5223 37.3227 14.1422 35.7738 15.0512 34.3949C13.7247 31.8665 13.2794 28.9677 13.786 26.1577C14.3913 22.7845 16.3893 19.8223 19.29 17.9974L29.9094 11.2292ZM19.8146 45.9861C21.8311 48.8931 25.4469 50.2333 28.8709 49.343H28.8708C29.6345 49.139 30.3624 48.8192 31.0293 48.3946L41.6512 41.6252C43.396 40.5274 44.5979 38.7455 44.9622 36.7164C45.33 34.6483 44.8489 32.5192 43.6278 30.8101C41.6113 27.9032 37.9955 26.5629 34.5715 27.4531C33.8084 27.6571 33.081 27.9768 32.4147 28.4012L28.3617 30.9842C28.1601 31.1125 27.9401 31.2092 27.7093 31.271C26.6776 31.5384 25.5887 31.1342 24.9815 30.2584C24.614 29.7429 24.4693 29.1012 24.5801 28.4779C24.6899 27.8669 25.0519 27.3302 25.5774 26.9996L36.2002 20.2298C36.4017 20.1015 36.6218 20.0048 36.8526 19.9431C37.8838 19.6754 38.9725 20.0795 39.5793 20.9551C39.9039 21.4146 40.0556 21.974 40.0078 22.5345L39.9714 22.9285L40.3662 23.0484C41.8596 23.4989 43.265 24.2014 44.5218 25.1254L45.0657 25.5245L45.2658 24.9145C45.3729 24.59 45.4577 24.2586 45.5196 23.9225C45.8873 21.8544 45.4063 19.7254 44.1852 18.0162C42.1687 15.1093 38.553 13.7691 35.129 14.6593C34.3653 14.8633 33.6374 15.1832 32.9705 15.6077L22.3487 22.3777C20.6036 23.475 19.4016 25.2568 19.0376 27.2858C18.6699 29.3539 19.1509 31.4829 20.372 33.192C22.3885 36.099 26.0043 37.4392 29.4283 36.549C30.1914 36.345 30.9188 36.0256 31.5853 35.6017L35.6389 33.0177C35.8402 32.8895 36.06 32.7929 36.2905 32.7311C37.3221 32.4637 38.4111 32.868 39.0183 33.7438C39.3857 34.2592 39.5306 34.9007 39.4205 35.524C39.3102 36.1352 38.948 36.6718 38.4224 37.0028L27.7996 43.7722C27.5981 43.9006 27.378 43.9973 27.1471 44.059C26.116 44.3266 25.0273 43.9225 24.4204 43.0469C24.0955 42.5876 23.9438 42.0281 23.992 41.4675L24.0284 41.0735L23.6336 40.9537C22.1404 40.5032 20.7351 39.8011 19.4783 38.8776L18.934 38.4778L18.734 39.0878C18.6266 39.4122 18.5418 39.7437 18.4801 40.0798C18.1125 42.1479 18.5935 44.277 19.8146 45.9861Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Svelte</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/solidjs.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M26.7069 8.48157C39.8712 5.45973 56.8336 18.3919 56.8336 18.3919L51.0722 28.1073L50.9621 28.0856C51.0348 28.141 51.0722 28.1726 51.0722 28.1726C51.0681 28.1647 50.7083 27.8867 50.0561 27.4261C46.2099 24.7104 32.1917 15.6482 20.9454 18.2298C20.8906 18.2436 20.8351 18.2574 20.7793 18.2714C20.4417 18.3556 20.0905 18.4432 19.7867 18.5547C18.0673 19.116 16.7102 20.0163 15.7606 21.1271L15.7312 21.1213L20.5914 12.7706C20.6522 12.6724 20.7085 12.5694 20.7654 12.4654C20.8589 12.2942 20.9541 12.12 21.0742 11.9583C22.0076 10.5936 23.5204 9.45636 25.5481 8.8065C25.852 8.69497 26.2032 8.60737 26.5408 8.52316L26.5433 8.52253C26.5983 8.50882 26.6529 8.49519 26.7069 8.48157Z M19.46 32.0592L32.05 27.9448C35.8485 26.7049 40.3864 27.4446 43.8122 29.5562C43.7796 29.5338 44.8294 30.2289 44.9407 30.3542L49.7055 28.4801C49.3614 28.2446 48.0959 27.3256 47.6565 27.0394C45.8866 25.8866 43.3927 24.3844 40.4974 22.978C34.6564 20.1405 27.4046 17.8034 21.1853 19.2266L21.0193 19.2682C20.6776 19.3536 20.3877 19.4261 20.1399 19.5171L20.1225 19.5235L20.1048 19.5293C17.6631 20.3263 16.1341 21.8339 15.5155 23.5318C14.8997 25.2222 15.1285 27.2564 16.5089 29.2452C17.2625 30.3308 18.2761 31.2823 19.46 32.0592Z M41.5714 52.075L41.5772 52.087L41.5651 52.0849L41.5714 52.075Z M47.1224 40.0146L42.5493 48.2321C42.4667 46.8997 41.9832 45.5181 41.0622 44.1913C37.9723 39.7398 31.1166 37.5303 25.7414 39.2849L7.16602 45.3854L7.25332 45.1164L12.6666 35.3578L32.3681 28.9194C34.8201 28.119 37.6546 28.2128 40.2542 29.0469C42.8556 29.8816 45.136 31.4297 46.5284 33.4354C48.133 35.7496 48.1942 38.1212 47.1224 40.0146Z M9.07091 46.9944C8.63742 46.7131 8.24774 46.4532 7.90665 46.2212L26.0604 40.2592C28.5122 39.4591 31.3463 39.5529 33.9456 40.387C36.5471 41.2218 38.8277 42.77 40.2201 44.7759C41.6005 46.7647 41.8293 48.7989 41.2135 50.4893C40.5949 52.1872 39.0659 53.6948 36.6242 54.4918L36.6065 54.4976L36.5891 54.504C36.3413 54.595 36.0514 54.6675 35.7097 54.7529L35.5437 54.7946C29.3243 56.2177 22.0725 53.8806 16.231 51.047C13.3356 49.6424 10.8415 48.1431 9.07091 46.9944Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">SolidJS</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/vue.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M43.0532 13.4531H50.1147L30.2756 47.8158L10.4365 13.4531H17.4978L30.2755 35.5845L43.0532 13.4531ZM42.1764 13.4531L30.2755 34.0659L18.3746 13.4531L25.6939 13.4531L30.2756 21.3888L34.8572 13.4531L42.1764 13.4531Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Vue</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/nuxtjs.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M32.5784 45.4741H50.2199C50.7802 45.4741 51.3307 45.3325 51.8159 45.0634C52.3012 44.7943 52.7041 44.4072 52.9842 43.9409C53.2642 43.4748 53.4115 42.946 53.4113 42.4078C53.4111 41.8696 53.2633 41.3409 52.9828 40.875L41.1352 21.164C40.8552 20.6979 40.4524 20.3109 39.9672 20.0418C39.4821 19.7727 38.9317 19.631 38.3715 19.631C37.8113 19.631 37.261 19.7727 36.7758 20.0418C36.2906 20.3109 35.8878 20.6979 35.6078 21.164L32.5784 26.2073L26.6555 16.3452C26.3753 15.8792 25.9723 15.4922 25.487 15.2232C25.0017 14.9541 24.4513 14.8125 23.8909 14.8125C23.3306 14.8125 22.7802 14.9541 22.2949 15.2232C21.8096 15.4922 21.4066 15.8792 21.1263 16.3452L6.38358 40.875C6.10311 41.3409 5.95532 41.8696 5.95508 42.4078C5.95483 42.946 6.10214 43.4748 6.38219 43.9409C6.66224 44.4072 7.06515 44.7943 7.5504 45.0634C8.03564 45.3325 8.58612 45.4741 9.14645 45.4741H20.2203C24.6079 45.4741 27.8436 43.6229 30.07 40.0113L38.3706 26.2073L47.0599 40.6619H35.4754L32.5784 45.4741ZM20.0398 40.657L12.3116 40.6553L23.8961 21.3836L29.6763 31.0195L25.8062 37.4599C24.3276 39.8032 22.6479 40.657 20.0398 40.657Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Nuxt</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px flex flex-col items-center gap-2 text-center aspect-square justify-center" href="../docs/guides/getting-started/quickstarts/refine.html"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M31.7374 20.9337C32.7027 20.9337 33.6284 21.3132 34.311 21.9887C34.9936 22.6642 35.377 23.5803 35.377 24.5356V39.4661C35.377 40.4214 34.9936 41.3375 34.311 42.013C33.6284 42.6885 32.7027 43.068 31.7374 43.068C30.7721 43.068 29.8463 42.6885 29.1638 42.013C28.4812 41.3375 28.0978 40.4214 28.0978 39.4661V24.5356C28.0978 24.0626 28.1919 23.5942 28.3748 23.1572C28.5577 22.7203 28.8258 22.3232 29.1638 21.9887C29.5018 21.6543 29.903 21.3889 30.3446 21.2079C30.7861 21.0269 31.2594 20.9337 31.7374 20.9337ZM31.7371 27.1915C33.2665 27.1915 34.5063 25.9646 34.5063 24.451C34.5063 22.9375 33.2665 21.7106 31.7371 21.7106C30.2077 21.7106 28.9679 22.9375 28.9679 24.451C28.9679 25.9646 30.2077 27.1915 31.7371 27.1915Z M54.0424 32C54.0424 44.3777 44.0083 54.4118 31.6306 54.4118C19.2529 54.4118 9.21875 44.3777 9.21875 32C9.21875 19.6223 19.2529 9.58813 31.6306 9.58813C44.0083 9.58813 54.0424 19.6223 54.0424 32ZM31.7374 19.3933C30.36 19.3952 29.0396 19.9376 28.0659 20.9016C27.0921 21.8657 26.5444 23.1726 26.543 24.5356V39.4661C26.543 40.8294 27.0902 42.137 28.0644 43.101C29.0385 44.065 30.3597 44.6066 31.7374 44.6066C33.115 44.6066 34.4362 44.065 35.4104 43.101C36.3845 42.137 36.9318 40.8294 36.9318 39.4661V24.5356C36.9304 23.1726 36.3827 21.8657 35.4089 20.9016C34.4352 19.9376 33.1148 19.3952 31.7374 19.3933Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Refine</span></a></div></div><div class="col-right space-y-6"><h2 class="h2 text-foreground-lighter max-w-sm">Or, start with <span class="text-foreground">Supabase AI Prompts</span> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles inline text-foreground"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg></h2><div class="grid md:grid-cols-2 gap-2"><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Bootstrap Next.js app with Supabase Auth</h3><a class="relative" href="https://supabase.com/docs/guides/getting-started/ai-prompts/nextjs-supabase-auth"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 h-4 not-sr-only stroke-1 opacity-80 transition-opacity group-hover:opacity-100"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div><div class="p-4 relative"><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">1. Install @supabase/supabase-js and @supabase/ssr packages.
2. Set up environment variables.
3. Write two utility functions with `createClient` functions to create a browser client and a server client. 
4. Hook up middleware to refresh auth tokens
</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Writing Supabase Edge Functions</h3><a class="relative" href="https://supabase.com/docs/guides/getting-started/ai-prompts/edge-functions"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 h-4 not-sr-only stroke-1 opacity-80 transition-opacity group-hover:opacity-100"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div><div class="p-4 relative"><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">1. Try to use Web APIs and Deno’s core APIs instead of external dependencies (eg: use fetch instead of Axios, use WebSockets API instead of node-ws)
2. If you are reusing utility methods between Edge Functions, add them to &#x27;supabase/functions/_shared&#x27; and import using a relative path. Do NOT have cross dependencies between Edge Functions.
3. Do NOT use bare specifiers when importing dependecnies. If you need to use an external dependency, make sure it&#x27;s prefixed with either &#x27;npm:&#x27; or &#x27;jsr:&#x27;. For example, &#x27;@supabase/supabase-js&#x27; should be written as &#x27;npm:@supabase/supabase-js&#x27;.
4. For external imports, always define a version. For example, &#x27;npm:@express&#x27; should be written as &#x27;npm:express@4.18.2&#x27;.
5. For external dependencies, importing via &#x27;npm:&#x27; and &#x27;jsr:&#x27; is preferred. Minimize the use of imports from @&#x27;deno.land/x&#x27; , &#x27;esm.sh&#x27; and @&#x27;unpkg.com&#x27; . If you have a package from one of those CDNs, you can replace the CDN hostname with &#x27;npm:&#x27; specifier.
</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Declarative Database Schema</h3><a class="relative" href="https://supabase.com/docs/guides/getting-started/ai-prompts/declarative-database-schema"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 h-4 not-sr-only stroke-1 opacity-80 transition-opacity group-hover:opacity-100"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div><div class="p-4 relative"><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Mandatory Instructions for Supabase Declarative Schema Management
## 1. **Exclusive Use of Declarative Schema**
-**All database schema modifications must be defined within &#x27;.sql&#x27; files located in the &#x27;supabase/schemas/&#x27; directory.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Create RLS policies</h3><a class="relative" href="https://supabase.com/docs/guides/getting-started/ai-prompts/database-rls-policies"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 h-4 not-sr-only stroke-1 opacity-80 transition-opacity group-hover:opacity-100"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div><div class="p-4 relative"><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">You&#x27;re a Supabase Postgres expert in writing row level security policies. Your purpose is to generate a policy with the constraints given by the user. You should first retrieve schema information to write policies for, usually the &#x27;public&#x27; schema.
The output should use the following instructions:

- The generated SQL must be valid SQL.</pre></div></div></div><div class="pt-2"><a class="group/text-link text-foreground-light hover:text-foreground mt-3 block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground" target="_self" href="https://supabase.com/docs/guides/getting-started/ai-prompts"><div class="group flex items-center gap-1"><span class="sr-only">View all prompts about https://supabase.com/docs/guides/getting-started/ai-prompts</span><span>View all prompts</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></div></div></div></div><div id="mcp" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="grid grid-cols-12 lg:gap-16"><div class="col-span-12 pb-8 lg:col-span-5 xl:col-span-5"><h2 class="h2"><div class="text-foreground-lighter">Supabase MCP server works seamlessly with<!-- --> <span class="text-foreground">your favorite AI code editor</span></div></h2><div class="p"><p></p></div><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm leading-4 px-3 py-2 h-[34px] mt-4" href="https://supabase.com/docs/guides/getting-started/mcp"><div class="[&amp;_svg]:h-[18px] [&amp;_svg]:w-[18px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><span class="truncate">Connect your AI tools</span></a><div class="py-8"><div class="grid grid-cols-5 md:grid-cols-6"><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px rounded-lg flex flex-col items-center gap-2 text-center aspect-square justify-center" href="https://supabase.com/docs/guides/getting-started/mcp#cursor"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><mask id="mask0_2981_701" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="80" height="80"><rect x="0.132324" width="60" height="60" fill="url(#pattern0_2981_701)"></rect></mask><g mask="url(#mask0_2981_701)"><rect x="-6.99658" y="-8.91089" width="71.8812" height="68.9109" fill="url(#paint0_linear_2981_701)"></rect></g><defs><pattern id="pattern0_2981_701" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_2981_701" transform="scale(0.000976562)"></use></pattern><linearGradient id="paint0_linear_2981_701" x1="-6.99658" y1="60" x2="61.853" y2="-11.8172" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-default))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-default))"></stop></linearGradient><image id="image0_2981_701" width="1024" height="1024" preserveAspectRatio="none" xlink:href="data:image/png;base64,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"></image></defs></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Cursor</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px rounded-lg flex flex-col items-center gap-2 text-center aspect-square justify-center" href="https://supabase.com/docs/guides/getting-started/mcp#visual-studio-code-copilot"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M50.1467 13.5721C50.2105 13.572 50.2743 13.5724 50.3382 13.576C50.3414 13.5762 50.3447 13.5768 50.3479 13.577C50.4258 13.5816 50.5036 13.5892 50.5813 13.5995C50.5895 13.6006 50.5976 13.6022 50.6057 13.6034C50.9388 13.6498 51.2689 13.7462 51.5833 13.8983L62.4924 19.1756C63.5692 19.6966 64.2777 20.757 64.3596 21.9442C64.3653 22.0231 64.3684 22.1026 64.3684 22.1825V22.3104C64.3684 22.301 64.3676 22.2914 64.3674 22.2821V57.8417C64.3675 57.834 64.3684 57.8259 64.3684 57.8182V57.9461C64.3684 57.9598 64.3666 57.9736 64.3665 57.9872C64.354 59.2535 63.6289 60.4044 62.4924 60.954L51.5833 66.2303C51.194 66.4187 50.7811 66.5227 50.3674 66.5497C50.3525 66.5507 50.3375 66.5518 50.3225 66.5526C50.2401 66.5568 50.1577 66.5585 50.0755 66.5565C49.6814 66.5509 49.2901 66.476 48.9221 66.3319C48.5051 66.1688 48.1177 65.918 47.7874 65.5858L26.9163 46.4471L17.8372 53.3749C17.4137 53.6981 16.9059 53.8466 16.4055 53.8241H16.3743C15.8739 53.8018 15.3809 53.6085 14.9876 53.2489L12.0706 50.5809C11.1081 49.7012 11.1073 48.1798 12.0686 47.2987L19.9573 40.0643L12.0676 32.8299C11.1064 31.9489 11.108 30.4273 12.0706 29.5477L14.9876 26.8797C15.3809 26.5201 15.8739 26.3269 16.3743 26.3045H16.594C17.032 26.3224 17.4668 26.4713 17.8372 26.7538L26.9163 33.6815L47.7874 14.5428C47.9113 14.4183 48.0433 14.3052 48.1819 14.204C48.7277 13.8051 49.3759 13.5895 50.0354 13.5721H50.0715C50.0966 13.5716 50.1217 13.5721 50.1467 13.5721ZM35.2825 40.0643L51.0969 52.1307V27.9969L35.2825 40.0643Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Visual Studio Code (Copilot)</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px rounded-lg flex flex-col items-center gap-2 text-center aspect-square justify-center" href="https://supabase.com/docs/guides/getting-started/mcp#claude-code"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M22.1027 49.8962L33.9052 43.2734L34.1027 42.6962L33.9052 42.3772H33.328L31.3534 42.2557L24.609 42.0734L18.7609 41.8304L13.0951 41.5266L11.6673 41.2228L10.3306 39.4608L10.4673 38.5797L11.6673 37.7747L13.3837 37.9266L17.1812 38.1848L22.8774 38.5797L27.009 38.8228L33.1306 39.4608H34.1027L34.2394 39.0658L33.9052 38.8228L33.647 38.5797L27.7534 34.5848L21.3736 30.362L18.0318 27.9316L16.2242 26.7013L15.3128 25.5468L14.9179 23.0253L16.5584 21.2177L18.7609 21.3696L19.323 21.5215L21.5559 23.238L26.3255 26.9291L32.5534 31.5165L33.4647 32.276L33.8293 32.0177L33.8749 31.8354L33.4647 31.1519L30.0774 25.0304L26.4622 18.8025L24.8521 16.2203L24.4268 14.6709C24.2749 14.0329 24.1685 13.5013 24.1685 12.8481L26.0369 10.3114L27.0698 9.97722L29.5609 10.3114L30.609 11.2228L32.1584 14.762L34.6647 20.3367L38.5534 27.9165L39.6926 30.1646L40.3002 32.2456L40.528 32.8835H40.923V32.519L41.242 28.2506L41.8344 23.0101L42.4116 16.2658L42.609 14.3671L43.5508 12.0886L45.4192 10.8582L46.8774 11.557L48.0774 13.2734L47.9103 14.3823L47.1964 19.0152L45.7989 26.276L44.8875 31.1367H45.4192L46.0268 30.5291L48.4875 27.2633L52.6192 22.0987L54.442 20.0481L56.5685 17.7848L57.9356 16.7063H60.5179L62.4166 19.5316L61.566 22.4481L58.9078 25.8203L56.7052 28.676L53.5458 32.9291L51.5711 36.3316L51.7534 36.6051L52.2242 36.5595L59.3635 35.0405L63.2217 34.3418L67.8242 33.5519L69.9053 34.5241L70.1331 35.5114L69.3128 37.5316L64.3913 38.7468L58.6192 39.9013L50.0217 41.9367L49.9154 42.0127L50.0369 42.1646L53.9103 42.5291L55.566 42.6203H59.6217L67.1711 43.1823L69.1458 44.4886L70.3306 46.0835L70.1331 47.2987L67.0951 48.8481L62.9939 47.876L53.4242 45.5975L50.1432 44.7772H49.6875V45.0506L52.4217 47.7241L57.4344 52.2506L63.7078 58.0835L64.0268 59.5266L63.2217 60.6658L62.3711 60.5443L56.8571 56.3975L54.7306 54.5291L49.9154 50.4734H49.5964V50.8987L50.7052 52.5241L56.5685 61.3342L56.8723 64.038L56.447 64.919L54.928 65.4506L53.2571 65.1468L49.8242 60.3316L46.285 54.9089L43.4293 50.0481L43.0799 50.2456L41.3939 68.3975L40.604 69.3241L38.7812 70.0228L37.2622 68.8684L36.4571 67L37.2622 63.3089L38.2344 58.4937L39.0242 54.6658L39.7382 49.9114L40.1635 48.3316L40.1331 48.2253L39.7837 48.2709L36.1989 53.1924L30.7458 60.5595L26.4318 65.1772L25.3989 65.5873L23.6065 64.6608L23.7736 63.0051L24.7761 61.5316L30.7458 53.9367L34.3458 49.2279L36.6698 46.5089L36.6546 46.1139H36.5179L20.6597 56.4127L17.8344 56.7772L16.6192 55.638L16.7711 53.7696L17.3483 53.162L22.1179 49.881L22.1027 49.8962Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Claude</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px rounded-lg flex flex-col items-center gap-2 text-center aspect-square justify-center" href="https://supabase.com/docs/guides/getting-started/mcp#windsurf"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M70.1801 22.6639H69.6084C66.5989 22.6592 64.1571 25.0966 64.1571 28.1059V40.2765C64.1571 42.7069 62.1485 44.6756 59.7579 44.6756C58.3377 44.6756 56.9197 43.9607 56.0785 42.7608L43.6475 25.0076C42.6163 23.5334 40.9384 22.6545 39.1219 22.6545C36.2885 22.6545 33.7386 25.0638 33.7386 28.0379V40.2788C33.7386 42.7092 31.7465 44.6779 29.3395 44.6779C27.9146 44.6779 26.499 43.9631 25.6576 42.7631L11.748 22.8983C11.434 22.4506 10.7285 22.6709 10.7285 23.2193V33.8338C10.7285 34.3705 10.8926 34.8908 11.1996 35.3314L24.8866 54.8798C25.6952 56.0352 26.8881 56.893 28.2638 57.2047C31.7066 57.9876 34.8752 55.3369 34.8752 51.9596V39.7258C34.8752 37.2954 36.844 35.3267 39.2742 35.3267H39.2812C40.7462 35.3267 42.1196 36.0415 42.9609 37.2414L55.3916 54.9924C56.4251 56.4688 58.0166 57.3455 59.9149 57.3455C62.8116 57.3455 65.2935 54.9336 65.2935 51.962V39.7234C65.2935 37.293 67.2622 35.3243 69.6927 35.3243H70.1777C70.4825 35.3243 70.7285 35.0783 70.7285 34.7736V23.2123C70.7285 22.9076 70.4825 22.6615 70.1777 22.6615L70.1801 22.6639Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Windsurf</span></a><a class="group relative p-4 transition-colors duration-200 hover:bg-surface-100 -m-px rounded-lg flex flex-col items-center gap-2 text-center aspect-square justify-center" href="https://supabase.com/docs/guides/getting-started/mcp#visual-studio-code-copilot"><div class="text-foreground-lighter group-hover:text-foreground transition-colors"><svg width="45" height="45" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M40.6646 10C42.5072 10 44.2747 10.7322 45.5776 12.0352C46.8803 13.338 47.6118 15.1049 47.6118 16.9473C47.6118 18.0072 47.3683 19.0415 46.9146 19.9775H53.1167C59.9917 19.9775 65.5669 25.5779 65.5669 32.4854V36.6523L69.1919 43.8926C69.3687 44.2454 69.4603 44.6347 69.4595 45.0293C69.4586 45.424 69.3654 45.813 69.187 46.165L65.5669 53.3252V57.4951C65.5668 64.4001 59.9917 70 53.1167 70H28.2144C21.337 69.9998 15.7652 64.4 15.7651 57.4951V53.3252L12.065 46.1875C11.8788 45.8299 11.7811 45.4325 11.7798 45.0293C11.7786 44.6263 11.8734 44.2288 12.0571 43.8701L15.7622 36.6523V32.4854C15.7622 25.5779 21.3374 19.9775 28.2124 19.9775H34.4146C33.9609 19.0416 33.7173 18.0071 33.7173 16.9473C33.7174 15.1048 34.4496 13.338 35.7525 12.0352C37.0553 10.7323 38.8221 10.0001 40.6646 10ZM49.5073 34C47.9996 34 46.553 34.5989 45.4868 35.665C44.4209 36.7311 43.8219 38.1771 43.8218 39.6846V49.79C43.8218 51.2976 44.4209 52.7435 45.4868 53.8096C46.553 54.8757 47.9996 55.4746 49.5073 55.4746C51.015 55.4746 52.4608 54.8757 53.5269 53.8096C54.593 52.7434 55.1919 51.2978 55.1919 49.79V39.6846C55.1918 38.9379 55.0451 38.1986 54.7593 37.5088C54.4734 36.8189 54.054 36.192 53.5259 35.6641C52.9978 35.1362 52.3711 34.7172 51.6812 34.4316C50.9912 34.1461 50.2541 33.9997 49.5073 34ZM31.1919 34C29.6843 34.0001 28.2385 34.599 27.1724 35.665C26.1063 36.7311 25.5075 38.177 25.5073 39.6846V49.79C25.5385 51.2768 26.1509 52.692 27.2134 53.7324C28.2759 54.7729 29.7038 55.3555 31.1909 55.3555C32.678 55.3555 34.106 54.7729 35.1685 53.7324C36.2309 52.692 36.8433 51.2768 36.8745 49.79V39.6846C36.8744 38.1774 36.276 36.732 35.2105 35.666C34.1449 34.5999 32.6992 34.0007 31.1919 34Z" fill="currentColor"></path></svg></div><span class="sr-only text-sm font-medium text-foreground-light group-hover:text-foreground transition-colors">Cline</span></a></div></div></div><div class="sbui-tabs--alt col-span-12 lg:col-span-7 xl:col-span-6 xl:col-start-7"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class="flex space-x-1 overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:R3akla6:-content-0" data-state="active" id="radix-:R3akla6:-trigger-0" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-selection text-foreground border-stronger " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>macOS</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R3akla6:-content-1" data-state="inactive" id="radix-:R3akla6:-trigger-1" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Windows</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R3akla6:-content-2" data-state="inactive" id="radix-:R3akla6:-trigger-2" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Windows (WSL)</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R3akla6:-content-3" data-state="inactive" id="radix-:R3akla6:-trigger-3" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Linux</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R3akla6:-trigger-0" id="radix-:R3akla6:-content-0" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><span></span></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R3akla6:-trigger-1" hidden="" id="radix-:R3akla6:-content-1" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R3akla6:-trigger-2" hidden="" id="radix-:R3akla6:-content-2" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R3akla6:-trigger-3" hidden="" id="radix-:R3akla6:-content-3" tabindex="0" class="focus:outline-none transition-height "></div></div><div class="overflow-hidden"><div class="swiper" style="z-index:0;margin-right:1px"><div class="swiper-wrapper"><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div></div></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="https://supabase.com/solutions/switch-from-neon"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="https://supabase.com/solutions/enterprise"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="https://supabase.com/support"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="https://supabase.com/partners"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="https://supabase.com/partners/integrations"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="https://supabase.com/brand-assets"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="https://supabase.com/legal/dpa"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="https://supabase.com/ui"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="https://supabase.com/changelog"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="https://supabase.com/careers"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="https://supabase.com/open-source"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="https://supabase.com/supasquad"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="https://supabase.com/rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="https://supabase.com/company"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="https://supabase.com/ga"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="https://supabase.com/terms"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="https://supabase.com/privacy"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="https://supabase.com/aup"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="https://supabase.com/support-policy"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="https://supabase.com/sla"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="https://supabase.com/humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="https://supabase.com/lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="https://supabase.com/.well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/solutions/postgres-developers","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"dynamicIds":[50693,33714,69038,7416,8101,1825,14411,79918,64427,75610,24787,45748,20631],"scriptLoader":[]}</script></body></html>