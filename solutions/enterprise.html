<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase for Enterprise</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Leading enterprises use Supabase to build faster, better, and more scalable products." data-next-head=""/><meta property="og:title" content="Supabase for Enterprise" data-next-head=""/><meta property="og:description" content="Leading enterprises use Supabase to build faster, better, and more scalable products." data-next-head=""/><meta property="og:url" content="https://supabase.com/enterprise" data-next-head=""/><meta property="og:image" content="/images/enterprise/enterprise-og.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=640&amp;q=75 640w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=750&amp;q=75 750w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=828&amp;q=75 828w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fgood-tape.png&amp;w=3840&amp;q=75 3840w" imageSizes="100vw" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=640&amp;q=75 640w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=750&amp;q=75 750w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=828&amp;q=75 828w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fxendit.png&amp;w=3840&amp;q=75 3840w" imageSizes="100vw" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=640&amp;q=75 640w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=750&amp;q=75 750w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=828&amp;q=75 828w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2Fimages%2Fcustomers%2Flogos%2Fchatbase.png&amp;w=3840&amp;q=75 3840w" imageSizes="100vw" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/593.d2473f38b8907d22.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5525.56b24ae1ff8557be.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7168.d03b059ebde0ad20.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1714.f35b862499fe48fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1194.49e6cb8481729c6f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7387.d11901457c413b2b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3236-c5f33d032da9564e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/solutions/enterprise-5b00e106f23e651c.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen overflow-visible"><div class="absolute inset-0 z-20 h-full w-full pointer-events-none"><nav class="sticky z-30 flex items-center bg-background/90 w-full border-b backdrop-blur-sm pointer-events-auto top-[65px]"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !p-2 flex items-start md:hidden"><button data-size="tiny" type="button" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 h-[26px] w-full min-w-[200px] flex justify-between items-center py-2" id="radix-:R2hkla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed"> <span class="truncate">Enterprise</span> <div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 hidden md:flex gap-3 items-center"><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="switch-from-neon.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left-right h-4 w-4"><path d="M8 3 4 7l4 4"></path><path d="M4 7h16"></path><path d="m16 21 4-4-4-4"></path><path d="M20 17H4"></path></svg><p>Switch From Neon</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="startups.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-4 w-4"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg><p>Startups</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b text-sm hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600 border-foreground-light text-foreground" href="enterprise.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2 h-4 w-4"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path><path d="M10 6h4"></path><path d="M10 10h4"></path><path d="M10 14h4"></path><path d="M10 18h4"></path></svg><p>Enterprise</p></a></div></nav></div><div class="h-[53px] not-sr-only"></div><div class="w-full max-w-full relative mx-auto py-16 lg:py-24 overflow-hidden [&amp;_h1]:2xl:!text-5xl bg-default border-0 lg:pb-8 [&amp;_.ph-footer]:mt-0 [&amp;_.ph-footer]:lg:mt-16 [&amp;_.ph-footer]:xl:mt-32"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 grid grid-cols-12 lg:gap-4"><div class="relative z-10 col-span-12 gap-8 lg:col-span-5"><div><div class="mb-4 flex items-center gap-3"><span class="text-brand-600 dark:text-brand font-mono uppercase">Supabase for Enterprise</span></div><h1 class="h1 text-3xl md:!text-4xl lg:!text-4xl 2xl:!text-6xl tracking-[-.15px]">Innovative Enterprises<span class="block">use Supabase</span></h1></div><div class="mb-4 md:mb-8"><p class="p lg:text-lg max-w-lg lg:max-w-none">Leading enterprises use Supabase to build faster, better, and more scalable products. From GitHub to PwC, innovative companies trust Supabase to drive their digital transformation strategy.</p></div><div class="flex flex-row md:flex-row md:items-center gap-2 mt-2"></div><div class="ph-footer relative z-10 mt-4 md:mt-8 lg:mt-20 xl:mt-32 col-span-12"><div class="flex lg:grid grid-cols-2 xl:flex flex-nowrap gap-4 md:gap-8 lg:gap-4 2xl:gap-8 lg:max-w-xs xl:max-w-none"><div class="h-12 lg:h-12 w-max"><img src="../images/logos/publicity/github.svg" alt="github" class=" w-auto block h-10 !min-h-10 md:h-12 md:!min-h-12 lg:h-11 lg:!min-h-11 2xl:h-12 2xl:!min-h-12 " draggable="false"/></div><div class="h-12 lg:h-12 w-max"><img src="../images/logos/publicity/mozilla.svg" alt="mozilla" class=" w-auto block h-10 !min-h-10 md:h-12 md:!min-h-12 lg:h-11 lg:!min-h-11 2xl:h-12 2xl:!min-h-12 " draggable="false"/></div><div class="h-12 lg:h-12 w-max"><img src="../images/logos/publicity/1password.svg" alt="1password" class=" w-auto block h-10 !min-h-10 md:h-12 md:!min-h-12 lg:h-11 lg:!min-h-11 2xl:h-12 2xl:!min-h-12 " draggable="false"/></div><div class="h-12 lg:h-12 w-max"><img src="../images/logos/publicity/pwc.svg" alt="pwc" class=" w-auto block h-10 !min-h-10 md:h-12 md:!min-h-12 lg:h-11 lg:!min-h-11 2xl:h-12 2xl:!min-h-12 " draggable="false"/></div><div class="h-12 lg:h-12 w-max"><img src="../images/logos/publicity/langchain.svg" alt="langchain" class=" w-auto block h-10 !min-h-10 md:h-12 md:!min-h-12 lg:h-11 lg:!min-h-11 2xl:h-12 2xl:!min-h-12 " draggable="false"/></div></div></div></div><div class="image-container relative min-h-[300px] col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"><div class="flex flex-col gap-4 w-full items-center justify-center min-h-[300px]"><div class="border rounded-xl bg-surface-75 p-4 md:p-6 w-full lg:max-w-lg min-h-[200px] md:min-h-[400px]"><form id="support-form" class="flex flex-col lg:grid lg:grid-cols-2 gap-4"><div class="flex flex-col col-span-full gap-y-2 md:col-span-1"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="firstName">First Name<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="firstName" name="firstName" placeholder="First Name" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2 md:col-span-1"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="secondName">Last Name<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="secondName" name="secondName" placeholder="Last Name" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="companyEmail">Company Email<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="companyEmail" name="companyEmail" placeholder="Company Email" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2 [&amp;_textarea]:min-h-[100px] [&amp;_textarea]:bg-foreground/[.026]"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="message">What are you interested in?<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><textarea class="flex min-h-10 w-full rounded-md border border-control px-3 py-2 text-base md:text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 bg-control" type="text" id="message" name="message" placeholder="Share more about what you want to accomplish"></textarea></div><input type="text" name="honeypot" style="display:none" aria-hidden="true" value=""/><div data-orientation="horizontal" role="none" class="shrink-0 bg-border-muted h-[1px] w-full col-span-full"></div><button data-size="small" type="submit" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 w-full flex items-center justify-center text-sm leading-4 px-3 py-2 h-[34px] col-span-full"> <span class="truncate">Request a demo</span> </button><p class="text-foreground-lighter text-sm col-span-full">By submitting this form, I confirm that I have read and understood the<!-- --> <a class="text-foreground hover:underline" href="../privacy.html">Privacy Policy</a>.</p></form></div><p class="text-foreground-lighter text-sm"><a class="text-foreground hover:underline" href="../support.html">Contact support</a> <!-- -->if you need technical help</p></div></div></div></div><section id="use-cases"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 md:gap-8 !pb-0"><div class="flex flex-col gap-2"><span class="label">Build with Supabase</span><h2 class="h2">Stay on the forefront</h2></div></div><div class="overflow-hidden"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-4"><ul class="hidden xl:flex flex-col gap-4 md:flex-row items-stretch w-full h-auto min-h-[300px]"><li class="w-full"><a target="_blank" class="w-full h-full" href="https://developer.mozilla.org/en-US/blog/introducing-ai-help/"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Mozilla use Supabase for GenAI and RAG" loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/mozilla.png 1x, ../_next/mozilla.png 2x" src="../_next/mozilla.png"/><h3 class="text-foreground">Mozilla use Supabase for GenAI and RAG</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block">We store embeddings in a PostgreSQL database, hosted by Supabase, to perform a<!-- --> <span class="text-foreground">similarity search to identify the most relevant sections within the MDN</span>.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></li><li class="w-full"><a class="w-full h-full" href="../customers/epsilon3.html"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Epsilon3 use Supabase to build software for NASA" loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/epsilon3.png 1x, ../_next/epsilon3.png 2x" src="../_next/epsilon3.png"/><h3 class="text-foreground">Epsilon3 use Supabase to build software for NASA</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block"><span class="text-foreground">Billion dollar missions need to run reliably and securely</span>. We use Supabase because they give us an open-source scalable back-end built by database experts that we can self-host.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></li><li class="w-full"><a class="w-full h-full" href="../customers/pebblely.html"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Pebblely use Supabase to scale to millions of users." loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/pebblely.png 1x, ../_next/pebblely.png 2x" src="../_next/pebblely.png"/><h3 class="text-foreground">Pebblely use Supabase to scale to millions of users.</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block">It streamlined the database, the API, and authentication.<!-- --> <span class="text-foreground">Everything was up and running in two days</span>. It is easy to get started and provides all the solutions we require as we continue to grow.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></li></ul><div class="xl:hidden"><div class="swiper h-[300px] w-full !overflow-visible" style="z-index:0;margin-right:1px"><div class="swiper-wrapper"><div class="swiper-slide"><a target="_blank" class="w-full h-full" href="https://developer.mozilla.org/en-US/blog/introducing-ai-help/"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Mozilla use Supabase for GenAI and RAG" loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/mozilla.png 1x, ../_next/mozilla.png 2x" src="../_next/mozilla.png"/><h3 class="text-foreground">Mozilla use Supabase for GenAI and RAG</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block">We store embeddings in a PostgreSQL database, hosted by Supabase, to perform a<!-- --> <span class="text-foreground">similarity search to identify the most relevant sections within the MDN</span>.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></div><div class="swiper-slide"><a class="w-full h-full" href="../customers/epsilon3.html"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Epsilon3 use Supabase to build software for NASA" loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/epsilon3.png 1x, ../_next/epsilon3.png 2x" src="../_next/epsilon3.png"/><h3 class="text-foreground">Epsilon3 use Supabase to build software for NASA</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block"><span class="text-foreground">Billion dollar missions need to run reliably and securely</span>. We use Supabase because they give us an open-source scalable back-end built by database experts that we can self-host.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></div><div class="swiper-slide"><a class="w-full h-full" href="../customers/pebblely.html"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-2"><div class="flex flex-col justify-between gap-6 p-3 md:max-w-[230px]"><img alt="Pebblely use Supabase to scale to millions of users." loading="lazy" width="150" height="30" decoding="async" data-nimg="1" class="max-h-[23px] max-w-[150px] w-auto object-contain object-left-bottom filter invert dark:invert-0 opacity-60" style="color:transparent" srcSet="../_next/pebblely.png 1x, ../_next/pebblely.png 2x" src="../_next/pebblely.png"/><h3 class="text-foreground">Pebblely use Supabase to scale to millions of users.</h3></div><div class="p-3 bg-surface-200 rounded-lg"><q class="text-sm block">It streamlined the database, the API, and authentication.<!-- --> <span class="text-foreground">Everything was up and running in two days</span>. It is easy to get started and provides all the solutions we require as we continue to grow.</q></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></div></div></div></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !pt-0"><ul class="grid grid-cols-2 gap-4 sm:gap-10 gap-y-10 lg:grid-cols-4 md:gap-12 lg:gap-x-8 mt-8"><li class="text-foreground text-sm max-w-[250px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-line stroke-1 mb-2"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="m19 9-5 5-4-4-3 3"></path></svg><h4 class="text-foreground text-xl lg:text-2xl">100x scale</h4><p class="text-foreground-lighter text-sm">Maergo handled 100x their highest sustained traffic with Supabase.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-4" target="_self" href="../customers/maergo.html"><div class="group flex items-center gap-1"><span class="sr-only">Read story about /customers/maergo</span><span>Read story</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></li><li class="text-foreground text-sm max-w-[250px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-dollar-sign stroke-1 mb-2"><line x1="12" x2="12" y1="2" y2="22"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg><h4 class="text-foreground text-xl lg:text-2xl">83% cost reduction</h4><p class="text-foreground-lighter text-sm">Shotgun reduced costs by 83% by migrating to Supabase.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-4" target="_self" href="../customers/shotgun.html"><div class="group flex items-center gap-1"><span class="sr-only">Read story about /customers/shotgun</span><span>Read story</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></li><li class="text-foreground text-sm max-w-[250px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-timer stroke-1 mb-2"><line x1="10" x2="14" y1="2" y2="2"></line><line x1="12" x2="15" y1="14" y2="11"></line><circle cx="12" cy="14" r="8"></circle></svg><h4 class="text-foreground text-xl lg:text-2xl">20% faster dev</h4><p class="text-foreground-lighter text-sm">Voypost enjoyed a 20% faster development process.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-4" target="_self" href="../customers/voypost.html"><div class="group flex items-center gap-1"><span class="sr-only">Read story about /customers/voypost</span><span>Read story</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></li><li class="text-foreground text-sm max-w-[250px]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scale stroke-1 mb-2"><path d="m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"></path><path d="m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"></path><path d="M7 21h10"></path><path d="M12 3v18"></path><path d="M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2"></path></svg><h4 class="text-foreground text-xl lg:text-2xl">GDPR compliance</h4><p class="text-foreground-lighter text-sm">Markprompt use Supabase to build GDPR-compliant AI chatbots.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-4" target="_self" href="../customers/markprompt.html"><div class="group flex items-center gap-1"><span class="sr-only">Read story about /customers/markprompt</span><span>Read story</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></li></ul></div></section><div id="performance" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 relative"><div class="relative z-10 flex flex-col gap-4 md:gap-8 pb-20"><div class="flex flex-col gap-2 max-w-xl"><h2 class="h2 !m-0">Top performance,<br/> at any scale</h2><p class="p !text-foreground-lighter">Supabase ensures optimal database performance at any scale, so you can focus on innovating and growing without worrying about infrastructure limitations—whether you&#x27;re handling high-traffic applications, complex queries, or massive data volumes.</p></div><div class="flex flex-wrap gap-4 md:gap-12"><li class="flex flex-col gap-2 text-sm"><span class="label">Databases managed</span><p class="text-foreground text-xl md:text-3xl">6,500,000+</p></li><li class="flex flex-col gap-2 text-sm"><span class="label">Databases launched daily</span><p class="text-foreground text-xl md:text-3xl">35,000+</p></li></div></div><div class="relative xl:absolute z-0 inset-0 mt-4 -mb-8 sm:mt-0 sm:-mb-20 md:-mt-20 md:-mb-36 xl:mt-0 xl:top-10 w-full aspect-[2.15/1]"><div class="absolute z-10 inset-0 left-auto right-[20%] -top-8 md:top-0 xl:top-[15%] w-fit h-[200px] lg:h-[400px] flex flex-col items-center gap-1"><div class="w-fit text-foreground bg-alternative p-4 rounded-lg border flex flex-col gap-1"><span class="label !text-[10px] !leading-3">Users</span><div class="flex items-center gap-2"><span class="text-foreground-light text-2xl">930,550</span><div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 py-0.5 text-xs h-[24px] px-2">+13.4%</div></div></div><div class="relative w-2 h-2 min-w-2 min-h-2 rounded-full border-2 border-stronger after:absolute after:inset-0 after:top-full after:mx-auto after:w-[2px] after:h-[150px] after:lg:h-[250px] after:bg-gradient-to-b after:from-border-stronger after:to-transparent"></div></div><svg width="100%" height="100%" viewBox="0 0 1403 599" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute inset-0 w-full h-full"><path d="M1402.27 0.744141C896.689 410.854 286.329 492.876 0.476562 492.876V598.744H1402.27V0.744141Z" fill="url(#paint0_linear_585_9420)"></path><path d="M11.4209 492.744C295.041 492.744 900.636 410.744 1402.27 0.744141" stroke="hsl(var(--foreground-lighter))"></path><defs><linearGradient id="paint0_linear_585_9420" x1="701.374" y1="170.846" x2="701.374" y2="561.839" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--border-overlay))"></stop><stop offset="1" stop-color="hsl(var(--border-overlay))" stop-opacity="0"></stop></linearGradient></defs></svg><div class="absolute inset-0 w-full h-full bg-[radial-gradient(50%_50%_at_50%_50%,_transparent_0%,_hsl(var(--background-default))_100%)]"></div></div></div><div id="security" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col xl:flex-row justify-between gap-4 md:gap-8"><div class="flex flex-col gap-2 max-w-xl"><span class="label">Security</span><h2 class="h2 !m-0">Trusted for medical records, missions to the moon, and everything in between</h2><p class="p !text-foreground-lighter">Keep your data secure with SOC 2, HIPAA, and GDPR compliance. Your customers’ data is encrypted at rest and in transit, with built-in tools for monitoring and managing security threats.</p><a class="group/text-link text-foreground-light hover:text-foreground block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground mt-2" target="_self" href="../security.html"><div class="group flex items-center gap-1"><span class="sr-only">Learn more about Security about /security</span><span>Learn more about Security</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></div><ul class="grid grid-cols-2 sm:grid-cols-2 gap-4 md:gap-x-20 h-fit xl:grid-cols-2 mt-4 xl:mt-8"><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-check w-4 h-4 stroke-1"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="m9 12 2 2 4-4"></path></svg></figure><p>SOC 2 Type II certified</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity w-4 h-4 stroke-1"><path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg></figure><p>HIPAA compliant</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-alert w-4 h-4 stroke-1"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg></figure><p>DDoS Protection</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-4 h-4 stroke-1"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></figure><p>Multi-factor Authentication</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check w-4 h-4 stroke-1"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><path d="m9 14 2 2 4-4"></path></svg></figure><p>Vulnerability Management</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 stroke-1"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></figure><p>Role-based access control</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-list w-4 h-4 stroke-1"><rect width="7" height="7" x="3" y="3" rx="1"></rect><rect width="7" height="7" x="3" y="14" rx="1"></rect><path d="M14 4h7"></path><path d="M14 9h7"></path><path d="M14 15h7"></path><path d="M14 20h7"></path></svg></figure><p>Database Audit Logs</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lightbulb w-4 h-4 stroke-1"><path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d="M9 18h6"></path><path d="M10 22h4"></path></svg></figure><p>Security Advisors</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-lock w-4 h-4 stroke-1"><rect width="8" height="5" x="14" y="17" rx="1"></rect><path d="M10 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v2.5"></path><path d="M20 17v-2a2 2 0 1 0-4 0v2"></path></svg></figure><p>Encrypted Storage</p></li><li class="flex flex-nowrap items-center h-fit gap-2 sm:gap-4 text-foreground-light text-xs sm:text-sm"><figure class="border not-prose bg-surface-100 flex h-8 w-8 items-center justify-center rounded-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-x w-4 h-4 stroke-1"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><line x1="17" x2="22" y1="8" y2="13"></line><line x1="22" x2="17" y1="8" y2="13"></line></svg></figure><p>Network restrictions</p></li></ul></div><div id="support" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 md:gap-8"><div class="flex flex-col gap-2"><span class="label">Support</span><h2 class="h2">Get expert help,<br/> whenever you need it</h2></div><ul class="grid grid-cols-1 gap-4 gap-y-10 md:grid-cols-3 md:gap-12 xl:gap-20"><li class="flex flex-col gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-earth stroke-1 mb-2 text-foreground-lighter w-7 h-7"><path d="M21.54 15H17a2 2 0 0 0-2 2v4.54"></path><path d="M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17"></path><path d="M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05"></path><circle cx="12" cy="12" r="10"></circle></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Global Support, 24/7</h4><p class="text-foreground-lighter text-sm">Our team has 100% global coverage. No matter where you are, we’re always available to resolve issues and keep your operations running smoothly.</p></li><li class="flex flex-col gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users stroke-1 mb-2 text-foreground-lighter w-7 h-7"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Dedicated team of experts</h4><p class="text-foreground-lighter text-sm">Get direct access to talented engineers. From onboarding to optimizations, our expert team is here to provide personalized, hands-on support whenever you need it.</p></li><li class="flex flex-col gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left-right stroke-1 mb-2 text-foreground-lighter w-7 h-7"><path d="M8 3 4 7l4 4"></path><path d="M4 7h16"></path><path d="m16 21 4-4-4-4"></path><path d="M20 17H4"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Migration &amp; Success Support</h4><p class="text-foreground-lighter text-sm">Our team ensures a smooth transition to Supabase while guiding you with best practices for scaling. We’re dedicated to your long-term success, every step of the way.</p></li></ul></div><div id="quote" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col items-center text-center gap-8 md:gap-12"><q class="text-2xl max-w-xs md:text-3xl md:max-w-xl">Supabase powers prototyping for fast-moving teams such as GitHub Next.</q><div class="flex flex-col items-center gap-1"><figure class="text-foreground-lighter mb-4"><svg width="91" height="24" viewBox="0 0 91 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.4878 10.2692C10.2941 10.2692 10.1376 10.4244 10.1376 10.6166V14.263C10.1376 14.4552 10.2941 14.6129 10.4878 14.6129H13.4204V19.1438C13.4204 19.1438 12.7624 19.3656 10.9422 19.3656C8.79419 19.3656 5.79445 18.587 5.79445 12.0431C5.79445 5.49923 8.91835 4.6369 11.851 4.6369C14.3889 4.6369 15.484 5.08038 16.1793 5.29474C16.3978 5.36126 16.599 5.14444 16.599 4.95227L17.4383 1.42901C17.4383 1.33785 17.4085 1.22944 17.3042 1.15799C17.0211 0.958425 15.2978 2.14502e-06 10.9422 2.14502e-06C5.92606 -0.00246167 0.77832 2.11642 0.77832 12.2969C0.77832 22.4774 6.67103 23.9951 11.6375 23.9951C15.7497 23.9951 18.2429 22.2507 18.2429 22.2507C18.3447 22.194 18.3571 22.0511 18.3571 21.9871V10.6166C18.3571 10.4244 18.2007 10.2692 18.007 10.2692H10.4878Z" fill="currentColor"></path><path d="M56.7404 1.21713C56.7404 1.02248 56.5864 0.867264 56.3927 0.867264H52.1613C51.9676 0.867264 51.8112 1.02495 51.8112 1.21713V9.33046H45.2157V1.21713C45.2157 1.02248 45.0593 0.867264 44.8656 0.867264H40.6342C40.4405 0.867264 40.284 1.02495 40.284 1.21713V23.1894C40.284 23.384 40.4405 23.5417 40.6342 23.5417H44.8656C45.0593 23.5417 45.2157 23.384 45.2157 23.1894V13.7924H51.8112L51.7988 23.1894C51.7988 23.384 51.9552 23.5417 52.1489 23.5417H56.3903C56.584 23.5417 56.7379 23.384 56.7404 23.1894V1.21713Z" fill="currentColor"></path><path d="M25.9906 4.09979C25.9906 2.58701 24.7688 1.36495 23.2615 1.36495C21.7542 1.36495 20.5324 2.58701 20.5324 4.09979C20.5324 5.61257 21.7542 6.83462 23.2615 6.83462C24.7688 6.83462 25.9906 5.6101 25.9906 4.09979Z" fill="currentColor"></path><path d="M25.6876 8.41392C25.6876 8.22174 25.5312 8.06406 25.3375 8.06406H21.1185C20.9248 8.06406 20.7509 8.26117 20.7509 8.45581V22.9874C20.7509 23.4136 21.0191 23.5417 21.3668 23.5417H25.1686C25.5858 23.5417 25.6876 23.3397 25.6876 22.98V8.41392Z" fill="currentColor"></path><path d="M72.8218 8.09609H68.6227C68.429 8.09609 68.2725 8.25377 68.2725 8.44842V19.2227C68.2725 19.2227 67.2047 19.9963 65.6924 19.9963C64.1802 19.9963 63.7754 19.3138 63.7754 17.8429V8.44842C63.7754 8.25377 63.6189 8.09609 63.4253 8.09609H59.164C58.9728 8.09609 58.8139 8.25377 58.8139 8.44842V18.555C58.8139 22.9233 61.2673 23.9926 64.6445 23.9926C67.4133 23.9926 69.6482 22.4749 69.6482 22.4749C69.6482 22.4749 69.755 23.2756 69.8022 23.3693C69.8494 23.4629 69.976 23.559 70.1101 23.559L72.8218 23.5467C73.013 23.5467 73.1719 23.389 73.1719 23.1968V8.44842C73.1719 8.25377 73.013 8.09609 72.8193 8.09609H72.8218Z" fill="currentColor"></path><path d="M84.3018 7.60086C81.9154 7.60086 80.2938 8.65784 80.2938 8.65784V1.21713C80.2938 1.02248 80.1374 0.867264 79.9437 0.867264H75.6999C75.5062 0.867264 75.3497 1.02495 75.3497 1.21713V23.1894C75.3497 23.384 75.5062 23.5417 75.6999 23.5417H78.645C78.7766 23.5417 78.8784 23.4727 78.9529 23.3545C79.0249 23.2362 79.1317 22.3419 79.1317 22.3419C79.1317 22.3419 80.8675 23.9729 84.1528 23.9729C88.0092 23.9729 90.2218 22.0314 90.2218 15.2584C90.2218 8.48537 86.6882 7.60086 84.3018 7.60086ZM82.6455 19.9815C81.1878 19.9372 80.202 19.2818 80.202 19.2818V12.324C80.202 12.324 81.1754 11.7302 82.3723 11.6243C83.8846 11.4888 85.3423 11.9421 85.3423 15.522C85.3423 19.2966 84.6842 20.0407 82.6479 19.9815H82.6455Z" fill="currentColor"></path><path d="M37.8505 8.05913H34.6769C34.6769 8.05913 34.6719 3.90022 34.6719 3.89775C34.6719 3.74007 34.59 3.66123 34.4062 3.66123H30.0804C29.9116 3.66123 29.8222 3.73514 29.8222 3.89529V8.19464C29.8222 8.19464 27.6543 8.71451 27.5078 8.75639C27.3613 8.79828 27.2545 8.93132 27.2545 9.09147V11.7943C27.2545 11.9889 27.411 12.1441 27.6047 12.1441H29.8222V18.6437C29.8222 23.4727 33.2366 23.9458 35.5386 23.9458C36.5915 23.9458 37.8505 23.6107 38.0591 23.5343C38.1857 23.4875 38.2577 23.3594 38.2577 23.219V20.2476C38.2602 20.053 38.0963 19.8978 37.9101 19.8978C37.7238 19.8978 37.252 19.9717 36.7653 19.9717C35.2058 19.9717 34.6769 19.2522 34.6769 18.3209C34.6769 17.3896 34.6769 12.1441 34.6769 12.1441H37.8505C38.0442 12.1441 38.2006 11.9865 38.2006 11.7943V8.409C38.2006 8.21435 38.0442 8.05913 37.8505 8.05913Z" fill="currentColor"></path></svg></figure><span class="text-foreground">Idan Gazit</span><span class="text-foreground-lighter font-mono text-sm">Senior Director of Research, GitHub Next</span></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 text grid gap-8 lg:gap-12 md:grid-cols-2"><div class="lg:pb-8 md:h-full w-full flex flex-col justify-between gap-2"><div class="flex flex-col gap-2 md:max-w-md"><h1 class="h1 !m-0">Request a demo</h1><p class="md:text-lg text-foreground-lighter">We can take your requirements and show you how Supabase can help you achieve your goals.</p></div><div class="flex-col gap-6 w-full hidden md:flex"><div class="text-foreground-lighter text-sm" style="opacity:0"><div class="text-foreground flex text-base lg:text-lg flex-col gap-1 max-w-xs"><p>&quot;<!-- -->My biggest regret is not having gone with Supabase from the beginning.<!-- -->&quot;</p><p class="text-foreground-lighter text-sm">Jakob Steinn, Co-founder &amp; Tech Lead, Good Tape</p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"><div class="absolute motion-reduce:hidden inset-0 w-full right-full bg-brand h-full transition-all opacity-100" style="transform:translateX(-100%)"></div></div><div class="w-full col-span-full flex gap-4 lg:gap-8 xl:gap-10" role="tablist"><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-100" aria-selected="true" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Goodtape logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/good-tape.png 640w, ../_next/good-tape.png 750w, ../_next/good-tape.png 828w, ../_next/good-tape.png 1080w, ../_next/good-tape.png 1200w, ../_next/good-tape.png 1920w, ../_next/good-tape.png 2048w, ../_next/good-tape.png 3840w" src="../_next/good-tape.png"/></div></div></button><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-50" aria-selected="false" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Xendit logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/xendit.png 640w, ../_next/xendit.png 750w, ../_next/xendit.png 828w, ../_next/xendit.png 1080w, ../_next/xendit.png 1200w, ../_next/xendit.png 1920w, ../_next/xendit.png 2048w, ../_next/xendit.png 3840w" src="../_next/xendit.png"/></div></div></button><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-50" aria-selected="false" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Chatbase logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/chatbase.png 640w, ../_next/chatbase.png 750w, ../_next/chatbase.png 828w, ../_next/chatbase.png 1080w, ../_next/chatbase.png 1200w, ../_next/chatbase.png 1920w, ../_next/chatbase.png 2048w, ../_next/chatbase.png 3840w" src="../_next/chatbase.png"/></div></div></button></div></div></div><div class="flex flex-col gap-4 w-full items-center justify-center min-h-[300px]"><div class="border rounded-xl bg-surface-75 p-4 md:p-6 w-full lg:max-w-lg min-h-[200px] md:min-h-[400px]"><form id="support-form" class="flex flex-col lg:grid lg:grid-cols-2 gap-4"><div class="flex flex-col col-span-full gap-y-2 md:col-span-1"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="firstName">First Name<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="firstName" name="firstName" placeholder="First Name" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2 md:col-span-1"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="secondName">Last Name<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="secondName" name="secondName" placeholder="Last Name" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="companyEmail">Company Email<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><input type="text" id="companyEmail" name="companyEmail" placeholder="Company Email" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px]" value=""/></div><div class="flex flex-col col-span-full gap-y-2 [&amp;_textarea]:min-h-[100px] [&amp;_textarea]:bg-foreground/[.026]"><label class="text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-light flex justify-between" for="message">What are you interested in?<div class="flex flex-nowrap text-right gap-1 items-center text-xs leading-none transition-opacity opacity-0 text-foreground-muted"></div></label><textarea class="flex min-h-10 w-full rounded-md border border-control px-3 py-2 text-base md:text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 bg-control" type="text" id="message" name="message" placeholder="Share more about what you want to accomplish"></textarea></div><input type="text" name="honeypot" style="display:none" aria-hidden="true" value=""/><div data-orientation="horizontal" role="none" class="shrink-0 bg-border-muted h-[1px] w-full col-span-full"></div><button data-size="small" type="submit" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 w-full flex items-center justify-center text-sm leading-4 px-3 py-2 h-[34px] col-span-full"> <span class="truncate">Request a demo</span> </button><p class="text-foreground-lighter text-sm col-span-full">By submitting this form, I confirm that I have read and understood the<!-- --> <a class="text-foreground hover:underline" href="../privacy.html">Privacy Policy</a>.</p></form></div><p class="text-foreground-lighter text-sm"><a class="text-foreground hover:underline" href="../support.html">Contact support</a> <!-- -->if you need technical help</p></div><div class="flex flex-col gap-6 w-full md:hidden mt-4"><div class="text-foreground-lighter text-sm" style="opacity:0"><div class="text-foreground flex text-base lg:text-lg flex-col gap-1 max-w-xs"><p>&quot;<!-- -->My biggest regret is not having gone with Supabase from the beginning.<!-- -->&quot;</p><p class="text-foreground-lighter text-sm">Jakob Steinn Co-founder &amp; Tech Lead</p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"><div class="absolute motion-reduce:hidden inset-0 w-full right-full bg-brand h-full transition-all opacity-100" style="transform:translateX(-100%)"></div></div><div class="w-full col-span-full flex gap-4 lg:gap-8 xl:gap-10" role="tablist"><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-100" aria-selected="true" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Goodtape logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/good-tape.png 640w, ../_next/good-tape.png 750w, ../_next/good-tape.png 828w, ../_next/good-tape.png 1080w, ../_next/good-tape.png 1200w, ../_next/good-tape.png 1920w, ../_next/good-tape.png 2048w, ../_next/good-tape.png 3840w" src="../_next/good-tape.png"/></div></div></button><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-50" aria-selected="false" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Xendit logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/xendit.png 640w, ../_next/xendit.png 750w, ../_next/xendit.png 828w, ../_next/xendit.png 1080w, ../_next/xendit.png 1200w, ../_next/xendit.png 1920w, ../_next/xendit.png 2048w, ../_next/xendit.png 3840w" src="../_next/xendit.png"/></div></div></button><button class="text-left text-lg flex flex-col group gap-1 transition-opacity flex-[1] opacity-50" aria-selected="false" role="tab"><div><div class="relative h-8 max-h-5 xl:max-h-6 w-20 max-w-20 md:w-28 xl:max-w-28"><img alt="Chatbase logo" draggable="false" decoding="async" data-nimg="fill" class=" bg-no-repeat m-0 object-left object-contain [[data-theme*=dark]_&amp;]:brightness-200 [[data-theme*=dark]_&amp;]:contrast-0 [[data-theme*=dark]_&amp;]:filter " style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcSet="../_next/chatbase.png 640w, ../_next/chatbase.png 750w, ../_next/chatbase.png 828w, ../_next/chatbase.png 1080w, ../_next/chatbase.png 1200w, ../_next/chatbase.png 1920w, ../_next/chatbase.png 2048w, ../_next/chatbase.png 3840w" src="../_next/chatbase.png"/></div></div></button></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/solutions/enterprise","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"dynamicIds":[40593,15525,24787,11714,1194,87387],"scriptLoader":[]}</script></body></html>