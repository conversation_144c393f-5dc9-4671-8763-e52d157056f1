<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/og/supabase-og.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase for building Apps</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Your app, your vision. Supabase powers the rest." data-next-head=""/><meta property="og:title" content="Supabase for building Apps" data-next-head=""/><meta property="og:description" content="Your app, your vision. Supabase powers the rest." data-next-head=""/><meta property="og:url" content="https://supabase.com/solutions/no-code" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" href="../images/index/products/auth.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/auth-light.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/edge-functions-dark.svg" as="image" data-next-head=""/><link rel="preload" href="../images/index/products/edge-functions-light.svg" as="image" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/89ee5cfe523ce5e4.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/89ee5cfe523ce5e4.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3714.dc4ad7fea361268b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9038.6ba958f0b4341f77.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7416.fc232284fd9a30e8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8101.13e26d6e3f0cdb6f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1825.ae58eb66bb3dd32a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5496.7fa9d86754a7073b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7277.4b96d86d63546bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2985.a676a3152a6bf503.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5748.7a50054c9a9490b7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7894.37210ad6d03a2e60.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/solutions/no-code-b53e6788c0094150.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="min-h-screen overflow-visible relative"><div class="absolute inset-0 z-20 h-full w-full pointer-events-none"><nav class="sticky z-30 flex items-center bg-background/90 w-full border-b backdrop-blur-sm pointer-events-auto top-[65px]"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !p-2 flex items-start md:hidden"><button data-size="tiny" type="button" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 h-[26px] w-full min-w-[200px] flex justify-between items-center py-2" id="radix-:R19kla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed"> <span class="truncate">No Code</span> <div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 hidden md:flex gap-3 items-center"><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="ai-builders.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot h-4 w-4"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg><p>AI Builders</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b text-sm hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600 border-foreground-light text-foreground" href="no-code.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pointer h-4 w-4"><path d="M22 14a8 8 0 0 1-8 8"></path><path d="M18 11v-1a2 2 0 0 0-2-2a2 2 0 0 0-2 2"></path><path d="M14 10V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1"></path><path d="M10 9.5V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v10"></path><path d="M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"></path></svg><p>No Code</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="beginners.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-puzzle h-4 w-4"><path d="M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z"></path></svg><p>Beginners</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="developers.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-xml h-4 w-4"><path d="m18 16 4-4-4-4"></path><path d="m6 8-4 4 4 4"></path><path d="m14.5 4-5 16"></path></svg><p>Developers</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="postgres-developers.html"><svg width="26" height="25" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"><path d="M13.553 14.5434C14.5936 16.6636 16.7499 18.0071 19.1117 18.0073H25.1729C25.5138 18.0076 25.7898 18.2842 25.7898 18.6252C25.7896 18.9659 25.5137 19.2427 25.1729 19.243H22.3584C22.3941 19.3211 22.415 19.4075 22.4151 19.499C22.4151 22.1237 20.0255 23.8922 17.3294 23.8923C14.9326 23.8923 12.924 22.2346 12.3854 20.0036L12.3382 19.7854L12.3267 19.6605C12.3294 19.3734 12.5331 19.1177 12.826 19.0605C13.1604 18.9955 13.4843 19.2139 13.5498 19.5483C13.8955 21.3196 15.4573 22.6565 17.3294 22.6565C19.5686 22.6564 21.1804 21.2361 21.1804 19.499C21.1804 19.4074 21.2012 19.3212 21.237 19.243H19.1117C16.2786 19.2429 13.6923 17.6302 12.4441 15.0868L13.553 14.5434Z" fill="currentColor"></path><path d="M11.9396 0.59251H13.0442C18.2192 0.592582 22.4148 4.78736 22.4151 9.96235V15.3952C22.4151 15.7363 22.1383 16.013 21.7972 16.0131C21.4562 16.0129 21.1804 15.7362 21.1804 15.3952V9.96235C21.1801 5.46961 17.537 1.82728 13.0442 1.82721H11.8137C11.7855 1.8272 11.758 1.82249 11.7308 1.81881H5.92134C5.01109 1.81884 4.19788 2.38681 3.88414 3.24128L1.93402 8.55247C1.59885 9.46536 1.86315 10.4909 2.59804 11.1278L3.93554 12.287C4.51736 12.7912 4.85232 13.5238 4.85239 14.2938V16.4254C4.85236 18.283 6.35795 19.7885 8.21554 19.7885C8.79044 19.7884 9.25714 19.3228 9.25722 18.7479V9.25112C9.25724 7.29523 10.0377 5.41991 11.4266 4.04273C11.6688 3.80262 12.0603 3.80473 12.3004 4.04693C12.5404 4.28914 12.5383 4.67959 12.2962 4.91972C11.1413 6.06493 10.4919 7.62466 10.4919 9.25112V18.7479C10.4918 20.005 9.47269 21.0242 8.21554 21.0243C5.67569 21.0242 3.61661 18.9652 3.61664 16.4254V14.2938C3.61658 13.8821 3.43783 13.4902 3.12675 13.2206L1.7882 12.0614C0.665609 11.0884 0.261983 9.52225 0.7738 8.12762L2.72393 2.81538C3.21621 1.47459 4.49303 0.583094 5.92134 0.583069H11.9396V0.59251Z" fill="currentColor"></path><path d="M12.7263 14.2602C13.0325 14.11 13.4027 14.2372 13.553 14.5434L12.4441 15.0868C12.2941 14.7806 12.4202 14.4104 12.7263 14.2602Z" fill="currentColor"></path><path d="M17.5298 8.99516C18.0167 8.99532 18.4118 9.38945 18.412 9.87633C18.412 10.3634 18.0168 10.7584 17.5298 10.7586C17.0426 10.7586 16.6476 10.3635 16.6476 9.87633C16.6478 9.38935 17.0428 8.99516 17.5298 8.99516Z" fill="currentColor"></path></svg><p>Postgres Devs</p></a></div></nav></div><div class="h-[53px] not-sr-only"></div><div class="w-full max-w-full relative mx-auto py-16 lg:py-24 overflow-hidden [&amp;_h1]:2xl:!text-5xl bg-default border-0 lg:pb-8 [&amp;_.ph-footer]:mt-0 [&amp;_.ph-footer]:lg:mt-16 [&amp;_.ph-footer]:xl:mt-32"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 grid grid-cols-12 lg:gap-4 [&amp;_div:first-child]:lg:col-span-6 [&amp;_div:nth-child(2)]:lg:col-span-6"><div class="relative z-10 col-span-12 gap-8 lg:col-span-5"><div><div class="mb-4 flex items-center gap-3"><span class="text-brand-600 dark:text-brand font-mono uppercase">Supabase for building Apps</span></div><h1 class="h1 text-3xl md:!text-4xl lg:!text-4xl 2xl:!text-6xl tracking-[-.15px]"><span class="block text-foreground">Your app, your vision.</span><span class="block md:ml-0">Supabase powers the rest.</span></h1></div><div class="mb-4 md:mb-8"><p class="p lg:text-lg max-w-lg lg:max-w-none">You don’t need to be a developer to build something incredible. Supabase provides a complete backend that’s easy to use with your favorite app builder tools. When you’re ready to scale, Supabase is there to scale with you. <br/>Build in a weekend, scale to millions.</p></div><div class="flex flex-row md:flex-row md:items-center gap-2 mt-2"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="image-container relative min-h-[300px] col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"></div></div></div><section id="quotes"><div class="overflow-hidden"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-4"><ul class="hidden xl:flex flex-col gap-4 md:flex-row items-stretch w-full h-auto min-h-64"><li class="w-full"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base">We chose Supabase because it&#x27;s<!-- --> <span class="text-foreground">extremely user friendly</span> and<!-- --> <span class="text-foreground">covers all the needs to build full-stack applications</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Anton Osika" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/anton-osika.jpg 1x, ../_next/anton-osika.jpg 2x" src="../_next/anton-osika.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Anton Osika</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Lovable - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></li><li class="w-full"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base">Supabase is awesome. Supabase is the<!-- --> <span class="text-foreground">key database integration</span> that we have...because it’s the<!-- --> <span class="text-foreground">best product in the world for storing and retrieving data</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Eric Simmons" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/eric-simons.jpg 1x, ../_next/eric-simons.jpg 2x" src="../_next/eric-simons.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Eric Simmons</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Bolt.new - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></li><li class="w-full"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base"><span class="text-foreground">v0 integrates with Supabase seamlessly.</span> If you ask v0 to generate an application and it needs Supabase,<!-- --> <span class="text-foreground">you’ll be prompted to create a Supabase account right there in the application</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Guillermo Rauch" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/guillermo-rauch.jpg 1x, ../_next/guillermo-rauch.jpg 2x" src="../_next/guillermo-rauch.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Guillermo Rauch</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Vercel (v0) - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></li></ul><div class="xl:hidden"><div class="swiper h-[300px] w-full !overflow-visible" style="z-index:0;margin-right:1px"><div class="swiper-wrapper"><div class="swiper-slide"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base">We chose Supabase because it&#x27;s<!-- --> <span class="text-foreground">extremely user friendly</span> and<!-- --> <span class="text-foreground">covers all the needs to build full-stack applications</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Anton Osika" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/anton-osika.jpg 1x, ../_next/anton-osika.jpg 2x" src="../_next/anton-osika.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Anton Osika</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Lovable - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></div><div class="swiper-slide"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base">Supabase is awesome. Supabase is the<!-- --> <span class="text-foreground">key database integration</span> that we have...because it’s the<!-- --> <span class="text-foreground">best product in the world for storing and retrieving data</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Eric Simmons" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/eric-simons.jpg 1x, ../_next/eric-simons.jpg 2x" src="../_next/eric-simons.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Eric Simmons</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Bolt.new - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></div><div class="swiper-slide"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] overflow-hidden flex flex-col justify-between text-foreground-lighter bg-surface-75 p-5"><div class="flex flex-col justify-between gap-6"><q class="text-base"><span class="text-foreground">v0 integrates with Supabase seamlessly.</span> If you ask v0 to generate an application and it needs Supabase,<!-- --> <span class="text-foreground">you’ll be prompted to create a Supabase account right there in the application</span>.</q></div><div class="flex flex-row gap-3 w-full items-center"><img alt="Guillermo Rauch" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="bg-surface-200 rounded-full border flex-shrink-0" style="color:transparent" srcSet="../_next/guillermo-rauch.jpg 1x, ../_next/guillermo-rauch.jpg 2x" src="../_next/guillermo-rauch.jpg"/><div class="flex flex-col gap-0"><span class="text-base text-foreground-light leading-snug">Guillermo Rauch</span><span class="uppercase font-mono text-sm text-foreground-lighter leading-tight">Vercel (v0) - CEO</span></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></div></div></div></div></div></div></section><div id="why-supabase" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 md:gap-8"><div class="flex flex-col gap-2 max-w-xl"><span class="label"></span><h2 class="h2 text-foreground-lighter">Why <span class="text-foreground">no-code app builders</span> choose Supabase</h2><p class="text-foreground-lighter mb-8">Keep your focus where it belongs: building great monetizable apps for your business or hobby. Supabase handles everything else: no servers to manage, no database to configure, no security settings to tweak. Just point, click, and build.</p></div><ul class="grid grid-cols-1 gap-4 gap-y-10 md:grid-cols-3 md:gap-12 xl:gap-20"><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap stroke-1 mb-2 text-current w-7 h-7"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Easy to use</h4><p class="text-foreground-lighter text-sm">Supabase is easy to use and set up. Instantly deploy a database for free, and affordably scale as you grow.</p></li><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pointer stroke-1 mb-2 text-current w-7 h-7"><path d="M22 14a8 8 0 0 1-8 8"></path><path d="M18 11v-1a2 2 0 0 0-2-2a2 2 0 0 0-2 2"></path><path d="M14 10V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1"></path><path d="M10 9.5V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v10"></path><path d="M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"></path></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Point and click backend</h4><p class="text-foreground-lighter text-sm">Supabase includes everything you need for a great app: user logins, storage, edge functions, real-time subscriptions, and vector search. Use one or all.</p></li><li class="flex flex-col gap-2 text-sm text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up stroke-1 mb-2 text-current w-7 h-7"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg><div class="w-full h-px overflow-hidden flex items-start bg-border-muted"><span class="h-full bg-foreground-lighter w-7"></span></div><h4 class="text-foreground text-lg lg:text-xl mt-1">Scales when you need it</h4><p class="text-foreground-lighter text-sm">Supabase is just Postgres, with all the performance, high availability, and flexibility you need when your app goes viral and hits it big.</p></li></ul></div><div id="postgres-platform" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-12 py-16 md:py-24 [&amp;_div.grid]:sm:divide-x [&amp;_div.grid]:divide-y [&amp;_div.grid&gt;div:nth-child(2n+1)]:sm:!border-l-0 [&amp;_div.grid&gt;div:nth-child(2n+2)]:sm:!border-l [&amp;_div.grid&gt;div:nth-child(2n+2)]:lg:!border-l [&amp;_div.grid&gt;div:nth-child(3n+3)]:lg:!border-l-0 [&amp;_div.grid&gt;div:nth-child(2n+3)]:lg:!border-l [&amp;_div.grid&gt;div:nth-child(2)]:lg:!border-t-0"><div class="flex flex-col gap-4 max-w-lg"><h2 class="text-2xl md:text-3xl text-foreground-lighter font-normal">Supabase is the Back-End for <span class="text-foreground">Everyone</span></h2><p class="text-foreground-lighter text-base md:text-lg">Supabase includes everything you need to create the perfect app for your brand, business, or just for fun.</p></div><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-0 sm:divide-x divide-y rounded-md border border-default overflow-hidden"><div class="relative overflow-hidden flex-1 flex items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground lg:col-span-2 flex-col lg:flex-row px-4 lg:pr-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:h-full ml-0 md:justify-start md:text-left md:items-start lg:max-w-[47%]"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.18625 8.66531H19.5035V15.331H5.18625V8.66531Z M4 17.0007C4 16.0804 4.7461 15.3343 5.66645 15.3343H18.9984C19.9187 15.3343 20.6648 16.0804 20.6648 17.0007V20.3335C20.6648 21.2539 19.9187 22 18.9984 22H5.66646C4.7461 22 4 21.2539 4 20.3335V17.0007Z M4 3.66646C4 2.7461 4.7461 2 5.66645 2H18.9984C19.9187 2 20.6648 2.7461 20.6648 3.66645V6.99926C20.6648 7.91962 19.9187 8.66572 18.9984 8.66572H5.66646C4.7461 8.66572 4 7.91962 4 6.99926V3.66646Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Database</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">A fully managed database that’s simple for creators and<!-- --> <span class="text-foreground">trusted by enterprises</span>.</p><span class="hidden lg:block text-foreground md:block"><ul class="hidden lg:flex flex-col gap-1 text-sm"><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> 100% portable</li><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> Built-in Auth with RLS</li><li><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check inline text-foreground-light h-4 w-4"><path d="M20 6 9 17l-5-5"></path></svg> Easy to extend</li></ul></span></div></div><div class="relative w-full max-w-xl pt-8"><div class="w-full h-full rounded-t-lg lg:rounded-tr-none overflow-hidden border-t border-l border-r lg:border-r-0 bg-surface-75"><table class="min-w-full m-0"><thead class="p-0"><tr class="border-b"><th class="py-2 px-4 text-left text-xs font-mono font-normal tracking-widest text-[#A0A0A0]">NAME</th><th class="py-2 px-4 text-left text-xs font-mono font-normal tracking-widest text-[#A0A0A0]">PUBLICATION</th></tr></thead><tbody class="bg-surface-100"><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Jon Meyers</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Chris Martin</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Amy Quek</td><td class="py-2 px-4 whitespace-nowrap">No</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Riccardo Bussetti</td><td class="py-2 px-4 whitespace-nowrap">No</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Beng Eu</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr><tr class="group/row hover:bg-selection hover:text-foreground transition-colors cursor-pointer"><td class="py-2 px-4 whitespace-nowrap">Tyler Hillery</td><td class="py-2 px-4 whitespace-nowrap">All</td></tr></tbody></table></div><div class=" absolute pointer-events-none w-full h-full inset-0 top-auto bg-[linear-gradient(to_bottom,transparent_0%,hsl(var(--background-default))_100%)] "></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l sm:!border-t-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.03305 15.8071H12.7252M5.03305 15.8071V18.884H12.7252V15.8071M5.03305 15.8071V12.7302H12.7252V15.8071M15.0419 8.15385V5.07692C15.0419 3.37759 13.6643 2 11.965 2C10.2657 2 8.88814 3.37759 8.88814 5.07692V8.15385M5 11.2307L5 18.9231C5 20.6224 6.37757 22 8.07689 22H15.769C17.4683 22 18.8459 20.6224 18.8459 18.9231V11.2307C18.8459 9.53142 17.4683 8.15385 15.769 8.15385L8.07689 8.15385C6.37757 8.15385 5 9.53142 5 11.2307Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Authentication</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Let your users<!-- --> <span class="text-foreground">login with email, Google, Apple, GitHub, and more</span>. Secure and trusted.</p></div></div><figure class="group absolute inset-0 z-0 -top-16 xl:top-0 xl:bottom-0 2xl:!-bottom-20" role="img" aria-label="Supabase Authentication provides Row Level Security which enables you to define custom Policies to restrict access to your database"><img draggable="false" alt="Supabase Authentication user db rows" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center xl:object-bottom" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth.svg"/><img draggable="false" alt="Supabase Authentication user db rows" aria-hidden="true" loading="lazy" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center xl:object-bottom opacity-0 group-hover:opacity-100 transition-opacity" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-active.svg"/><img draggable="false" alt="Supabase Authentication user db rows" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center xl:object-bottom" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-light.svg"/><img draggable="false" alt="Supabase Authentication user db rows" aria-hidden="true" loading="lazy" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center xl:object-bottom opacity-0 group-hover:opacity-100 transition-opacity" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/auth-active-light.svg"/></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.15928 1.94531V5.84117M6.24345 5.84117L2.91385 2.40977M6.24345 8.53673H2.4248M16.7998 16.496L21.9988 15.2019C22.7217 15.022 22.8065 14.0285 22.1246 13.7286L9.73411 8.28034C9.08269 7.99391 8.41873 8.65652 8.70383 9.30851L14.0544 21.5445C14.3518 22.2247 15.341 22.1456 15.5266 21.4269L16.7998 16.496Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Realtime</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Build immersive<!-- --> <span class="text-foreground">multi-player, collaborative experiences</span>.</p></div></div><figure class="absolute inset-0 xl:-bottom-2 2xl:bottom-0 z-0 w-full overflow-hidden pointer-events-auto [&amp;_.visual-overlay]:bg-[linear-gradient(to_top,transparent_0%,transparent_50%,hsl(var(--background-default))_75%)]" role="img" aria-label="Supabase Realtime multiplayer app demo"><img alt="Supabase Realtime" loading="lazy" decoding="async" data-nimg="fill" class="hidden dark:block absolute object-cover xl:object-center inset-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/realtime-dark.svg"/><img alt="Supabase Realtime" loading="lazy" decoding="async" data-nimg="fill" class="dark:hidden absolute object-cover xl:object-center inset-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/realtime-light.svg"/><div class="absolute will-change-transform" style="position:absolute;top:60%;left:30%;transform:translate(0px, 0px) translate(-50%, -50%);transition:transform 0.75s ease-out"><svg width="30" height="38" viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.58385 1.69742C2.57836 0.865603 1.05859 1.58076 1.05859 2.88572V35.6296C1.05859 37.1049 2.93111 37.7381 3.8265 36.5656L12.5863 25.0943C12.6889 24.96 12.8483 24.8812 13.0173 24.8812H27.3245C28.7697 24.8812 29.4211 23.0719 28.3076 22.1507L3.58385 1.69742Z" fill="hsl(var(--background-surface-200))" stroke="hsl(var(--foreground-lighter))" stroke-linejoin="round"></path></svg><div class="!w-[66.70px] !h-[33.35px] absolute left-full flex items-center justify-center gap-1 -top-6 border border-foreground-lighter/70 rounded-full bg-surface-100"><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class="absolute will-change-transform scale-[80%]" style="position:absolute;top:80%;left:65%;transform:translate(0px, 0px) translate(-50%, -50%);transition:transform 1s ease-out"><svg width="20" height="28" viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.58385 1.69742C2.57836 0.865603 1.05859 1.58076 1.05859 2.88572V35.6296C1.05859 37.1049 2.93111 37.7381 3.8265 36.5656L12.5863 25.0943C12.6889 24.96 12.8483 24.8812 13.0173 24.8812H27.3245C28.7697 24.8812 29.4211 23.0719 28.3076 22.1507L3.58385 1.69742Z" fill="hsl(var(--background-surface-200))" stroke="hsl(var(--foreground-lighter))" stroke-linejoin="round"></path></svg><div class="!w-[55px] !h-[28px] absolute left-full flex items-center justify-center gap-1 -top-6 border border-foreground-muted rounded-full bg-surface-100 opacity-0 group-hover:opacity-100 transition-opacity"><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-foreground-lighter animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class="absolute will-change-transform w-1 h-1 opacity-0 motion-safe:group-hover:opacity-100 delay-0 duration-75 group-hover:duration-300 transition-opacity" style="position:absolute;top:0;left:0;transform:translate(0px, 0px) translate(-50%, -50%)"><div class="w-auto h-auto px-2.5 py-1.5 absolute left-full flex items-center justify-center gap-1 -top-6 border border-brand rounded-full bg-brand-300"><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_200ms_infinite] pause group-hover:run"></div><div class="w-1.5 h-1.5 rounded-full bg-brand animate-[pulse_600ms_cubic-bezier(0.4,0,0.6,1)_400ms_infinite] pause group-hover:run"></div></div></div><div class=" visual-overlay absolute pointer-events-none w-full h-full max-h-[400px] lg:max-h-none inset-0 top-auto bg-[linear-gradient(to_top,transparent_0%,transparent_50%,hsl(var(--background-surface-75))_85%)] "></div></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 sm:!border-l lg:!border-l-0"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.6594 21.8201C8.10788 22.5739 9.75418 23 11.5 23C17.299 23 22 18.299 22 12.5C22 10.7494 21.5716 9.09889 20.8139 7.64754M16.4016 3.21191C14.9384 2.43814 13.2704 2 11.5 2C5.70101 2 1 6.70101 1 12.5C1 14.287 1.44643 15.9698 2.23384 17.4428M2.23384 17.4428C1.81058 17.96 1.55664 18.6211 1.55664 19.3416C1.55664 20.9984 2.89979 22.3416 4.55664 22.3416C6.21349 22.3416 7.55664 20.9984 7.55664 19.3416C7.55664 17.6847 6.21349 16.3416 4.55664 16.3416C3.62021 16.3416 2.78399 16.7706 2.23384 17.4428ZM21.5 5.64783C21.5 7.30468 20.1569 8.64783 18.5 8.64783C16.8432 8.64783 15.5 7.30468 15.5 5.64783C15.5 3.99097 16.8432 2.64783 18.5 2.64783C20.1569 2.64783 21.5 3.99097 21.5 5.64783ZM18.25 12.5C18.25 16.2279 15.2279 19.25 11.5 19.25C7.77208 19.25 4.75 16.2279 4.75 12.5C4.75 8.77208 7.77208 5.75 11.5 5.75C15.2279 5.75 18.25 8.77208 18.25 12.5Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Edge Functions</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">Custom backend logic when you want to dive into code.</p></div></div><figure class="absolute inset-0 z-20" role="img" aria-label="Supabase Edge Functions visual composition"><img alt="Supabase Edge Functions globe" decoding="async" data-nimg="fill" class="hidden dark:block absolute inset-0 object-cover object-center" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/edge-functions-dark.svg"/><img alt="Supabase Edge Functions globe" decoding="async" data-nimg="fill" class="dark:hidden absolute inset-0 object-cover object-center" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="../images/index/products/edge-functions-light.svg"/><div class=" absolute inset-0 top-[48%] xl:top-[45%] w-full max-w-[200px] h-fit mx-auto px-2.5 py-1.5 flex items-center justify-start rounded-full bg-surface-100 border border-strong text-xs text-foreground-lighter text-left "><span class="mr-2">$</span>supabase<span class="ml-1 text-brand inline-block">functions <span>deploy</span></span></div></figure></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 lg:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.4997 12.1386V9.15811L14.8463 3.53163H6.43717C5.57423 3.53163 4.87467 4.23119 4.87467 5.09413V9.78087M20.4447 9.13199L14.844 3.53125L14.844 7.56949C14.844 8.43243 15.5436 9.13199 16.4065 9.13199L20.4447 9.13199ZM7.12729 9.78087H4.83398C3.97104 9.78087 3.27148 10.4804 3.27148 11.3434V19.1559C3.27148 20.8818 4.67059 22.2809 6.39648 22.2809H18.8965C20.6224 22.2809 22.0215 20.8818 22.0215 19.1559V13.7011C22.0215 12.8381 21.3219 12.1386 20.459 12.1386H10.8032C10.3933 12.1386 9.99969 11.9774 9.70743 11.6899L8.22312 10.2296C7.93086 9.94202 7.53729 9.78087 7.12729 9.78087Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">Storage</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">Affordable and fast</span>, for all the videos and images you need in your app.</p></div></div></div><div class="relative overflow-hidden flex-1 flex flex-col items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground !border-l-0 lg:!border-l"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.13477 12.8129C4.13477 14.1481 4.43245 15.4138 4.96506 16.5471M12.925 4.02271C11.5644 4.02271 10.276 4.33184 9.12614 4.88371M21.7152 12.8129C21.7152 11.4644 21.4115 10.1867 20.8688 9.0447M12.925 21.6032C14.2829 21.6032 15.5689 21.2952 16.717 20.7454M16.717 20.7454C17.2587 21.5257 18.1612 22.0366 19.1831 22.0366C20.84 22.0366 22.1831 20.6935 22.1831 19.0366C22.1831 17.3798 20.84 16.0366 19.1831 16.0366C17.5263 16.0366 16.1831 17.3798 16.1831 19.0366C16.1831 19.6716 16.3804 20.2605 16.717 20.7454ZM4.96506 16.5471C4.16552 17.086 3.63965 17.9999 3.63965 19.0366C3.63965 20.6935 4.98279 22.0366 6.63965 22.0366C8.2965 22.0366 9.63965 20.6935 9.63965 19.0366C9.63965 17.3798 8.2965 16.0366 6.63965 16.0366C6.01951 16.0366 5.44333 16.2248 4.96506 16.5471ZM9.12614 4.88371C8.58687 4.08666 7.67444 3.56274 6.63965 3.56274C4.98279 3.56274 3.63965 4.90589 3.63965 6.56274C3.63965 8.2196 4.98279 9.56274 6.63965 9.56274C8.2965 9.56274 9.63965 8.2196 9.63965 6.56274C9.63965 5.94069 9.45032 5.36285 9.12614 4.88371ZM20.8688 9.0447C21.6621 8.50486 22.1831 7.59464 22.1831 6.56274C22.1831 4.90589 20.84 3.56274 19.1831 3.56274C17.5263 3.56274 16.1831 4.90589 16.1831 6.56274C16.1831 8.2196 17.5263 9.56274 19.1831 9.56274C19.8081 9.56274 20.3884 9.37165 20.8688 9.0447Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5"></path></svg><h3 class="">AI Ready</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground">When you’re ready to explore vectors and<!-- --> <span class="text-foreground">the power of AI</span>, Supabase is there with industry-standard tools to guide you.</p></div></div></div><div class="relative overflow-hidden flex-1 flex items-start sm:items-center lg:items-start justify-between bg-default w-full h-full min-h-[350px] sm:min-h-[400px] text-foreground-lighter [&amp;_strong]:!font-normal [&amp;_strong]:!text-foreground sm:col-span-2 flex-col"><div class="relative z-10 p-4 sm:p-6 2xl:p-8 w-full mx-auto gap-2 sm:gap-4 flex flex-col items-start sm:items-center text-left sm:text-center lg:mx-0 lg:items-start lg:text-left"><div class="flex items-center gap-2"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.00391 1.67476C4.95842 1.67476 1.67891 4.95427 1.67891 8.99975C1.67891 13.0452 4.95842 16.3248 9.00391 16.3248C13.0494 16.3248 16.3289 13.0452 16.3289 8.99975C16.3289 4.95427 13.0494 1.67476 9.00391 1.67476ZM1.32891 8.99975C1.32891 4.76097 4.76512 1.32476 9.00391 1.32476C13.2427 1.32476 16.6789 4.76097 16.6789 8.99975C16.6789 13.2385 13.2427 16.6748 9.00391 16.6748C4.76512 16.6748 1.32891 13.2385 1.32891 8.99975Z" fill="black" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M5.90901 5.90877C6.33097 5.48681 6.90326 5.24976 7.5 5.24976H12C12.4142 5.24976 12.75 5.58554 12.75 5.99976C12.75 6.41397 12.4142 6.74976 12 6.74976H7.5C7.30109 6.74976 7.11032 6.82877 6.96967 6.96943C6.82902 7.11008 6.75 7.30084 6.75 7.49976C6.75 7.69867 6.82902 7.88943 6.96967 8.03009C7.11032 8.17074 7.30109 8.24976 7.5 8.24976H10.5C11.0967 8.24976 11.669 8.48681 12.091 8.90877C12.5129 9.33072 12.75 9.90302 12.75 10.4998C12.75 11.0965 12.5129 11.6688 12.091 12.0907C11.669 12.5127 11.0967 12.7498 10.5 12.7498H6C5.58579 12.7498 5.25 12.414 5.25 11.9998C5.25 11.5855 5.58579 11.2498 6 11.2498H10.5C10.6989 11.2498 10.8897 11.1707 11.0303 11.0301C11.171 10.8894 11.25 10.6987 11.25 10.4998C11.25 10.3008 11.171 10.1101 11.0303 9.96943C10.8897 9.82877 10.6989 9.74976 10.5 9.74976H7.5C6.90326 9.74976 6.33097 9.5127 5.90901 9.09075C5.48705 8.66879 5.25 8.09649 5.25 7.49976C5.25 6.90302 5.48705 6.33072 5.90901 5.90877Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M8.99609 3.75C9.41031 3.75 9.74609 4.08579 9.74609 4.5V13.5C9.74609 13.9142 9.41031 14.25 8.99609 14.25C8.58188 14.25 8.24609 13.9142 8.24609 13.5V4.5C8.24609 4.08579 8.58188 3.75 8.99609 3.75Z" fill="currentColor"></path></svg><h3 class="">Pricing for builders</h3></div><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm 2xl:text-base [&amp;_strong]:!text-foreground"><span class="text-foreground">A generous free tier</span>, plus fair, flexible pricing when you’re ready to scale.</p></div></div><div class="relative w-full h-full"><div class="absolute inset-0 w-full px-4 pt-1.5 pb-0 lg:p-6 2xl:p-8"><div class="flex flex-col lg:grid grid-cols-4 gap-4 h-full mt-4 lg:mt-0 border border-strong rounded-xl p-4"><div class="flex justify-between w-full"><div class="flex flex-col text-lighter leading-4 text-xs w-full gap-4"><div class="h-full w-full flex flex-col justify-between"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs px-2.5 h-[26px] w-full pl-1 py-2" id="radix-:R5nlkla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="3.5" y="3.5" width="6.77393" height="6.77393" rx="1.5" stroke="hsl(var(--foreground-default))"></rect><rect x="5.55078" y="5.55127" width="2.67139" height="2.67139" rx="1.33569" stroke="hsl(var(--foreground-muted))"></rect></svg></div> <span class="truncate"><div class="lg:min-w-[80px] flex items-center grow w-full gap-1"><span class="text-foreground-light">Plan</span> <span>Pro</span></div></span> <div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></div></button></div><div><div class="flex flex-col gap-1 text-lighter text-right leading-4 w-full border-b pb-1 mb-1"><div class="flex items-center justify-between"><span class="text-foreground-muted">Plan</span><span class="text-light font-mono" translate="no">$<!-- -->25</span></div><div class="flex items-center justify-between"><span class="text-foreground-muted">Total Compute</span><span class="text-light font-mono">$<!-- -->10</span></div><div class="flex items-center justify-between"><span class="text-foreground-muted">Compute Credits</span><span class="text-light font-mono" translate="no">- $<!-- -->10</span></div></div><div class="flex items-center gap-1 w-full justify-between"><span>Total Estimate</span><span class="text-foreground font-mono flex items-center gap-1"><button data-state="closed" type="button" role="button" class="flex [&amp;_svg]:data-[state=delayed-open]:fill-foreground-lighter [&amp;_svg]:data-[state=instant-open]:fill-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" stroke-width="2" class="transition-colors fill-foreground-muted w-4 h-4"><path d="M15 8A7 7 0 1 1 1 8a7 7 0 0 1 14 0ZM9 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM6.75 8a.75.75 0 0 0 0 1.5h.75v1.75a.75.75 0 0 0 1.5 0v-2.5A.75.75 0 0 0 8.25 8h-1.5Z" fill-rule="evenodd" clip-rule="evenodd"></path></svg></button>$<!-- -->25</span></div></div></div></div><div class="flex flex-col gap-2 col-span-3 items-start flex-1 w-full lg:w-auto lg:border-l lg:pl-4"><div class="w-full flex flex-col items-start gap-y-2 border-none"><div class="group w-full flex flex-col gap-3 p-3 bg-surface-200 rounded border"><div class="w-full flex justify-between items-center"><div class="flex items-center gap-2"><div class="items-center bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs rounded-md text-center flex justify-center font-mono uppercase group-data-[state=open]:ring-2 group-data-[state=open]:ring-opacity-20 transition-all group-data-[state=open]:ring-foreground-muted bg-opacity-50 group-data-[state=open]:bg-opacity-75">Micro</div><p class="text-xs text-foreground-lighter">Project <!-- -->1</p></div><span class="leading-3 text-sm" translate="no">$10</span></div><span dir="ltr" data-orientation="horizontal" aria-disabled="false" class="relative flex touch-none select-none items-center w-full mt-1" style="--radix-slider-thumb-transform:translateX(-50%)"><span data-orientation="horizontal" class="relative h-1 w-full grow overflow-hidden rounded-full bg-surface-300"><span data-orientation="horizontal" class="absolute h-full bg-foreground-muted" style="left:0%;right:100%"></span></span><span style="transform:var(--radix-slider-thumb-transform);position:absolute;left:calc(0% + 0px)"><span role="slider" aria-valuemin="1" aria-valuemax="10" aria-orientation="horizontal" data-orientation="horizontal" tabindex="0" class="block h-5 w-5 rounded-full border-2 border-background-surface-100 bg-foreground ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" style="display:none" data-radix-collection-item=""></span></span></span><input style="display:none" value="1"/><div class="flex items-center justify-between text-sm"><div class="w-full flex items-center gap-2"><span class="text-lighter text-xs md:text-[13px]">1 GB<!-- --> RAM /<!-- --> <!-- -->2-core ARM<!-- --> CPU / Connections: Direct<!-- --> <!-- -->60<!-- -->, Pooler<!-- --> <!-- -->200</span></div><div class="flex items-center gap-2"></div></div></div></div><div class="w-full"><button data-size="tiny" type="button" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong flex items-center justify-center text-xs px-2.5 py-1 h-[26px] w-full border-dashed text-foreground-light hover:text-foreground"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></div> <span class="truncate"><span class="w-full text-left">Add Project</span></span> </button></div></div></div></div></div></div></div></div><div id="platform-starter" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-16 md:py-24 lg:mb-24 [&amp;&gt;div.grid]:lg:grid-cols-5 [&amp;_.col-left]:lg:col-span-2 [&amp;_.col-right]:lg:col-span-3"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16"><div class="col-left space-y-6 lg:pr-10"><h2 class="h2 text-foreground-lighter"><span class="text-foreground">Start building in seconds</span></h2><p class="text-foreground-light">Choose your platform to get started:</p><div class="flex lg:grid grid-cols-2 flex-nowrap gap-4 md:gap-8 lg:gap-4 2xl:gap-4 lg:pt-4 xl:grid xl:grid-cols-3 max-w-none xl:gap-8 2xl:gap-x-20"><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://lovable.dev/"><img src="../images/logos/publicity/lovable.svg" alt="lovable" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://bolt.new"><img src="../images/logos/publicity/bolt.svg" alt="bolt" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://v0.dev"><img src="../images/logos/publicity/v0.svg" alt="v0" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://www.figma.com/make/"><img src="../images/logos/publicity/figma.svg" alt="figma" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://tempo.new"><img src="../images/logos/publicity/tempo.svg" alt="tempo" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://gumloop.com"><img src="../images/logos/publicity/gumloop.svg" alt="gumloop" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a><a target="_blank" class="h-12 lg:h-12 w-max hover:opacity-100 opacity-80 transition-opacity" href="https://co.dev"><img src="../images/logos/publicity/co-com.svg" alt="co.com" class=" w-auto block h-10 !min-h-10 md:h-10 md:!min-h-10 lg:h-7 lg:!min-h-7 2xl:h-10 2xl:!min-h-10 " draggable="false"/></a></div></div><div class="col-right space-y-6"><div class="grid md:grid-cols-2 gap-2"><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Internal CRM for High-Touch Client Services</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Build an internal CRM that tracks interactions with clients in a service business (e.g. agency, law firm, consultancy). Include a client directory with contact info and tags, a timeline of interactions (calls, meetings, emails), and a task management feature per client. Add a pipeline view for sales or project stages. Use Supabase Auth for role-based access and row-level security to restrict views by user.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Customer Feedback Collection and Tagging System</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Create a tool to collect and tag customer feedback from multiple sources like support tickets, surveys, and interviews. Allow team members to log new feedback with sentiment, category, and product area. Summarize trends in charts (e.g. top requests, most common bugs). Use Supabase as the backend with triggers to notify product owners when thresholds are met. Optional: add AI-powered tagging for fast triage.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Automated Onboarding Portal for New Hires</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Build a customizable portal for onboarding new employees in a remote company. Show personalized checklists by role, with links to key docs, Slack channels, and tool logins. Let managers monitor completion status. Include a welcome board where coworkers can leave greetings. Use Supabase to manage checklists, users, and messages. Add role-based views for HR vs. employee.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Recurring Revenue Dashboard for Indie SaaS</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Create a financial dashboard for indie founders to track recurring revenue. Include metrics like MRR, ARR, churn, and LTV. Add charts for monthly trends. Let users manually enter Stripe or PayPal data, or auto-sync via webhook. Use Supabase to store data with per-user access control. Configure Slack integration for daily digests.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Custom Client Portal for Freelancers</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Build a secure portal where freelancers can share files, invoices, and project updates with clients. Each client should log in and only see their own dashboard. Include a file upload section, embedded payment buttons, and a timeline of status updates. Use Supabase for data storage and row-level security.</pre></div></div><div class="relative group bg-surface-75 border border-default rounded-lg"><div class="flex items-center justify-between px-4 py-3 border-b border-default bg-surface-100"><h3 class="text-sm text-foreground truncate">Compliance Tracker for Early-Stage Startups</h3></div><div class="p-4 relative"><button data-size="tiny" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs text-foreground-light hover:text-foreground absolute top-2 right-2 w-7 h-7 p-1 shadow-lg" data-state="closed"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3 h-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button><pre class="text-xs text-foreground-light whitespace-pre-wrap font-mono leading-relaxed line-clamp-4">Create a dashboard for startups to track legal, security, and HR compliance tasks. Let users define tasks, assign owners, set due dates, and upload supporting documents. Group tasks by category (e.g. Security, Legal) and show progress bars. Use Supabase for storing task data and documents.</pre></div></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/solutions/no-code","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"dynamicIds":[33714,69038,7416,8101,1825,57277,45748,37894],"scriptLoader":[]}</script></body></html>