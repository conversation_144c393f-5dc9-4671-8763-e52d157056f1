<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/queues/layout-b7ba5f0183e7181b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>PGMQ Extension | Supabase Docs</title><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="pgmq.html"/><meta property="og:title" content="PGMQ Extension | Supabase Docs"/><meta property="og:url" content="https://supabase.com/docs/guides/queues/pgmq"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=queues&amp;title=PGMQ%20Extension&amp;description=undefined"/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="PGMQ Extension"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="PGMQ Extension | Supabase Docs"/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../queues.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-stack"><path d="M4 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path><path d="M10 16c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path><rect width="8" height="8" x="14" y="14" rx="2"></rect></svg><span class="  false hover:text-brand text-foreground">Queues</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../queues.html">Overview</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Getting Started</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quickstart.html">Quickstart</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">References</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="api.html">API</a></li></div><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="pgmq.html">PGMQ Extension</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Queues</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../queues.html">Queues</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">References</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="pgmq.html">PGMQ Extension</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>PGMQ Extension</p></h1><hr class="not-prose border-t-0 border-b my-8"/><p>pgmq is a lightweight message queue built on Postgres.</p>
<h2 id="features" class="group scroll-mt-24">Features<a href="#features" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Lightweight - No background worker or external dependencies, just Postgres functions packaged in an extension</li>
<li>&quot;exactly once&quot; delivery of messages to a consumer within a visibility timeout</li>
<li>API parity with AWS SQS and RSMQ</li>
<li>Messages stay in the queue until explicitly removed</li>
<li>Messages can be archived, instead of deleted, for long-term retention and replayability</li>
</ul>
<h2 id="enable-the-extension" class="group scroll-mt-24">Enable the extension<a href="#enable-the-extension" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> extension pgmq;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="get-usage" class="group scroll-mt-24">Usage <a href="#get-usage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="queue-management" class="group scroll-mt-24">Queue management<a href="#queue-management" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="create" class="group scroll-mt-24"><code>create</code><a href="#create" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Create a new queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">create</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> void</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">create</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">create</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h4 id="createunlogged" class="group scroll-mt-24"><code>create_unlogged</code><a href="#createunlogged" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Creates an unlogged table. This is useful when write throughput is more important than durability.
See Postgres documentation for <a href="https://www.postgresql.org/docs/current/sql-createtable.html#SQL-CREATETABLE-UNLOGGED">unlogged tables</a> for more information.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">create_unlogged</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> void</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">create_unlogged</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_unlogged</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> create_unlogged</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-----------------</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="detacharchive" class="group scroll-mt-24"><code>detach_archive</code><a href="#detacharchive" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Drop the queue&#x27;s archive table as a member of the PGMQ extension. Useful for preventing the queue&#x27;s archive table from being drop when <code>drop extension pgmq</code> is executed.
This does not prevent the further archives() from appending to the archive table.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">detach_archive</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">detach_archive</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> detach_archive</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">----------------</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="dropqueue" class="group scroll-mt-24"><code>drop_queue</code><a href="#dropqueue" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Deletes a queue and its archive table.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">drop_queue</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">boolean</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">drop_queue</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_unlogged</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> drop_queue</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> t</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="sending-messages" class="group scroll-mt-24">Sending messages<a href="#sending-messages" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="send" class="group scroll-mt-24"><code>send</code><a href="#send" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Send a single message to a queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">send</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    msg jsonb,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">delay</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">default</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">0</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-keyword)">bigint</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg</code></td><td align="left"><code>jsonb</code></td><td align="left">The message to send to the queue</td></tr><tr><td align="left"><code>delay</code></td><td align="left"><code>integer</code></td><td align="left">Time in seconds before the message becomes visible. Defaults to 0.</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">send</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">{&quot;hello&quot;: &quot;world&quot;}</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">send</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-constant)">4</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="sendbatch" class="group scroll-mt-24"><code>send_batch</code><a href="#sendbatch" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Send 1 or more messages to a queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">send_batch</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    msgs jsonb[],</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">delay</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">default</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">0</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-keyword)">bigint</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msgs</code></td><td align="left"><code>jsonb[]</code></td><td align="left">Array of messages to send to the queue</td></tr><tr><td align="left"><code>delay</code></td><td align="left"><code>integer</code></td><td align="left">Time in seconds before the messages becomes visible. Defaults to 0.</td></tr></tbody></table>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">send_batch</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">array</span><span style="color:var(--code-foreground)">[</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">{&quot;hello&quot;: &quot;world_0&quot;}</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">::jsonb,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">{&quot;hello&quot;: &quot;world_1&quot;}</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">::jsonb</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    ]</span></span><span class="block h-5"><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> send_batch</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-constant)">1</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-constant)">2</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h3 id="reading-messages" class="group scroll-mt-24">Reading messages<a href="#reading-messages" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="read" class="group scroll-mt-24"><code>read</code><a href="#read" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Read 1 or more messages from a queue. The VT specifies the duration of time in seconds that the message is invisible to other consumers. At the end of that duration, the message is visible again and could be read by other consumers.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">read</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    vt </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    qty </span><span style="color:var(--code-token-keyword)">integer</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">message_record</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>vt</code></td><td align="left"><code>integer</code></td><td align="left">Time in seconds that the message become invisible after reading</td></tr><tr><td align="left"><code>qty</code></td><td align="left"><code>integer</code></td><td align="left">The number of messages to read from the queue. Defaults to 1</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">read</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">2</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> msg_id | read_ct |          enqueued_at          |              vt               |       </span><span style="color:var(--code-token-keyword)">message</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------+---------+-------------------------------+-------------------------------+----------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">14</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">47</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">356595</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">17</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">08</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">608922</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world_0</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">2</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">14</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">47</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">356595</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">17</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">08</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">608974</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world_1</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">2</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">rows</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="readwithpoll" class="group scroll-mt-24"><code>read_with_poll</code><a href="#readwithpoll" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Same as read(). Also provides convenient long-poll functionality.
When there are no messages in the queue, the function call will wait for <code>max_poll_seconds</code> in duration before returning.
If messages reach the queue during that duration, they will be read and returned immediately.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">read_with_poll</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    vt </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    qty </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    max_poll_seconds </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">default</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">5</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    poll_interval_ms </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">default</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">100</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">message_record</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>vt</code></td><td align="left"><code>integer</code></td><td align="left">Time in seconds that the message become invisible after reading.</td></tr><tr><td align="left"><code>qty</code></td><td align="left"><code>integer</code></td><td align="left">The number of messages to read from the queue. Defaults to 1.</td></tr><tr><td align="left"><code>max_poll_seconds</code></td><td align="left"><code>integer</code></td><td align="left">Time in seconds to wait for new messages to reach the queue. Defaults to 5.</td></tr><tr><td align="left"><code>poll_interval_ms</code></td><td align="left"><code>integer</code></td><td align="left">Milliseconds between the internal poll operations. Defaults to 100.</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">read_with_poll</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">5</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">100</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> msg_id | read_ct |          enqueued_at          |              vt               |      </span><span style="color:var(--code-token-keyword)">message</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------+---------+-------------------------------+-------------------------------+--------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">09</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">09</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">177756</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">27</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">00</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">337929</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="pop" class="group scroll-mt-24"><code>pop</code><a href="#pop" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Reads a single message from a queue and deletes it upon read.</p>
<p>Note: utilization of pop() results in at-most-once delivery semantics if the consuming application does not guarantee processing of the message.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">pop</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">message_record</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-foreground)">pgmq</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"># </span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">pop</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> msg_id | read_ct |          enqueued_at          |              vt               |      </span><span style="color:var(--code-token-keyword)">message</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------+---------+-------------------------------+-------------------------------+--------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">2</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">09</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">09</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">177756</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">27</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">00</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">337929</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h3 id="deletingarchiving-messages" class="group scroll-mt-24">Deleting/Archiving messages<a href="#deletingarchiving-messages" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="delete-single" class="group scroll-mt-24"><code>delete</code> (single)<a href="#delete-single" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Deletes a single message from a queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">delete</span><span style="color:var(--code-foreground)"> (queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">, msg_id: </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">boolean</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg_id</code></td><td align="left"><code>bigint</code></td><td align="left">Message ID of the message to delete</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">delete</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">5</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">delete</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> t</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="delete-batch" class="group scroll-mt-24"><code>delete</code> (batch)<a href="#delete-batch" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Delete one or many messages from a queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">delete</span><span style="color:var(--code-foreground)"> (queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">, msg_ids: </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">[])</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> setof </span><span style="color:var(--code-token-keyword)">bigint</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg_ids</code></td><td align="left"><code>bigint[]</code></td><td align="left">Array of message IDs to delete</td></tr></tbody></table>
<p>Examples:</p>
<p>Delete two messages that exist.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">delete</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-keyword)">array</span><span style="color:var(--code-foreground)">[2, 3]);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">delete</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">2</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">3</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Delete two messages, one that exists and one that does not. Message <code>999</code> does not exist.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">delete</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-keyword)">array</span><span style="color:var(--code-foreground)">[6, 999]);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">delete</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">6</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="purgequeue" class="group scroll-mt-24"><code>purge_queue</code><a href="#purgequeue" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Permanently deletes all messages in a queue. Returns the number of messages that were deleted.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span>purge_queue(queue_name text)</span></span><span class="block h-5"><span>returns bigint</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p>Example:</p>
<p>Purge the queue when it contains 8 messages;</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">purge_queue</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> purge_queue</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">           </span><span style="color:var(--code-token-constant)">8</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="archive-single" class="group scroll-mt-24"><code>archive</code> (single)<a href="#archive-single" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Removes a single requested message from the specified queue and inserts it into the queue&#x27;s archive.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">archive</span><span style="color:var(--code-foreground)">(queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">, msg_id </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">boolean</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg_id</code></td><td align="left"><code>bigint</code></td><td align="left">Message ID of the message to archive</td></tr></tbody></table>
<p>Returns
Boolean value indicating success or failure of the operation.</p>
<p>Example; remove message with ID 1 from queue <code>my_queue</code> and archive it:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">archive</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> archive</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">---------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">       t</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="archive-batch" class="group scroll-mt-24"><code>archive</code> (batch)<a href="#archive-batch" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Deletes a batch of requested messages from the specified queue and inserts them into the queue&#x27;s archive.
Returns an array of message ids that were successfully archived.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span>pgmq.archive(queue_name text, msg_ids bigint[])</span></span><span class="block h-5"><span>RETURNS SETOF bigint</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg_ids</code></td><td align="left"><code>bigint[]</code></td><td align="left">Array of message IDs to archive</td></tr></tbody></table>
<p>Examples:</p>
<p>Delete messages with ID 1 and 2 from queue <code>my_queue</code> and move to the archive.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">archive</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-keyword)">array</span><span style="color:var(--code-foreground)">[1, 2]);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> archive</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">---------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">       </span><span style="color:var(--code-token-constant)">1</span></span><span class="block h-5"><span style="color:var(--code-foreground)">       </span><span style="color:var(--code-token-constant)">2</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Delete messages 4, which exists and 999, which does not exist.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">archive</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-keyword)">array</span><span style="color:var(--code-foreground)">[4, 999]);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> archive</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">---------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">       </span><span style="color:var(--code-token-constant)">4</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h3 id="utilities" class="group scroll-mt-24">Utilities<a href="#utilities" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="setvt" class="group scroll-mt-24"><code>set_vt</code><a href="#setvt" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Sets the visibility timeout of a message to a specified time duration in the future. Returns the record of the message that was updated.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">set_vt</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    msg_id </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    vt_offset </span><span style="color:var(--code-token-keyword)">integer</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">message_record</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>queue_name</code></td><td align="left"><code>text</code></td><td align="left">The name of the queue</td></tr><tr><td align="left"><code>msg_id</code></td><td align="left"><code>bigint</code></td><td align="left">ID of the message to set visibility time</td></tr><tr><td align="left"><code>vt_offset</code></td><td align="left"><code>integer</code></td><td align="left">Duration from now, in seconds, that the message&#x27;s VT should be set to</td></tr></tbody></table>
<p>Example:</p>
<p>Set the visibility timeout of message 1 to 30 seconds from now.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">set_vt</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">11</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">30</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> msg_id | read_ct |          enqueued_at          |              vt               |       </span><span style="color:var(--code-token-keyword)">message</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------+---------+-------------------------------+-------------------------------+----------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">     </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">0</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">42</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">21</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">778741</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">59</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">34</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">286462</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world_0</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="listqueues" class="group scroll-mt-24"><code>list_queues</code><a href="#listqueues" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>List all the queues that currently exist.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-foreground)">list_queues</span><span style="color:var(--code-token-punctuation)">()</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">RETURNS</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">TABLE</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    created_at </span><span style="color:var(--code-token-keyword)">timestamp with time zone</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    is_partitioned </span><span style="color:var(--code-token-keyword)">boolean</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    is_unlogged </span><span style="color:var(--code-token-keyword)">boolean</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">list_queues</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      queue_name      |          created_at           | is_partitioned | is_unlogged</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">----------------------+-------------------------------+----------------+-------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_queue             | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">14</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">13</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">17</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">092576</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | f              | f</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_partitioned_queue | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">47</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">37</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">098692</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | t              | f</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_unlogged          | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">20</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">02</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">30</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">976109</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | f              | t</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="metrics" class="group scroll-mt-24"><code>metrics</code><a href="#metrics" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Get metrics for a specific queue.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">metrics</span><span style="color:var(--code-foreground)">(queue_name: </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">table</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_name </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    queue_length </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    newest_msg_age_sec </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    oldest_msg_age_sec </span><span style="color:var(--code-token-keyword)">integer</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    total_messages </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    scrape_time </span><span style="color:var(--code-token-keyword)">timestamp with time zone</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Parameters:</strong></p>
<table><thead><tr><th align="left">Parameter</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left">queue_name</td><td align="left">text</td><td align="left">The name of the queue</td></tr></tbody></table>
<p><strong>Returns:</strong></p>
<p>| Attribute            | Type                       | Description                                                               |
| :------------------- | :------------------------- | :------------------------------------------------------------------------ | -------------------------------------------------- |
| <code>queue_name</code>         | <code>text</code>                     | The name of the queue                                                     |
| <code>queue_length</code>       | <code>bigint</code>                   | Number of messages currently in the queue                                 |
| <code>newest_msg_age_sec</code> | <code>integer                   | null</code>                                                                     | Age of the newest message in the queue, in seconds |
| <code>oldest_msg_age_sec</code> | <code>integer                   | null</code>                                                                     | Age of the oldest message in the queue, in seconds |
| <code>total_messages</code>     | <code>bigint</code>                   | Total number of messages that have passed through the queue over all time |
| <code>scrape_time</code>        | <code>timestamp with time zone</code> | The current timestamp                                                     |</p>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">metrics</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">my_queue</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> queue_name | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">------------+--------------+--------------------+--------------------+----------------+-------------------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_queue   |           </span><span style="color:var(--code-token-constant)">16</span><span style="color:var(--code-foreground)"> |               </span><span style="color:var(--code-token-constant)">2445</span><span style="color:var(--code-foreground)"> |               </span><span style="color:var(--code-token-constant)">2447</span><span style="color:var(--code-foreground)"> |             </span><span style="color:var(--code-token-constant)">35</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">20</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">23</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">08</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">406259</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<hr/>
<h4 id="metricsall" class="group scroll-mt-24"><code>metrics_all</code><a href="#metricsall" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Get metrics for all existing queues.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span>pgmq.metrics_all()</span></span><span class="block h-5"><span>RETURNS TABLE(</span></span><span class="block h-5"><span>    queue_name text,</span></span><span class="block h-5"><span>    queue_length bigint,</span></span><span class="block h-5"><span>    newest_msg_age_sec integer,</span></span><span class="block h-5"><span>    oldest_msg_age_sec integer,</span></span><span class="block h-5"><span>    total_messages bigint,</span></span><span class="block h-5"><span>    scrape_time timestamp with time zone</span></span><span class="block h-5"><span>)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p><strong>Returns:</strong></p>
<p>| Attribute            | Type                       | Description                                                               |
| :------------------- | :------------------------- | :------------------------------------------------------------------------ | -------------------------------------------------- |
| <code>queue_name</code>         | <code>text</code>                     | The name of the queue                                                     |
| <code>queue_length</code>       | <code>bigint</code>                   | Number of messages currently in the queue                                 |
| <code>newest_msg_age_sec</code> | <code>integer                   | null</code>                                                                     | Age of the newest message in the queue, in seconds |
| <code>oldest_msg_age_sec</code> | <code>integer                   | null</code>                                                                     | Age of the oldest message in the queue, in seconds |
| <code>total_messages</code>     | <code>bigint</code>                   | Total number of messages that have passed through the queue over all time |
| <code>scrape_time</code>        | <code>timestamp with time zone</code> | The current timestamp                                                     |</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">pgmq</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">metrics_all</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      queue_name      | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">----------------------+--------------+--------------------+--------------------+----------------+-------------------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_queue             |           </span><span style="color:var(--code-token-constant)">16</span><span style="color:var(--code-foreground)"> |               </span><span style="color:var(--code-token-constant)">2563</span><span style="color:var(--code-foreground)"> |               </span><span style="color:var(--code-token-constant)">2565</span><span style="color:var(--code-foreground)"> |             </span><span style="color:var(--code-token-constant)">35</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">20</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">25</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">07</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">016413</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_partitioned_queue |            </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |                 </span><span style="color:var(--code-token-constant)">11</span><span style="color:var(--code-foreground)"> |                 </span><span style="color:var(--code-token-constant)">11</span><span style="color:var(--code-foreground)"> |              </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">20</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">25</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">07</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">016413</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span></span><span class="block h-5"><span style="color:var(--code-foreground)"> my_unlogged          |            </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |                  </span><span style="color:var(--code-token-constant)">3</span><span style="color:var(--code-foreground)"> |                  </span><span style="color:var(--code-token-constant)">3</span><span style="color:var(--code-foreground)"> |              </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">20</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">25</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">07</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">016413</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="types" class="group scroll-mt-24">Types<a href="#types" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="messagerecord" class="group scroll-mt-24"><code>message_record</code><a href="#messagerecord" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>The complete representation of a message in a queue.</p>
<table><thead><tr><th align="left">Attribute Name</th><th align="left">Type</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><code>msg_id</code></td><td align="left"><code>bigint</code></td><td align="left">Unique ID of the message</td></tr><tr><td align="left"><code>read_ct</code></td><td align="left"><code>bigint</code></td><td align="left">Number of times the message has been read. Increments on read().</td></tr><tr><td align="left"><code>enqueued_at</code></td><td align="left"><code>timestamp with time zone</code></td><td align="left">time that the message was inserted into the queue</td></tr><tr><td align="left"><code>vt</code></td><td align="left"><code>timestamp with time zone</code></td><td align="left">Timestamp when the message will become available for consumers to read</td></tr><tr><td align="left"><code>message</code></td><td align="left"><code>jsonb</code></td><td align="left">The message payload</td></tr></tbody></table>
<p>Example:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-foreground)">msg_id | read_ct |          enqueued_at          |              vt               |      </span><span style="color:var(--code-token-keyword)">message</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">--------+---------+-------------------------------+-------------------------------+--------------------</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> |       </span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">06</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">941509</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | </span><span style="color:var(--code-token-constant)">2023</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">10</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">28</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">19</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">06</span><span style="color:var(--code-foreground)">:</span><span style="color:var(--code-token-constant)">27</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">419392</span><span style="color:var(--code-token-keyword)">-</span><span style="color:var(--code-token-constant)">05</span><span style="color:var(--code-foreground)"> | {</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">hello</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">: </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">world</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">}</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="resources" class="group scroll-mt-24">Resources<a href="#resources" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Official Docs: <a href="https://pgmq.github.io/pgmq/#creating-a-queue">pgmq/api</a></li>
</ul><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/queues/pgmq.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\""])</script><script>self.__next_f.push([1,"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804"])</script><script>self.__next_f.push([1,"-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl"])</script><script>self.__next_f.push([1,"=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1"])</script><script>self.__next_f.push([1,"026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\""])</script><script>self.__next_f.push([1,",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2424\",\"static/chunks/app/guides/queues/layout-b7ba5f0183e7181b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GS"])</script><script>self.__next_f.push([1,"RZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2424\",\"static/chunks/app/guides/queues/layout-b7ba5f0183e7181b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunk"])</script><script>self.__next_f.push([1,"s/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB"])</script><script>self.__next_f.push([1,"1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC"])</script><script>self.__next_f.push([1,"7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"sta"])</script><script>self.__next_f.push([1,"tic/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL["])</script><script>self.__next_f.push([1,"\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"queues\",\"pgmq\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"queues\",{\"children\":[[\"slug\",\"pgmq\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"queues\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"pgmq\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"xEAoQw5G470uqCuDsBatr\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"PGMQ Extension\"]}]]}],\"$undefined\",[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/queues/pgmq.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"PGMQ Extension | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"3\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/queues/pgmq\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"PGMQ Extension | Supabase Docs\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/queues/pgmq\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=queues\u0026title=PGMQ%20Extension\u0026description=undefined\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:alt\",\"content\":\"PGMQ Extension\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"11\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"12\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"PGMQ Extension | Supabase Docs\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"19\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"p\",null,{\"children\":\"pgmq is a lightweight message queue built on Postgres.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Features\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":\"Lightweight - No background worker or external dependencies, just Postgres functions packaged in an extension\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"\\\"exactly once\\\" delivery of messages to a consumer within a visibility timeout\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"API parity with AWS SQS and RSMQ\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Messages stay in the queue until explicitly removed\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Messages can be archived, instead of deleted, for long-term retention and replayability\"}],\"\\n\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Enable the extension\"}],\"\\n\",\"$L2c\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Usage [#get-usage]\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Queue management\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"create\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Create a new queue.\"}],\"\\n\",\"\\n\",\"$L2d\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L2e\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"create_unlogged\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Creates an unlogged table. This is useful when write throughput is more important than durability.\\nSee Postgres documentation for \",[\"$\",\"a\",null,{\"href\":\"https://www.postgresql.org/docs/current/sql-createtable.html#SQL-CREATETABLE-UNLOGGED\",\"children\":\"unlogged tables\"}],\" for more information.\"]}],\"\\n\",\"\\n\",\"$L2f\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L30\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"detach_archive\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Drop the queue's archive table as a member of the PGMQ extension. Useful for preventing the queue's archive table from being drop when \",[\"$\",\"code\",null,{\"children\":\"drop extension pgmq\"}],\" is executed.\\nThis does not prevent the further archives() from appending to the archive table.\"]}],\"\\n\",\"\\n\",\"$L31\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L32\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"drop_queue\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Deletes a queue and its archive table.\"}],\"\\n\",\"\\n\",\"$L33\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L34\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Sending messages\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"send\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Send a single message to a queue.\"}],\"\\n\",\"\\n\",\"$L35\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"jsonb\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The message to send to the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"delay\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Time in seconds before the message becomes visible. Defaults to 0.\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L36\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"send_batch\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Send 1 or more messages to a queue.\"}],\"\\n\",\"\\n\",\"$L37\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msgs\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"jsonb[]\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Array of messages to send to the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"delay\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Time in seconds before the messages becomes visible. Defaults to 0.\"}]]}]]}]]}],\"\\n\",\"\\n\",\"$L38\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Reading messages\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"read\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Read 1 or more messages from a queue. The VT specifies the duration of time in seconds that the message is invisible to other consumers. At the end of that duration, the message is visible again and could be read by other consumers.\"}],\"\\n\",\"\\n\",\"$L39\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"vt\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Time in seconds that the message become invisible after reading\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"qty\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The number of messages to read from the queue. Defaults to 1\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L3a\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"read_with_poll\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Same as read(). Also provides convenient long-poll functionality.\\nWhen there are no messages in the queue, the function call will wait for \",[\"$\",\"code\",null,{\"children\":\"max_poll_seconds\"}],\" in duration before returning.\\nIf messages reach the queue during that duration, they will be read and returned immediately.\"]}],\"\\n\",\"\\n\",\"$L3b\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"vt\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Time in seconds that the message become invisible after reading.\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"qty\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The number of messages to read from the queue. Defaults to 1.\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"max_poll_seconds\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Time in seconds to wait for new messages to reach the queue. Defaults to 5.\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"poll_interval_ms\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Milliseconds between the internal poll operations. Defaults to 100.\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L3c\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"pop\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Reads a single message from a queue and deletes it upon read.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Note: utilization of pop() results in at-most-once delivery semantics if the consuming application does not guarantee processing of the message.\"}],\"\\n\",\"\\n\",\"$L3d\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L3e\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Deleting/Archiving messages\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[[\"$\",\"code\",null,{\"children\":\"delete\"}],\" (single)\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Deletes a single message from a queue.\"}],\"\\n\",\"\\n\",\"$L3f\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_id\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Message ID of the message to delete\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L40\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[[\"$\",\"code\",null,{\"children\":\"delete\"}],\" (batch)\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Delete one or many messages from a queue.\"}],\"\\n\",\"\\n\",\"$L41\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_ids\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint[]\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Array of message IDs to delete\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Examples:\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Delete two messages that exist.\"}],\"\\n\",\"\\n\",\"$L42\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Delete two messages, one that exists and one that does not. Message \",[\"$\",\"code\",null,{\"children\":\"999\"}],\" does not exist.\"]}],\"\\n\",\"$L43\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"purge_queue\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Permanently deletes all messages in a queue. Returns the number of messages that were deleted.\"}],\"\\n\",\"$L44\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Purge the queue when it contains 8 messages;\"}],\"\\n\",\"\\n\",\"$L45\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[[\"$\",\"code\",null,{\"children\":\"archive\"}],\" (single)\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Removes a single requested message from the specified queue and inserts it into the queue's archive.\"}],\"\\n\",\"\\n\",\"$L46\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_id\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Message ID of the message to archive\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Returns\\nBoolean value indicating success or failure of the operation.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Example; remove message with ID 1 from queue \",[\"$\",\"code\",null,{\"children\":\"my_queue\"}],\" and archive it:\"]}],\"\\n\",\"\\n\",\"$L47\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[[\"$\",\"code\",null,{\"children\":\"archive\"}],\" (batch)\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Deletes a batch of requested messages from the specified queue and inserts them into the queue's archive.\\nReturns an array of message ids that were successfully archived.\"}],\"\\n\",\"$L48\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_ids\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint[]\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Array of message IDs to archive\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Examples:\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Delete messages with ID 1 and 2 from queue \",[\"$\",\"code\",null,{\"children\":\"my_queue\"}],\" and move to the archive.\"]}],\"\\n\",\"\\n\",\"$L49\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Delete messages 4, which exists and 999, which does not exist.\"}],\"\\n\",\"\\n\",\"$L4a\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Utilities\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"set_vt\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Sets the visibility timeout of a message to a specified time duration in the future. Returns the record of the message that was updated.\"}],\"\\n\",\"\\n\",\"$L4b\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"queue_name\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"text\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_id\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"ID of the message to set visibility time\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"vt_offset\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"integer\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Duration from now, in seconds, that the message's VT should be set to\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Set the visibility timeout of message 1 to 30 seconds from now.\"}],\"\\n\",\"$L4c\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"list_queues\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"List all the queues that currently exist.\"}],\"\\n\",\"\\n\",\"$L4d\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L4e\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"metrics\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Get metrics for a specific queue.\"}],\"\\n\",\"\\n\",\"$L4f\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Parameters:\"}]}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Parameter\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"queue_name\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"text\"}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The name of the queue\"}]]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Returns:\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"| Attribute            | Type                       | Description                                                               |\\n| :------------------- | :------------------------- | :------------------------------------------------------------------------ | -------------------------------------------------- |\\n| \",[\"$\",\"code\",null,{\"children\":\"queue_name\"}],\"         | \",[\"$\",\"code\",null,{\"children\":\"text\"}],\"                     | The name of the queue                                                     |\\n| \",[\"$\",\"code\",null,{\"children\":\"queue_length\"}],\"       | \",[\"$\",\"code\",null,{\"children\":\"bigint\"}],\"                   | Number of messages currently in the queue                                 |\\n| \",[\"$\",\"code\",null,{\"children\":\"newest_msg_age_sec\"}],\" | \",[\"$\",\"code\",null,{\"children\":\"integer                   | null\"}],\"                                                                     | Age of the newest message in the queue, in seconds |\\n| \",[\"$\",\"code\",null,{\"children\":\"oldest_msg_age_sec\"}],\" | \",[\"$\",\"code\",null,{\"children\":\"integer                   | null\"}],\"                                                                     | Age of the oldest message in the queue, in seconds |\\n| \",[\"$\",\"code\",null,{\"children\":\"total_messages\"}],\"     | \",[\"$\",\"code\",null,{\"children\":\"bigint\"}],\"                   | Total number of messages that have passed through the queue over all time |\\n| \",[\"$\",\"code\",null,{\"children\":\"scrape_time\"}],\"        | \",[\"$\",\"code\",null,{\"children\":\"timestamp with time zone\"}],\" | The current timestamp                                                     |\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L50\",\"\\n\",[\"$\",\"hr\",null,{}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"metrics_all\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Get metrics for all existing queues.\"}],\"\\n\",\"$L51\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Returns:\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"| Attribute            | Type                       | Description                                                               |\\n| :------------------- | :------------------------- | :------------------------------------------------------------------------ | -------------------------------------------------- |\\n| \",[\"$\",\"code\",null,{\"children\":\"queue_name\"}],\"         | \",[\"$\",\"code\",null,{\"children\":\"text\"}],\"                     | The name of the queue                                                     |\\n| \",[\"$\",\"code\",null,{\"children\":\"queue_length\"}],\"       | \",[\"$\",\"code\",null,{\"children\":\"bigint\"}],\"                   | Number of messages currently in the queue                                 |\\n| \",[\"$\",\"code\",null,{\"children\":\"newest_msg_age_sec\"}],\" | \",[\"$\",\"code\",null,{\"children\":\"integer                   | null\"}],\"                                                                     | Age of the newest message in the queue, in seconds |\\n| \",[\"$\",\"code\",null,{\"children\":\"oldest_msg_age_sec\"}],\" | \",[\"$\",\"code\",null,{\"children\":\"integer                   | null\"}],\"                                                                     | Age of the oldest message in the queue, in seconds |\\n| \",[\"$\",\"code\",null,{\"children\":\"total_messages\"}],\"     | \",[\"$\",\"code\",null,{\"children\":\"bigint\"}],\"                   | Total number of messages that have passed through the queue over all time |\\n| \",[\"$\",\"code\",null,{\"children\":\"scrape_time\"}],\"        | \",[\"$\",\"code\",null,{\"children\":\"timestamp with time zone\"}],\" | The current timestamp                                                     |\"]}],\"\\n\",\"\\n\",\"$L52\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Types\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":[\"$\",\"code\",null,{\"children\":\"message_record\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"The complete representation of a message in a queue.\"}],\"\\n\",[\"$\",\"table\",null,{\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"children\":[[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Attribute Name\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"align\":\"left\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"msg_id\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Unique ID of the message\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"read_ct\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"bigint\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Number of times the message has been read. Increments on read().\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"enqueued_at\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"timestamp with time zone\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"time that the message was inserted into the queue\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"vt\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"timestamp with time zone\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"Timestamp when the message will become available for consumers to read\"}]]}],[\"$\",\"tr\",null,{\"children\":[[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"message\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":[\"$\",\"code\",null,{\"children\":\"jsonb\"}]}],[\"$\",\"td\",null,{\"align\":\"left\",\"children\":\"The message payload\"}]]}]]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Example:\"}],\"\\n\",\"\\n\",\"$L53\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Resources\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"Official Docs: \",[\"$\",\"a\",null,{\"href\":\"https://pgmq.github.io/pgmq/#creating-a-queue\",\"children\":\"pgmq/api\"}]]}],\"\\n\"]}]]\n"])</script><script>self.__next_f.push([1,"54:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4025\",\"static/chunks/app/guides/queues/%5B%5B...slug%5D%5D/page-9b846a4b7c38b35e.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n2c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"styl"])</script><script>self.__next_f.push([1,"e\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" extension pgmq;\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"create extension pgmq;\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"create\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" void\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.create(queue_name text)\\nreturns void\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"create\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select from pgmq.create('my_queue');\\n create\\n--------\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"create_unlogged\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" void\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.create_unlogged(queue_name text)\\nreturns void\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n30:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"c"])</script><script>self.__next_f.push([1,"lassName\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"create_unlogged\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_unlogged\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" create_unlogged\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-----------------\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select pgmq.create_unlogged('my_unlogged');\\n create_unlogged\\n-----------------\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n31:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"chil"])</script><script>self.__next_f.push([1,"dren\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"detach_archive\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.detach_archive(queue_name text)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"32:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"detach_archive\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" detach_archive\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"----------------\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.detach_archive('my_queue');\\n detach_archive\\n----------------\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"33:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"drop_queue\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"boolean\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.drop_queue(queue_name text)\\nreturns boolean\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"34:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"drop_queue\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_unlogged\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" drop_queue\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" t\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.drop_queue('my_unlogged');\\n drop_queue\\n------------\\n t\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"35:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"send\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    msg jsonb,\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delay\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"default\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.send(\\n    queue_name text,\\n    msg jsonb,\\n    delay integer default 0\\n)\\nreturns setof bigint\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"36:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"send\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"{\\\"hello\\\": \\\"world\\\"}\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"send\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"4\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.send('my_queue', '{\\\"hello\\\": \\\"world\\\"}');\\n send\\n------\\n    4\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"37:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"send_batch\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    msgs jsonb[],\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delay\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"default\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.send_batch(\\n    queue_name text,\\n    msgs jsonb[],\\n    delay integer default 0\\n)\\nreturns setof bigint\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"38:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"send_batch\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"array\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"{\\\"hello\\\": \\\"world_0\\\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"::jsonb,\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"{\\\"hello\\\": \\\"world_1\\\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"::jsonb\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    ]\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" send_batch\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"------------\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.send_batch(\\n    'my_queue',\\n    array[\\n      '{\\\"hello\\\": \\\"world_0\\\"}'::jsonb,\\n      '{\\\"hello\\\": \\\"world_1\\\"}'::jsonb\\n    ]\\n);\\n send_batch\\n------------\\n          1\\n          2\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"39:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"read\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    vt \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    qty \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"message_record\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.read(\\n    queue_name text,\\n    vt integer,\\n    qty integer\\n)\\n\\nreturns setof pgmq.message_record\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3a:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"read\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" msg_id | read_ct |          enqueued_at          |              vt               |       \"}],[\"$\",\"span\",\"90\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"message\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------+---------+-------------------------------+-------------------------------+----------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"14\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"47\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"356595\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"17\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"08\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"608922\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world_0\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"14\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"47\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"356595\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"17\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"08\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"608974\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world_1\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"rows\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.read('my_queue', 10, 2);\\n msg_id | read_ct |          enqueued_at          |              vt               |       message\\n--------+---------+-------------------------------+-------------------------------+----------------------\\n      1 |       1 | 2023-10-28 19:14:47.356595-05 | 2023-10-28 19:17:08.608922-05 | {\\\"hello\\\": \\\"world_0\\\"}\\n      2 |       1 | 2023-10-28 19:14:47.356595-05 | 2023-10-28 19:17:08.608974-05 | {\\\"hello\\\": \\\"world_1\\\"}\\n(2 rows)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3b:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"read_with_poll\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    vt \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    qty \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    max_poll_seconds \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"default\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"5\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    poll_interval_ms \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"default\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"100\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"message_record\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.read_with_poll(\\n    queue_name text,\\n    vt integer,\\n    qty integer,\\n    max_poll_seconds integer default 5,\\n    poll_interval_ms integer default 100\\n)\\nreturns setof pgmq.message_record\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"read_with_poll\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"5\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"100\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" msg_id | read_ct |          enqueued_at          |              vt               |      \"}],[\"$\",\"span\",\"89\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"message\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------+---------+-------------------------------+-------------------------------+--------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"09\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"09\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"177756\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"27\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"00\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"337929\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.read_with_poll('my_queue', 1, 1, 5, 100);\\n msg_id | read_ct |          enqueued_at          |              vt               |      message\\n--------+---------+-------------------------------+-------------------------------+--------------------\\n      1 |       1 | 2023-10-28 19:09:09.177756-05 | 2023-10-28 19:27:00.337929-05 | {\\\"hello\\\": \\\"world\\\"}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pop\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"message_record\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.pop(queue_name text)\\nreturns setof pgmq.message_record\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"# \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pop\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" msg_id | read_ct |          enqueued_at          |              vt               |      \"}],[\"$\",\"span\",\"89\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"message\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------+---------+-------------------------------+-------------------------------+--------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"09\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"09\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"177756\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"27\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"00\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"337929\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq=# select * from pgmq.pop('my_queue');\\n msg_id | read_ct |          enqueued_at          |              vt               |      message\\n--------+---------+-------------------------------+-------------------------------+--------------------\\n      1 |       2 | 2023-10-28 19:09:09.177756-05 | 2023-10-28 19:27:00.337929-05 | {\\\"hello\\\": \\\"world\\\"}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (queue_name \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", msg_id: \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"boolean\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.delete (queue_name text, msg_id: bigint)\\nreturns boolean\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"40:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"5\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" t\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select pgmq.delete('my_queue', 5);\\n delete\\n--------\\n t\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"41:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (queue_name \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", msg_ids: \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[])\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" setof \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.delete (queue_name text, msg_ids: bigint[])\\nreturns setof bigint\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"42:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"array\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[2, 3]);\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"3\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.delete('my_queue', array[2, 3]);\\n delete\\n--------\\n      2\\n      3\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"43:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"array\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[6, 999]);\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"6\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.delete('my_queue', array[6, 999]);\\n delete\\n--------\\n      6\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"44:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"purge_queue(queue_name text)\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"returns bigint\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"purge_queue(queue_name text)\\nreturns bigint\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"45:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"purge_queue\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" purge_queue\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"           \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"8\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.purge_queue('my_queue');\\n purge_queue\\n-------------\\n           8\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"46:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"archive\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", msg_id \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"boolean\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.archive(queue_name text, msg_id bigint)\\nreturns boolean\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"47:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"archive\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" archive\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"---------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"       t\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.archive('my_queue', 1);\\n archive\\n---------\\n       t\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"48:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"pgmq.archive(queue_name text, msg_ids bigint[])\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"RETURNS SETOF bigint\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.archive(queue_name text, msg_ids bigint[])\\nRETURNS SETOF bigint\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"49:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"archive\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"array\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[1, 2]);\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" archive\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"---------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"       \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"       \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.archive('my_queue', array[1, 2]);\\n archive\\n---------\\n       1\\n       2\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4a:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"archive\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"array\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"[4, 999]);\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" archive\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"---------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"       \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"4\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.archive('my_queue', array[4, 999]);\\n archive\\n---------\\n       4\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4b:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"set_vt\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    msg_id \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    vt_offset \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"message_record\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.set_vt(\\n    queue_name text,\\n    msg_id bigint,\\n    vt_offset integer\\n)\\nreturns pgmq.message_record\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"set_vt\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"11\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"30\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" msg_id | read_ct |          enqueued_at          |              vt               |       \"}],[\"$\",\"span\",\"90\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"message\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------+---------+-------------------------------+-------------------------------+----------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"     \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"42\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"21\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"778741\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"59\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"34\"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"286462\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"80\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"90\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"93\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world_0\"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.set_vt('my_queue', 11, 30);\\n msg_id | read_ct |          enqueued_at          |              vt               |       message\\n--------+---------+-------------------------------+-------------------------------+----------------------\\n     1 |       0 | 2023-10-28 19:42:21.778741-05 | 2023-10-28 19:59:34.286462-05 | {\\\"hello\\\": \\\"world_0\\\"}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"list_queues\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"RETURNS\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"TABLE\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    created_at \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"timestamp with time zone\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    is_partitioned \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"boolean\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    is_unlogged \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"boolean\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"list_queues()\\nRETURNS TABLE(\\n    queue_name text,\\n    created_at timestamp with time zone,\\n    is_partitioned boolean,\\n    is_unlogged boolean\\n)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"list_queues\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      queue_name      |          created_at           | is_partitioned | is_unlogged\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"----------------------+-------------------------------+----------------+-------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_queue             | \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"14\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"13\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"17\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"092576\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | f              | f\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_partitioned_queue | \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"47\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"37\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"098692\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | t              | f\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_unlogged          | \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"20\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"02\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"30\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"976109\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | f              | t\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.list_queues();\\n      queue_name      |          created_at           | is_partitioned | is_unlogged\\n----------------------+-------------------------------+----------------+-------------\\n my_queue             | 2023-10-28 14:13:17.092576-05 | f              | f\\n my_partitioned_queue | 2023-10-28 19:47:37.098692-05 | t              | f\\n my_unlogged          | 2023-10-28 20:02:30.976109-05 | f              | t\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"metrics\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(queue_name: \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"table\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_name \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    queue_length \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    newest_msg_age_sec \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    oldest_msg_age_sec \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"integer\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    total_messages \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    scrape_time \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"timestamp with time zone\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.metrics(queue_name: text)\\nreturns table(\\n    queue_name text,\\n    queue_length bigint,\\n    newest_msg_age_sec integer,\\n    oldest_msg_age_sec integer,\\n    total_messages bigint,\\n    scrape_time timestamp with time zone\\n)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"50:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"metrics\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_queue\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" queue_name | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"------------+--------------+--------------------+--------------------+----------------+-------------------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_queue   |           \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"16\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |               \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2445\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |               \"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2447\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |             \"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"35\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"93\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"96\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"98\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"20\"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"23\"}],[\"$\",\"span\",\"104\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"08\"}],[\"$\",\"span\",\"107\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"108\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"406259\"}],[\"$\",\"span\",\"114\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.metrics('my_queue');\\n queue_name | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time\\n------------+--------------+--------------------+--------------------+----------------+-------------------------------\\n my_queue   |           16 |               2445 |               2447 |             35 | 2023-10-28 20:23:08.406259-05\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"51:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"pgmq.metrics_all()\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"RETURNS TABLE(\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    queue_name text,\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    queue_length bigint,\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    newest_msg_age_sec integer,\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    oldest_msg_age_sec integer,\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    total_messages bigint,\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"    scrape_time timestamp with time zone\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"pgmq.metrics_all()\\nRETURNS TABLE(\\n    queue_name text,\\n    queue_length bigint,\\n    newest_msg_age_sec integer,\\n    oldest_msg_age_sec integer,\\n    total_messages bigint,\\n    scrape_time timestamp with time zone\\n)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"52:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pgmq\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"metrics_all\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      queue_name      | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"----------------------+--------------+--------------------+--------------------+----------------+-------------------------------\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_queue             |           \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"16\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |               \"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2563\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |               \"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2565\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |             \"}],[\"$\",\"span\",\"93\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"35\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"98\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"106\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"108\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"109\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"20\"}],[\"$\",\"span\",\"111\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"112\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"25\"}],[\"$\",\"span\",\"114\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"07\"}],[\"$\",\"span\",\"117\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"118\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"016413\"}],[\"$\",\"span\",\"124\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"125\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_partitioned_queue |            \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |                 \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"11\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |                 \"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"11\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |              \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"98\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"106\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"108\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"109\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"20\"}],[\"$\",\"span\",\"111\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"112\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"25\"}],[\"$\",\"span\",\"114\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"07\"}],[\"$\",\"span\",\"117\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"118\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"016413\"}],[\"$\",\"span\",\"124\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"125\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" my_unlogged          |            \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |                  \"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"3\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |                  \"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"3\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |              \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"98\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"106\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"108\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"109\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"20\"}],[\"$\",\"span\",\"111\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"112\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"25\"}],[\"$\",\"span\",\"114\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"07\"}],[\"$\",\"span\",\"117\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"118\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"016413\"}],[\"$\",\"span\",\"124\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"125\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"select * from pgmq.metrics_all();\\n      queue_name      | queue_length | newest_msg_age_sec | oldest_msg_age_sec | total_messages |          scrape_time\\n----------------------+--------------+--------------------+--------------------+----------------+-------------------------------\\n my_queue             |           16 |               2563 |               2565 |             35 | 2023-10-28 20:25:07.016413-05\\n my_partitioned_queue |            1 |                 11 |                 11 |              1 | 2023-10-28 20:25:07.016413-05\\n my_unlogged          |            1 |                  3 |                  3 |              1 | 2023-10-28 20:25:07.016413-05\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"53:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"msg_id | read_ct |          enqueued_at          |              vt               |      \"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"message\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"--------+---------+-------------------------------+-------------------------------+--------------------\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" |       \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"06\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"941509\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2023\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"10\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"28\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"19\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"06\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"27\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"419392\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"05\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" | {\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"hello\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"95\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"world\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L54\",null,{\"content\":\"msg_id | read_ct |          enqueued_at          |              vt               |      message\\n--------+---------+-------------------------------+-------------------------------+--------------------\\n      1 |       1 | 2023-10-28 19:06:19.941509-05 | 2023-10-28 19:06:27.419392-05 | {\\\"hello\\\": \\\"world\\\"}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>