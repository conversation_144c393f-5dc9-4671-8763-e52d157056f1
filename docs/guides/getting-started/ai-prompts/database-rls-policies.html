<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../../supabase-dark.svg"/><link rel="preload" as="image" href="../../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>AI Prompt: Database: Create RLS policies | Supabase Docs</title><meta name="description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><meta property="og:title" content="AI Prompt: Database: Create RLS policies | Supabase Docs"/><meta property="og:description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta property="og:url" content="https://supabase.com/docs"/><meta property="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.393Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.393Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="AI Prompt: Database: Create RLS policies | Supabase Docs"/><meta name="twitter:description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../../favicon/favicon.ico"/><link rel="icon" href="../../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../../getting-started.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.9311 6.70099C12.9311 7.27834 12.9311 8.72172 11.9311 9.29907L5.56891 12.9723C4.56891 13.5496 3.31891 12.8279 3.31891 11.6732L3.31891 4.32682C3.31891 3.17212 4.56891 2.45043 5.56891 3.02778L11.9311 6.70099ZM11.4311 8.43304C11.7644 8.24059 11.7644 7.75947 11.4311 7.56702L5.06891 3.89381C4.73558 3.70136 4.31891 3.94192 4.31891 4.32682L4.31891 11.6732C4.31891 12.0581 4.73558 12.2987 5.06891 12.1063L11.4311 8.43304Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Start with Supabase</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../features.html">Features</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../architecture.html">Architecture</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Framework Quickstarts</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/nextjs.html">Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/reactjs.html">React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/nuxtjs.html">Nuxt</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/vue.html">Vue</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/hono.html">Hono</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/flutter.html">Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/ios-swiftui.html">iOS SwiftUI</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/kotlin.html">Android Kotlin</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/sveltekit.html">SvelteKit</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/laravel.html">Laravel PHP</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/ruby-on-rails.html">Ruby on Rails</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/solidjs.html">SolidJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/redwoodjs.html">RedwoodJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstarts/refine.html">refine</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Web app demos</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-nextjs.html">Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-react.html">React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-vue-3.html">Vue 3</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-nuxt-3.html">Nuxt 3</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-angular.html">Angular</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-redwoodjs.html">RedwoodJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-solidjs.html">SolidJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-svelte.html">Svelte</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-sveltekit.html">SvelteKit</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-refine.html">refine</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Mobile tutorials</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-flutter.html">Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-expo-react-native.html">Expo React Native</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-kotlin.html">Android Kotlin</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-react.html">Ionic React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-vue.html">Ionic Vue</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-angular.html">Ionic Angular</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-swift.html">Swift</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">AI Tools</span><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ai-prompts.html">Prompts</a></li><div data-state="open" id="radix-«R1jlmtqcrlb»" role="region" aria-labelledby="radix-«Rjlmtqcrlb»" data-orientation="vertical" class="transition data-open:animate-slide-down data-closed:animate-slide-up ml-2" style="--radix-accordion-content-height:var(--radix-collapsible-content-height);--radix-accordion-content-width:var(--radix-collapsible-content-width)"><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="nextjs-supabase-auth.html">Bootstrap Next.js app with Supabase Auth</a></li><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="edge-functions.html">Writing Supabase Edge Functions</a></li><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="declarative-database-schema.html">Database: Declarative Database Schema</a></li><li><a class="cursor-pointer transition text-sm text-brand" href="database-rls-policies.html">Database: Create RLS policies</a></li><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="database-functions.html">Database: Create functions</a></li><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="database-create-migration.html">Database: Create migration</a></li><li><a class="cursor-pointer transition text-sm hover:text-brand text-foreground-lighter" href="code-format-sql.html">Postgres SQL Style Guide</a></li></div></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../mcp.html">Model context protocol (MCP)</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Getting Started</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../../getting-started.html">Getting started</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">AI Tools</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../ai-prompts.html">Prompts</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>AI Prompt: Database: Create RLS policies</p></h1><hr class="not-prose border-t-0 border-b my-8"/><h2 id="how-to-use" class="group scroll-mt-24">How to use<a href="#how-to-use" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Copy the prompt to a file in your repo.</p>
<p>Use the &quot;include file&quot; feature from your AI tool to include the prompt when chatting with your AI assistant. For example, with GitHub Copilot, use <code>#&lt;filename&gt;</code>, in Cursor, use <code>@Files</code>, and in Zed, use <code>/file</code>.</p>
<h2 id="prompt" class="group scroll-mt-24">Prompt<a href="#prompt" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div><div class="w-full">57</div><div class="w-full">58</div><div class="w-full">59</div><div class="w-full">60</div><div class="w-full">61</div><div class="w-full">62</div><div class="w-full">63</div><div class="w-full">64</div><div class="w-full">65</div><div class="w-full">66</div><div class="w-full">67</div><div class="w-full">68</div><div class="w-full">69</div><div class="w-full">70</div><div class="w-full">71</div><div class="w-full">72</div><div class="w-full">73</div><div class="w-full">74</div><div class="w-full">75</div><div class="w-full">76</div><div class="w-full">77</div><div class="w-full">78</div><div class="w-full">79</div><div class="w-full">80</div><div class="w-full">81</div><div class="w-full">82</div><div class="w-full">83</div><div class="w-full">84</div><div class="w-full">85</div><div class="w-full">86</div><div class="w-full">87</div><div class="w-full">88</div><div class="w-full">89</div><div class="w-full">90</div><div class="w-full">91</div><div class="w-full">92</div><div class="w-full">93</div><div class="w-full">94</div><div class="w-full">95</div><div class="w-full">96</div><div class="w-full">97</div><div class="w-full">98</div><div class="w-full">99</div><div class="w-full">100</div><div class="w-full">101</div><div class="w-full">102</div><div class="w-full">103</div><div class="w-full">104</div><div class="w-full">105</div><div class="w-full">106</div><div class="w-full">107</div><div class="w-full">108</div><div class="w-full">109</div><div class="w-full">110</div><div class="w-full">111</div><div class="w-full">112</div><div class="w-full">113</div><div class="w-full">114</div><div class="w-full">115</div><div class="w-full">116</div><div class="w-full">117</div><div class="w-full">118</div><div class="w-full">119</div><div class="w-full">120</div><div class="w-full">121</div><div class="w-full">122</div><div class="w-full">123</div><div class="w-full">124</div><div class="w-full">125</div><div class="w-full">126</div><div class="w-full">127</div><div class="w-full">128</div><div class="w-full">129</div><div class="w-full">130</div><div class="w-full">131</div><div class="w-full">132</div><div class="w-full">133</div><div class="w-full">134</div><div class="w-full">135</div><div class="w-full">136</div><div class="w-full">137</div><div class="w-full">138</div><div class="w-full">139</div><div class="w-full">140</div><div class="w-full">141</div><div class="w-full">142</div><div class="w-full">143</div><div class="w-full">144</div><div class="w-full">145</div><div class="w-full">146</div><div class="w-full">147</div><div class="w-full">148</div><div class="w-full">149</div><div class="w-full">150</div><div class="w-full">151</div><div class="w-full">152</div><div class="w-full">153</div><div class="w-full">154</div><div class="w-full">155</div><div class="w-full">156</div><div class="w-full">157</div><div class="w-full">158</div><div class="w-full">159</div><div class="w-full">160</div><div class="w-full">161</div><div class="w-full">162</div><div class="w-full">163</div><div class="w-full">164</div><div class="w-full">165</div><div class="w-full">166</div><div class="w-full">167</div><div class="w-full">168</div><div class="w-full">169</div><div class="w-full">170</div><div class="w-full">171</div><div class="w-full">172</div><div class="w-full">173</div><div class="w-full">174</div><div class="w-full">175</div><div class="w-full">176</div><div class="w-full">177</div><div class="w-full">178</div><div class="w-full">179</div><div class="w-full">180</div><div class="w-full">181</div><div class="w-full">182</div><div class="w-full">183</div><div class="w-full">184</div><div class="w-full">185</div><div class="w-full">186</div><div class="w-full">187</div><div class="w-full">188</div><div class="w-full">189</div><div class="w-full">190</div><div class="w-full">191</div><div class="w-full">192</div><div class="w-full">193</div><div class="w-full">194</div><div class="w-full">195</div><div class="w-full">196</div><div class="w-full">197</div><div class="w-full">198</div><div class="w-full">199</div><div class="w-full">200</div><div class="w-full">201</div><div class="w-full">202</div><div class="w-full">203</div><div class="w-full">204</div><div class="w-full">205</div><div class="w-full">206</div><div class="w-full">207</div><div class="w-full">208</div><div class="w-full">209</div><div class="w-full">210</div><div class="w-full">211</div><div class="w-full">212</div><div class="w-full">213</div><div class="w-full">214</div><div class="w-full">215</div><div class="w-full">216</div><div class="w-full">217</div><div class="w-full">218</div><div class="w-full">219</div><div class="w-full">220</div><div class="w-full">221</div><div class="w-full">222</div><div class="w-full">223</div><div class="w-full">224</div><div class="w-full">225</div><div class="w-full">226</div><div class="w-full">227</div><div class="w-full">228</div><div class="w-full">229</div><div class="w-full">230</div><div class="w-full">231</div><div class="w-full">232</div><div class="w-full">233</div><div class="w-full">234</div><div class="w-full">235</div><div class="w-full">236</div><div class="w-full">237</div><div class="w-full">238</div><div class="w-full">239</div><div class="w-full">240</div><div class="w-full">241</div><div class="w-full">242</div><div class="w-full">243</div><div class="w-full">244</div><div class="w-full">245</div><div class="w-full">246</div><div class="w-full">247</div><div class="w-full">248</div><div class="w-full">249</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-punctuation)">---</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Specify the following for Cursor rules</span></span><span class="block h-5"><span style="color:var(--code-token-function)">description</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-string)">Guidelines for writing Postgres Row Level Security policies</span></span><span class="block h-5"><span style="color:var(--code-token-function)">alwaysApply</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">false</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">---</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">#</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Database: Create RLS policies</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">You&#x27;re a Supabase Postgres expert in writing row level security policies. Your purpose is to generate a policy with the constraints given by the user. You should first retrieve schema information to write policies for, usually the &#x27;public&#x27; schema.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">The output should use the following instructions:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> The generated SQL must be valid SQL.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> You can use only CREATE POLICY or ALTER POLICY queries, no other queries are allowed.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> Always use double apostrophe in SQL strings (eg. &#x27;Night&#x27;&#x27;s watch&#x27;)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> You can add short explanations to your messages.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> The result should be a valid markdown. The SQL code should be wrapped in ``` (including sql language tag).</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> Always use &quot;auth.uid()&quot; instead of &quot;current_user&quot;.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> SELECT policies should always have USING but not WITH CHECK</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> INSERT policies should always have WITH CHECK but not USING</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> UPDATE policies should always have WITH CHECK and most often have USING</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> DELETE policies should always have USING but not WITH CHECK</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> Don&#x27;t use </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">FOR ALL</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">. Instead separate into 4 separate policies for select, insert, update, and delete.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> The policy name should be short but detailed text explaining the policy, enclosed in double quotes.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> Always put explanations as separate text. Never use inline SQL comments.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> If the user asks for something that&#x27;s not related to SQL policies, explain to the user</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  that you can only help with policies.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> Discourage </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">RESTRICTIVE</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> policies and encourage </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">PERMISSIVE</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> policies, and explain why.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">The output should look like this:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">CREATE</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">POLICY</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">My descriptive policy.</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">ON</span><span style="color:var(--code-token-string-expression)"> books </span><span style="color:var(--code-token-keyword)">FOR</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">INSERT</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated </span><span style="color:var(--code-token-keyword)">USING</span><span style="color:var(--code-token-string-expression)"> ( (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> author_id ) </span><span style="color:var(--code-token-keyword)">WITH</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Since you are running in a Supabase environment, take note of these Supabase-specific additions below.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">##</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Authenticated and unauthenticated roles</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Supabase maps every request to one of the roles:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">anon</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">: an unauthenticated request (the user is not logged in)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">authenticated</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">: an authenticated request (the user is logged in)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">These are actually </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-string)">Postgres Roles</span><span style="color:var(--code-token-punctuation)">](</span><span style="color:var(--code-foreground)">/docs/guides/database/postgres/roles</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)">. You can use these roles within your Policies using the </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">TO</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> clause:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Profiles are viewable by everyone</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">select</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated, anon</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- OR</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Public profiles are viewable only by authenticated users</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">select</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Note that </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">for ...</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> must be added after the table but before the roles. </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">to ...</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> must be added after </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">for ...</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Incorrect</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Public profiles are viewable only by authenticated users</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">select</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Correct</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Public profiles are viewable only by authenticated users</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">select</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">##</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Multiple operations</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">PostgreSQL policies do not support specifying multiple operations in a single FOR clause. You need to create separate policies for each operation.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Incorrect</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Profiles can be created and deleted by any user</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">insert</span><span style="color:var(--code-token-string-expression)">, </span><span style="color:var(--code-token-keyword)">delete</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-comment);font-style:italic">-- cannot create a policy on multiple operators</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">with</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">check</span><span style="color:var(--code-token-string-expression)"> ( true )</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Correct</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Profiles can be created by any user</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">insert</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">with</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">check</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Profiles can be deleted by any user</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">delete</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( true );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">##</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Helper functions</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Supabase provides some helper functions that make it easier to write Policies.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-function)">auth.uid()</span><span style="color:var(--code-token-punctuation)">`</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Returns the ID of the user making the request.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-function)">auth.jwt()</span><span style="color:var(--code-token-punctuation)">`</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Returns the JWT of the user making the request. Anything that you store in the user&#x27;s </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">raw_app_meta_data</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> column or the </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">raw_user_meta_data</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> column will be accessible using this function. It&#x27;s important to know the distinction between these two:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">raw_user_meta_data</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> - can be updated by the authenticated user using the </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">supabase.auth.update()</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> function. It is not a good place to store authorization data.</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">-</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">raw_app_meta_data</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> - cannot be updated by the user, so it&#x27;s a good place to store authorization data.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">The </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">auth.jwt()</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> function is extremely versatile. For example, if you store some team data inside </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">app_metadata</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">, you can use it to determine whether a particular user belongs to a team. For example, if this was an array of IDs:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">User is in team</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> my_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( team_id </span><span style="color:var(--code-token-keyword)">in</span><span style="color:var(--code-token-string-expression)"> (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">jwt</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">-&gt;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">app_metadata</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">-&gt;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">teams</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">));</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">MFA</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">The </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">auth.jwt()</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> function can be used to check for </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-string)">Multi-Factor Authentication</span><span style="color:var(--code-token-punctuation)">](</span><span style="color:var(--code-foreground)">/docs/guides/auth/auth-mfa#enforce-rules-for-mfa-logins</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)">. For example, you could restrict a user from updating their profile unless they have at least 2 levels of authentication (Assurance Level 2):</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Restrict updates.</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> profiles</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-token-string-expression)"> restrictive</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">update</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated </span><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> (</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">  (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">jwt</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-keyword)">-&gt;&gt;</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">aal</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">aal2</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">);</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">##</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">RLS performance recommendations</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Every authorization system has an impact on performance. While row level security is powerful, the performance impact is important to keep in mind. This is especially true for queries that scan every row in a table - like many </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">select</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> operations, including those using limit, offset, and ordering.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Based on a series of </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-string)">tests</span><span style="color:var(--code-token-punctuation)">](</span><span style="color:var(--code-foreground)">https://github.com/GaryAustin1/RLS-Performance</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)">, we have a few recommendations for RLS:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Add indexes</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Make sure you&#x27;ve added </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-string)">indexes</span><span style="color:var(--code-token-punctuation)">](</span><span style="color:var(--code-foreground)">/docs/guides/database/postgres/indexes</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> on any columns used within the Policies which are not already indexed (or primary keys). For a Policy like this:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access their own records</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> user_id );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">You can add an index like:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">index</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-function)">userid</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> btree (user_id);</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Call functions with </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-function)">select</span><span style="color:var(--code-token-punctuation)">`</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">You can use </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">select</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> statement to improve policies that use functions. For example, instead of this:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access their own records</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> user_id );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">You can do:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access their own records</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> user_id );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">This method works well for JWT functions like </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">auth.uid()</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> and </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">auth.jwt()</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> as well as </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">security definer</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> Functions. Wrapping the function causes an </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">initPlan</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> to be run by the Postgres optimizer, which allows it to &quot;cache&quot; the results per-statement, rather than calling the function on each row.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Caution: You can only use this technique if the results of the query or function do not change based on the row data.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Minimize joins</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">You can often rewrite your Policies to avoid joins between the source and the target table. Instead, try to organize your policy to fetch all the relevant data from the target table into an array or set, then you can use an </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">IN</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> or </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">ANY</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> operation in your filter.</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">For example, this is an example of a slow policy which joins the source </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">test_table</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> to the target </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">team_user</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access records belonging to their teams</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> (</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">  (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">in</span><span style="color:var(--code-token-string-expression)"> (</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> user_id</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> team_user</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">where</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">team_user</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">team_id</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> team_id </span><span style="color:var(--code-token-comment);font-style:italic">-- joins to the source &quot;test_table.team_id&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">  )</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">);</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">We can rewrite this to avoid this join, and instead select the filter criteria into a set:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access records belonging to their teams</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> test_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> (</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">  team_id </span><span style="color:var(--code-token-keyword)">in</span><span style="color:var(--code-token-string-expression)"> (</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> team_id</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> team_user</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">    </span><span style="color:var(--code-token-keyword)">where</span><span style="color:var(--code-token-string-expression)"> user_id </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-comment);font-style:italic">-- no join</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">  )</span></span><span class="block h-5"><span style="color:var(--code-token-string-expression)">);</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">###</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Specify roles in your policies</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Always use the Role of inside your policies, specified by the </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">TO</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> operator. For example, instead of this query:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access their own records</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> rls_test</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> user_id );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">Use:</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span><span style="color:var(--code-foreground)">sql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Users can access their own records</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-token-string-expression)"> rls_test</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-token-string-expression)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-token-string-expression)"> ( (</span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">auth</span><span style="color:var(--code-token-string-expression)">.</span><span style="color:var(--code-token-constant)">uid</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-string-expression)">) </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-string-expression)"> user_id );</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">```</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">This prevents the policy </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">( (select auth.uid()) = user_id )</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> from running for any </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">anon</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> users, since the execution stops at the </span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">to authenticated</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)"> step.</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/examples/prompts/database-rls-policies.md" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNh"])</script><script>self.__next_f.push([1,"ybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/"])</script><script>self.__next_f.push([1,"chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-d"])</script><script>self.__next_f.push([1,"d96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42"])</script><script>self.__next_f.push([1,"MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a20107"])</script><script>self.__next_f.push([1,"9596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f5"])</script><script>self.__next_f.push([1,"31b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3966\",\"static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42"])</script><script>self.__next_f.push([1,"MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3966\",\"static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunk"])</script><script>self.__next_f.push([1,"s/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dp"])</script><script>self.__next_f.push([1,"l=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4"])</script><script>self.__next_f.push([1,".js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNh"])</script><script>self.__next_f.push([1,"ybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.sup"])</script><script>self.__next_f.push([1,"abase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"getting-started\",\"ai-prompts\",\"database-rls-policies\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"getting-started\",{\"children\":[\"ai-prompts\",{\"children\":[[\"slug\",\"database-rls-policies\",\"d\"],{\"children\":[\"__PAGE__\",{}]}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"getting-started\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[\"ai-prompts\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"slug\",\"database-rls-policies\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"iwKfIpVIZx8dyqtWkNtFb\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142"])</script><script>self.__next_f.push([1,"\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87"])</script><script>self.__next_f.push([1,"ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"AI Prompt: Database: Create RLS policies\"]}]]}],\"$undefined\",[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/examples/prompts/database-rls-policies.md\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-v"])</script><script>self.__next_f.push([1,"ar(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"AI Prompt: Database: Create RLS policies | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"AI Prompt: Database: Create RLS policies | Supabase Docs\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"9\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.393Z\"}],[\"$\",\"meta\",\"10\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.393Z\"}],[\"$\",\"meta\",\"11\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:title\",\"content\":\"AI Prompt: Database: Create RLS policies | Supabase Docs\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"18\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"19\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"20\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":{\"prompts\":[{\"name\":\"Bootstrap Next.js app with Supabase Auth\",\"url\":\"/guides/getting-started/ai-prompts/nextjs-supabase-auth\"},{\"name\":\"Writing Supabase Edge Functions\",\"url\":\"/guides/getting-started/ai-prompts/edge-functions\"},{\"name\":\"Database: Declarative Database Schema\",\"url\":\"/guides/getting-started/ai-prompts/declarative-database-schema\"},{\"name\":\"Database: Create RLS policies\",\"url\":\"/guides/getting-started/ai-prompts/database-rls-policies\"},{\"name\":\"Database: Create functions\",\"url\":\"/guides/getting-started/ai-prompts/database-functions\"},{\"name\":\"Database: Create migration\",\"url\":\"/guides/getting-started/ai-prompts/database-create-migration\"},{\"name\":\"Postgres SQL Style Guide\",\"url\":\"/guides/getting-started/ai-prompts/code-format-sql\"}]},\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n29:[[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"How to use\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Copy the prompt to a file in your repo.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Use the \\\"include file\\\" feature from your AI tool to include the prompt when chatting with your AI assistant. For example, with GitHub Copilot, use \",[\"$\",\"code\",null,{\"children\":\"#\u003cfilename\u003e\"}],\", in Cursor, use \",[\"$\",\"code\",null,{\"children\":\"@Files\"}],\", and in Zed, use \",[\"$\",\"code\",null,{\"children\":\"/file\"}],\".\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\""])</script><script>self.__next_f.push([1,"Prompt\"}],\"\\n\",\"$L2c\"]\n"])</script><script>self.__next_f.push([1,"2d:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6026\",\"static/chunks/app/guides/getting-started/ai-prompts/%5Bslug%5D/page-4072d3a201079596.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n2e:T20ce,"])</script><script>self.__next_f.push([1,"---\n# Specify the following for Cursor rules\ndescription: Guidelines for writing Postgres Row Level Security policies\nalwaysApply: false\n---\n\n# Database: Create RLS policies\n\nYou're a Supabase Postgres expert in writing row level security policies. Your purpose is to generate a policy with the constraints given by the user. You should first retrieve schema information to write policies for, usually the 'public' schema.\n\nThe output should use the following instructions:\n\n- The generated SQL must be valid SQL.\n- You can use only CREATE POLICY or ALTER POLICY queries, no other queries are allowed.\n- Always use double apostrophe in SQL strings (eg. 'Night''s watch')\n- You can add short explanations to your messages.\n- The result should be a valid markdown. The SQL code should be wrapped in ``` (including sql language tag).\n- Always use \"auth.uid()\" instead of \"current_user\".\n- SELECT policies should always have USING but not WITH CHECK\n- INSERT policies should always have WITH CHECK but not USING\n- UPDATE policies should always have WITH CHECK and most often have USING\n- DELETE policies should always have USING but not WITH CHECK\n- Don't use `FOR ALL`. Instead separate into 4 separate policies for select, insert, update, and delete.\n- The policy name should be short but detailed text explaining the policy, enclosed in double quotes.\n- Always put explanations as separate text. Never use inline SQL comments.\n- If the user asks for something that's not related to SQL policies, explain to the user\n  that you can only help with policies.\n- Discourage `RESTRICTIVE` policies and encourage `PERMISSIVE` policies, and explain why.\n\nThe output should look like this:\n\n```sql\nCREATE POLICY \"My descriptive policy.\" ON books FOR INSERT to authenticated USING ( (select auth.uid()) = author_id ) WITH ( true );\n```\n\nSince you are running in a Supabase environment, take note of these Supabase-specific additions below.\n\n## Authenticated and unauthenticated roles\n\nSupabase maps every request to one of the roles:\n\n- `anon`: an unauthenticated request (the user is not logged in)\n- `authenticated`: an authenticated request (the user is logged in)\n\nThese are actually [Postgres Roles](/docs/guides/database/postgres/roles). You can use these roles within your Policies using the `TO` clause:\n\n```sql\ncreate policy \"Profiles are viewable by everyone\"\non profiles\nfor select\nto authenticated, anon\nusing ( true );\n\n-- OR\n\ncreate policy \"Public profiles are viewable only by authenticated users\"\non profiles\nfor select\nto authenticated\nusing ( true );\n```\n\nNote that `for ...` must be added after the table but before the roles. `to ...` must be added after `for ...`:\n\n### Incorrect\n\n```sql\ncreate policy \"Public profiles are viewable only by authenticated users\"\non profiles\nto authenticated\nfor select\nusing ( true );\n```\n\n### Correct\n\n```sql\ncreate policy \"Public profiles are viewable only by authenticated users\"\non profiles\nfor select\nto authenticated\nusing ( true );\n```\n\n## Multiple operations\n\nPostgreSQL policies do not support specifying multiple operations in a single FOR clause. You need to create separate policies for each operation.\n\n### Incorrect\n\n```sql\ncreate policy \"Profiles can be created and deleted by any user\"\non profiles\nfor insert, delete -- cannot create a policy on multiple operators\nto authenticated\nwith check ( true )\nusing ( true );\n```\n\n### Correct\n\n```sql\ncreate policy \"Profiles can be created by any user\"\non profiles\nfor insert\nto authenticated\nwith check ( true );\n\ncreate policy \"Profiles can be deleted by any user\"\non profiles\nfor delete\nto authenticated\nusing ( true );\n```\n\n## Helper functions\n\nSupabase provides some helper functions that make it easier to write Policies.\n\n### `auth.uid()`\n\nReturns the ID of the user making the request.\n\n### `auth.jwt()`\n\nReturns the JWT of the user making the request. Anything that you store in the user's `raw_app_meta_data` column or the `raw_user_meta_data` column will be accessible using this function. It's important to know the distinction between these two:\n\n- `raw_user_meta_data` - can be updated by the authenticated user using the `supabase.auth.update()` function. It is not a good place to store authorization data.\n- `raw_app_meta_data` - cannot be updated by the user, so it's a good place to store authorization data.\n\nThe `auth.jwt()` function is extremely versatile. For example, if you store some team data inside `app_metadata`, you can use it to determine whether a particular user belongs to a team. For example, if this was an array of IDs:\n\n```sql\ncreate policy \"User is in team\"\non my_table\nto authenticated\nusing ( team_id in (select auth.jwt() -\u003e 'app_metadata' -\u003e 'teams'));\n```\n\n### MFA\n\nThe `auth.jwt()` function can be used to check for [Multi-Factor Authentication](/docs/guides/auth/auth-mfa#enforce-rules-for-mfa-logins). For example, you could restrict a user from updating their profile unless they have at least 2 levels of authentication (Assurance Level 2):\n\n```sql\ncreate policy \"Restrict updates.\"\non profiles\nas restrictive\nfor update\nto authenticated using (\n  (select auth.jwt()-\u003e\u003e'aal') = 'aal2'\n);\n```\n\n## RLS performance recommendations\n\nEvery authorization system has an impact on performance. While row level security is powerful, the performance impact is important to keep in mind. This is especially true for queries that scan every row in a table - like many `select` operations, including those using limit, offset, and ordering.\n\nBased on a series of [tests](https://github.com/GaryAustin1/RLS-Performance), we have a few recommendations for RLS:\n\n### Add indexes\n\nMake sure you've added [indexes](/docs/guides/database/postgres/indexes) on any columns used within the Policies which are not already indexed (or primary keys). For a Policy like this:\n\n```sql\ncreate policy \"Users can access their own records\" on test_table\nto authenticated\nusing ( (select auth.uid()) = user_id );\n```\n\nYou can add an index like:\n\n```sql\ncreate index userid\non test_table\nusing btree (user_id);\n```\n\n### Call functions with `select`\n\nYou can use `select` statement to improve policies that use functions. For example, instead of this:\n\n```sql\ncreate policy \"Users can access their own records\" on test_table\nto authenticated\nusing ( auth.uid() = user_id );\n```\n\nYou can do:\n\n```sql\ncreate policy \"Users can access their own records\" on test_table\nto authenticated\nusing ( (select auth.uid()) = user_id );\n```\n\nThis method works well for JWT functions like `auth.uid()` and `auth.jwt()` as well as `security definer` Functions. Wrapping the function causes an `initPlan` to be run by the Postgres optimizer, which allows it to \"cache\" the results per-statement, rather than calling the function on each row.\n\nCaution: You can only use this technique if the results of the query or function do not change based on the row data.\n\n### Minimize joins\n\nYou can often rewrite your Policies to avoid joins between the source and the target table. Instead, try to organize your policy to fetch all the relevant data from the target table into an array or set, then you can use an `IN` or `ANY` operation in your filter.\n\nFor example, this is an example of a slow policy which joins the source `test_table` to the target `team_user`:\n\n```sql\ncreate policy \"Users can access records belonging to their teams\" on test_table\nto authenticated\nusing (\n  (select auth.uid()) in (\n    select user_id\n    from team_user\n    where team_user.team_id = team_id -- joins to the source \"test_table.team_id\"\n  )\n);\n```\n\nWe can rewrite this to avoid this join, and instead select the filter criteria into a set:\n\n```sql\ncreate policy \"Users can access records belonging to their teams\" on test_table\nto authenticated\nusing (\n  team_id in (\n    select team_id\n    from team_user\n    where user_id = (select auth.uid()) -- no join\n  )\n);\n```\n\n### Specify roles in your policies\n\nAlways use the Role of inside your policies, specified by the `TO` operator. For example, instead of this query:\n\n```sql\ncreate policy \"Users can access their own records\" on rls_test\nusing ( auth.uid() = user_id );\n```\n\nUse:\n\n```sql\ncreate policy \"Users can access their own records\" on rls_test\nto authenticated\nusing ( (select auth.uid()) = user_id );\n```\n\nThis prevents the policy `( (select auth.uid()) = user_id )` from running for any `anon` users, since the execution stops at the `to authenticated` step."])</script><script>self.__next_f.push([1,"2c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}],[\"$\",\"div\",\"56\",{\"className\":\"w-full\",\"children\":57}],[\"$\",\"div\",\"57\",{\"className\":\"w-full\",\"children\":58}],[\"$\",\"div\",\"58\",{\"className\":\"w-full\",\"children\":59}],[\"$\",\"div\",\"59\",{\"className\":\"w-full\",\"children\":60}],[\"$\",\"div\",\"60\",{\"className\":\"w-full\",\"children\":61}],[\"$\",\"div\",\"61\",{\"className\":\"w-full\",\"children\":62}],[\"$\",\"div\",\"62\",{\"className\":\"w-full\",\"children\":63}],[\"$\",\"div\",\"63\",{\"className\":\"w-full\",\"children\":64}],[\"$\",\"div\",\"64\",{\"className\":\"w-full\",\"children\":65}],[\"$\",\"div\",\"65\",{\"className\":\"w-full\",\"children\":66}],[\"$\",\"div\",\"66\",{\"className\":\"w-full\",\"children\":67}],[\"$\",\"div\",\"67\",{\"className\":\"w-full\",\"children\":68}],[\"$\",\"div\",\"68\",{\"className\":\"w-full\",\"children\":69}],[\"$\",\"div\",\"69\",{\"className\":\"w-full\",\"children\":70}],[\"$\",\"div\",\"70\",{\"className\":\"w-full\",\"children\":71}],[\"$\",\"div\",\"71\",{\"className\":\"w-full\",\"children\":72}],[\"$\",\"div\",\"72\",{\"className\":\"w-full\",\"children\":73}],[\"$\",\"div\",\"73\",{\"className\":\"w-full\",\"children\":74}],[\"$\",\"div\",\"74\",{\"className\":\"w-full\",\"children\":75}],[\"$\",\"div\",\"75\",{\"className\":\"w-full\",\"children\":76}],[\"$\",\"div\",\"76\",{\"className\":\"w-full\",\"children\":77}],[\"$\",\"div\",\"77\",{\"className\":\"w-full\",\"children\":78}],[\"$\",\"div\",\"78\",{\"className\":\"w-full\",\"children\":79}],[\"$\",\"div\",\"79\",{\"className\":\"w-full\",\"children\":80}],[\"$\",\"div\",\"80\",{\"className\":\"w-full\",\"children\":81}],[\"$\",\"div\",\"81\",{\"className\":\"w-full\",\"children\":82}],[\"$\",\"div\",\"82\",{\"className\":\"w-full\",\"children\":83}],[\"$\",\"div\",\"83\",{\"className\":\"w-full\",\"children\":84}],[\"$\",\"div\",\"84\",{\"className\":\"w-full\",\"children\":85}],[\"$\",\"div\",\"85\",{\"className\":\"w-full\",\"children\":86}],[\"$\",\"div\",\"86\",{\"className\":\"w-full\",\"children\":87}],[\"$\",\"div\",\"87\",{\"className\":\"w-full\",\"children\":88}],[\"$\",\"div\",\"88\",{\"className\":\"w-full\",\"children\":89}],[\"$\",\"div\",\"89\",{\"className\":\"w-full\",\"children\":90}],[\"$\",\"div\",\"90\",{\"className\":\"w-full\",\"children\":91}],[\"$\",\"div\",\"91\",{\"className\":\"w-full\",\"children\":92}],[\"$\",\"div\",\"92\",{\"className\":\"w-full\",\"children\":93}],[\"$\",\"div\",\"93\",{\"className\":\"w-full\",\"children\":94}],[\"$\",\"div\",\"94\",{\"className\":\"w-full\",\"children\":95}],[\"$\",\"div\",\"95\",{\"className\":\"w-full\",\"children\":96}],[\"$\",\"div\",\"96\",{\"className\":\"w-full\",\"children\":97}],[\"$\",\"div\",\"97\",{\"className\":\"w-full\",\"children\":98}],[\"$\",\"div\",\"98\",{\"className\":\"w-full\",\"children\":99}],[\"$\",\"div\",\"99\",{\"className\":\"w-full\",\"children\":100}],[\"$\",\"div\",\"100\",{\"className\":\"w-full\",\"children\":101}],[\"$\",\"div\",\"101\",{\"className\":\"w-full\",\"children\":102}],[\"$\",\"div\",\"102\",{\"className\":\"w-full\",\"children\":103}],[\"$\",\"div\",\"103\",{\"className\":\"w-full\",\"children\":104}],[\"$\",\"div\",\"104\",{\"className\":\"w-full\",\"children\":105}],[\"$\",\"div\",\"105\",{\"className\":\"w-full\",\"children\":106}],[\"$\",\"div\",\"106\",{\"className\":\"w-full\",\"children\":107}],[\"$\",\"div\",\"107\",{\"className\":\"w-full\",\"children\":108}],[\"$\",\"div\",\"108\",{\"className\":\"w-full\",\"children\":109}],[\"$\",\"div\",\"109\",{\"className\":\"w-full\",\"children\":110}],[\"$\",\"div\",\"110\",{\"className\":\"w-full\",\"children\":111}],[\"$\",\"div\",\"111\",{\"className\":\"w-full\",\"children\":112}],[\"$\",\"div\",\"112\",{\"className\":\"w-full\",\"children\":113}],[\"$\",\"div\",\"113\",{\"className\":\"w-full\",\"children\":114}],[\"$\",\"div\",\"114\",{\"className\":\"w-full\",\"children\":115}],[\"$\",\"div\",\"115\",{\"className\":\"w-full\",\"children\":116}],[\"$\",\"div\",\"116\",{\"className\":\"w-full\",\"children\":117}],[\"$\",\"div\",\"117\",{\"className\":\"w-full\",\"children\":118}],[\"$\",\"div\",\"118\",{\"className\":\"w-full\",\"children\":119}],[\"$\",\"div\",\"119\",{\"className\":\"w-full\",\"children\":120}],[\"$\",\"div\",\"120\",{\"className\":\"w-full\",\"children\":121}],[\"$\",\"div\",\"121\",{\"className\":\"w-full\",\"children\":122}],[\"$\",\"div\",\"122\",{\"className\":\"w-full\",\"children\":123}],[\"$\",\"div\",\"123\",{\"className\":\"w-full\",\"children\":124}],[\"$\",\"div\",\"124\",{\"className\":\"w-full\",\"children\":125}],[\"$\",\"div\",\"125\",{\"className\":\"w-full\",\"children\":126}],[\"$\",\"div\",\"126\",{\"className\":\"w-full\",\"children\":127}],[\"$\",\"div\",\"127\",{\"className\":\"w-full\",\"children\":128}],[\"$\",\"div\",\"128\",{\"className\":\"w-full\",\"children\":129}],[\"$\",\"div\",\"129\",{\"className\":\"w-full\",\"children\":130}],[\"$\",\"div\",\"130\",{\"className\":\"w-full\",\"children\":131}],[\"$\",\"div\",\"131\",{\"className\":\"w-full\",\"children\":132}],[\"$\",\"div\",\"132\",{\"className\":\"w-full\",\"children\":133}],[\"$\",\"div\",\"133\",{\"className\":\"w-full\",\"children\":134}],[\"$\",\"div\",\"134\",{\"className\":\"w-full\",\"children\":135}],[\"$\",\"div\",\"135\",{\"className\":\"w-full\",\"children\":136}],[\"$\",\"div\",\"136\",{\"className\":\"w-full\",\"children\":137}],[\"$\",\"div\",\"137\",{\"className\":\"w-full\",\"children\":138}],[\"$\",\"div\",\"138\",{\"className\":\"w-full\",\"children\":139}],[\"$\",\"div\",\"139\",{\"className\":\"w-full\",\"children\":140}],[\"$\",\"div\",\"140\",{\"className\":\"w-full\",\"children\":141}],[\"$\",\"div\",\"141\",{\"className\":\"w-full\",\"children\":142}],[\"$\",\"div\",\"142\",{\"className\":\"w-full\",\"children\":143}],[\"$\",\"div\",\"143\",{\"className\":\"w-full\",\"children\":144}],[\"$\",\"div\",\"144\",{\"className\":\"w-full\",\"children\":145}],[\"$\",\"div\",\"145\",{\"className\":\"w-full\",\"children\":146}],[\"$\",\"div\",\"146\",{\"className\":\"w-full\",\"children\":147}],[\"$\",\"div\",\"147\",{\"className\":\"w-full\",\"children\":148}],[\"$\",\"div\",\"148\",{\"className\":\"w-full\",\"children\":149}],[\"$\",\"div\",\"149\",{\"className\":\"w-full\",\"children\":150}],[\"$\",\"div\",\"150\",{\"className\":\"w-full\",\"children\":151}],[\"$\",\"div\",\"151\",{\"className\":\"w-full\",\"children\":152}],[\"$\",\"div\",\"152\",{\"className\":\"w-full\",\"children\":153}],[\"$\",\"div\",\"153\",{\"className\":\"w-full\",\"children\":154}],[\"$\",\"div\",\"154\",{\"className\":\"w-full\",\"children\":155}],[\"$\",\"div\",\"155\",{\"className\":\"w-full\",\"children\":156}],[\"$\",\"div\",\"156\",{\"className\":\"w-full\",\"children\":157}],[\"$\",\"div\",\"157\",{\"className\":\"w-full\",\"children\":158}],[\"$\",\"div\",\"158\",{\"className\":\"w-full\",\"children\":159}],[\"$\",\"div\",\"159\",{\"className\":\"w-full\",\"children\":160}],[\"$\",\"div\",\"160\",{\"className\":\"w-full\",\"children\":161}],[\"$\",\"div\",\"161\",{\"className\":\"w-full\",\"children\":162}],[\"$\",\"div\",\"162\",{\"className\":\"w-full\",\"children\":163}],[\"$\",\"div\",\"163\",{\"className\":\"w-full\",\"children\":164}],[\"$\",\"div\",\"164\",{\"className\":\"w-full\",\"children\":165}],[\"$\",\"div\",\"165\",{\"className\":\"w-full\",\"children\":166}],[\"$\",\"div\",\"166\",{\"className\":\"w-full\",\"children\":167}],[\"$\",\"div\",\"167\",{\"className\":\"w-full\",\"children\":168}],[\"$\",\"div\",\"168\",{\"className\":\"w-full\",\"children\":169}],[\"$\",\"div\",\"169\",{\"className\":\"w-full\",\"children\":170}],[\"$\",\"div\",\"170\",{\"className\":\"w-full\",\"children\":171}],[\"$\",\"div\",\"171\",{\"className\":\"w-full\",\"children\":172}],[\"$\",\"div\",\"172\",{\"className\":\"w-full\",\"children\":173}],[\"$\",\"div\",\"173\",{\"className\":\"w-full\",\"children\":174}],[\"$\",\"div\",\"174\",{\"className\":\"w-full\",\"children\":175}],[\"$\",\"div\",\"175\",{\"className\":\"w-full\",\"children\":176}],[\"$\",\"div\",\"176\",{\"className\":\"w-full\",\"children\":177}],[\"$\",\"div\",\"177\",{\"className\":\"w-full\",\"children\":178}],[\"$\",\"div\",\"178\",{\"className\":\"w-full\",\"children\":179}],[\"$\",\"div\",\"179\",{\"className\":\"w-full\",\"children\":180}],[\"$\",\"div\",\"180\",{\"className\":\"w-full\",\"children\":181}],[\"$\",\"div\",\"181\",{\"className\":\"w-full\",\"children\":182}],[\"$\",\"div\",\"182\",{\"className\":\"w-full\",\"children\":183}],[\"$\",\"div\",\"183\",{\"className\":\"w-full\",\"children\":184}],[\"$\",\"div\",\"184\",{\"className\":\"w-full\",\"children\":185}],[\"$\",\"div\",\"185\",{\"className\":\"w-full\",\"children\":186}],[\"$\",\"div\",\"186\",{\"className\":\"w-full\",\"children\":187}],[\"$\",\"div\",\"187\",{\"className\":\"w-full\",\"children\":188}],[\"$\",\"div\",\"188\",{\"className\":\"w-full\",\"children\":189}],[\"$\",\"div\",\"189\",{\"className\":\"w-full\",\"children\":190}],[\"$\",\"div\",\"190\",{\"className\":\"w-full\",\"children\":191}],[\"$\",\"div\",\"191\",{\"className\":\"w-full\",\"children\":192}],[\"$\",\"div\",\"192\",{\"className\":\"w-full\",\"children\":193}],[\"$\",\"div\",\"193\",{\"className\":\"w-full\",\"children\":194}],[\"$\",\"div\",\"194\",{\"className\":\"w-full\",\"children\":195}],[\"$\",\"div\",\"195\",{\"className\":\"w-full\",\"children\":196}],[\"$\",\"div\",\"196\",{\"className\":\"w-full\",\"children\":197}],[\"$\",\"div\",\"197\",{\"className\":\"w-full\",\"children\":198}],[\"$\",\"div\",\"198\",{\"className\":\"w-full\",\"children\":199}],[\"$\",\"div\",\"199\",{\"className\":\"w-full\",\"children\":200}],[\"$\",\"div\",\"200\",{\"className\":\"w-full\",\"children\":201}],[\"$\",\"div\",\"201\",{\"className\":\"w-full\",\"children\":202}],[\"$\",\"div\",\"202\",{\"className\":\"w-full\",\"children\":203}],[\"$\",\"div\",\"203\",{\"className\":\"w-full\",\"children\":204}],[\"$\",\"div\",\"204\",{\"className\":\"w-full\",\"children\":205}],[\"$\",\"div\",\"205\",{\"className\":\"w-full\",\"children\":206}],[\"$\",\"div\",\"206\",{\"className\":\"w-full\",\"children\":207}],[\"$\",\"div\",\"207\",{\"className\":\"w-full\",\"children\":208}],[\"$\",\"div\",\"208\",{\"className\":\"w-full\",\"children\":209}],[\"$\",\"div\",\"209\",{\"className\":\"w-full\",\"children\":210}],[\"$\",\"div\",\"210\",{\"className\":\"w-full\",\"children\":211}],[\"$\",\"div\",\"211\",{\"className\":\"w-full\",\"children\":212}],[\"$\",\"div\",\"212\",{\"className\":\"w-full\",\"children\":213}],[\"$\",\"div\",\"213\",{\"className\":\"w-full\",\"children\":214}],[\"$\",\"div\",\"214\",{\"className\":\"w-full\",\"children\":215}],[\"$\",\"div\",\"215\",{\"className\":\"w-full\",\"children\":216}],[\"$\",\"div\",\"216\",{\"className\":\"w-full\",\"children\":217}],[\"$\",\"div\",\"217\",{\"className\":\"w-full\",\"children\":218}],[\"$\",\"div\",\"218\",{\"className\":\"w-full\",\"children\":219}],[\"$\",\"div\",\"219\",{\"className\":\"w-full\",\"children\":220}],[\"$\",\"div\",\"220\",{\"className\":\"w-full\",\"children\":221}],[\"$\",\"div\",\"221\",{\"className\":\"w-full\",\"children\":222}],[\"$\",\"div\",\"222\",{\"className\":\"w-full\",\"children\":223}],[\"$\",\"div\",\"223\",{\"className\":\"w-full\",\"children\":224}],[\"$\",\"div\",\"224\",{\"className\":\"w-full\",\"children\":225}],[\"$\",\"div\",\"225\",{\"className\":\"w-full\",\"children\":226}],[\"$\",\"div\",\"226\",{\"className\":\"w-full\",\"children\":227}],[\"$\",\"div\",\"227\",{\"className\":\"w-full\",\"children\":228}],[\"$\",\"div\",\"228\",{\"className\":\"w-full\",\"children\":229}],[\"$\",\"div\",\"229\",{\"className\":\"w-full\",\"children\":230}],[\"$\",\"div\",\"230\",{\"className\":\"w-full\",\"children\":231}],[\"$\",\"div\",\"231\",{\"className\":\"w-full\",\"children\":232}],[\"$\",\"div\",\"232\",{\"className\":\"w-full\",\"children\":233}],[\"$\",\"div\",\"233\",{\"className\":\"w-full\",\"children\":234}],[\"$\",\"div\",\"234\",{\"className\":\"w-full\",\"children\":235}],[\"$\",\"div\",\"235\",{\"className\":\"w-full\",\"children\":236}],[\"$\",\"div\",\"236\",{\"className\":\"w-full\",\"children\":237}],[\"$\",\"div\",\"237\",{\"className\":\"w-full\",\"children\":238}],[\"$\",\"div\",\"238\",{\"className\":\"w-full\",\"children\":239}],[\"$\",\"div\",\"239\",{\"className\":\"w-full\",\"children\":240}],[\"$\",\"div\",\"240\",{\"className\":\"w-full\",\"children\":241}],[\"$\",\"div\",\"241\",{\"className\":\"w-full\",\"children\":242}],[\"$\",\"div\",\"242\",{\"className\":\"w-full\",\"children\":243}],[\"$\",\"div\",\"243\",{\"className\":\"w-full\",\"children\":244}],[\"$\",\"div\",\"244\",{\"className\":\"w-full\",\"children\":245}],[\"$\",\"div\",\"245\",{\"className\":\"w-full\",\"children\":246}],[\"$\",\"div\",\"246\",{\"className\":\"w-full\",\"children\":247}],[\"$\",\"div\",\"247\",{\"className\":\"w-full\",\"children\":248}],[\"$\",\"div\",\"248\",{\"className\":\"w-full\",\"children\":249}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"---\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Specify the following for Cursor rules\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"description\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Guidelines for writing Postgres Row Level Security policies\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"alwaysApply\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"false\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"---\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"#\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Database: Create RLS policies\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"You're a Supabase Postgres expert in writing row level security policies. Your purpose is to generate a policy with the constraints given by the user. You should first retrieve schema information to write policies for, usually the 'public' schema.\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"The output should use the following instructions:\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" The generated SQL must be valid SQL.\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" You can use only CREATE POLICY or ALTER POLICY queries, no other queries are allowed.\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Always use double apostrophe in SQL strings (eg. 'Night''s watch')\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" You can add short explanations to your messages.\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" The result should be a valid markdown. The SQL code should be wrapped in ``` (including sql language tag).\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Always use \\\"auth.uid()\\\" instead of \\\"current_user\\\".\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" SELECT policies should always have USING but not WITH CHECK\"}]]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" INSERT policies should always have WITH CHECK but not USING\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" UPDATE policies should always have WITH CHECK and most often have USING\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" DELETE policies should always have USING but not WITH CHECK\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Don't use \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"FOR ALL\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\". Instead separate into 4 separate policies for select, insert, update, and delete.\"}]]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" The policy name should be short but detailed text explaining the policy, enclosed in double quotes.\"}]]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Always put explanations as separate text. Never use inline SQL comments.\"}]]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" If the user asks for something that's not related to SQL policies, explain to the user\"}]]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  that you can only help with policies.\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Discourage \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"RESTRICTIVE\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" policies and encourage \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"PERMISSIVE\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" policies, and explain why.\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"The output should look like this:\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"CREATE\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"POLICY\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"My descriptive policy.\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"ON\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" books \"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"FOR\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"INSERT\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated \"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"USING\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( (\"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"96\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"97\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"104\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" author_id ) \"}],[\"$\",\"span\",\"118\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"WITH\"}],[\"$\",\"span\",\"122\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Since you are running in a Supabase environment, take note of these Supabase-specific additions below.\"}]]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"##\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Authenticated and unauthenticated roles\"}]]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Supabase maps every request to one of the roles:\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"anon\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": an unauthenticated request (the user is not logged in)\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"authenticated\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\": an authenticated request (the user is logged in)\"}]]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"These are actually \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Postgres Roles\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"](\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"/docs/guides/database/postgres/roles\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\". You can use these roles within your Policies using the \"}],[\"$\",\"span\",\"130\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"131\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"TO\"}],[\"$\",\"span\",\"133\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"134\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" clause:\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Profiles are viewable by everyone\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated, anon\"}]]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- OR\"}]]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Public profiles are viewable only by authenticated users\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"56\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"57\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}]]}],[\"$\",\"span\",\"58\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"59\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"60\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"61\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"62\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Note that \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"for ...\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" must be added after the table but before the roles. \"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"to ...\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"80\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" must be added after \"}],[\"$\",\"span\",\"101\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"for ...\"}],[\"$\",\"span\",\"109\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"110\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}]]}],[\"$\",\"span\",\"63\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"64\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Incorrect\"}]]}],[\"$\",\"span\",\"65\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"66\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"67\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Public profiles are viewable only by authenticated users\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"68\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"69\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"70\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}]]}],[\"$\",\"span\",\"71\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"72\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"73\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"74\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Correct\"}]]}],[\"$\",\"span\",\"75\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"76\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"77\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Public profiles are viewable only by authenticated users\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"78\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"79\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}]]}],[\"$\",\"span\",\"80\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"81\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"82\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"83\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"84\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"##\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Multiple operations\"}]]}],[\"$\",\"span\",\"85\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"86\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"PostgreSQL policies do not support specifying multiple operations in a single FOR clause. You need to create separate policies for each operation.\"}]]}],[\"$\",\"span\",\"87\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"88\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Incorrect\"}]]}],[\"$\",\"span\",\"89\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"90\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"91\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Profiles can be created and deleted by any user\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"92\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"93\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"insert\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\", \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- cannot create a policy on multiple operators\"}]]}],[\"$\",\"span\",\"94\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"95\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"with\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"check\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true )\"}]]}],[\"$\",\"span\",\"96\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"97\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"98\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"99\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Correct\"}]]}],[\"$\",\"span\",\"100\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"101\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"102\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Profiles can be created by any user\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"103\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"104\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"insert\"}]]}],[\"$\",\"span\",\"105\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"106\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"with\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"check\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"107\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"108\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Profiles can be deleted by any user\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"109\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"110\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}]]}],[\"$\",\"span\",\"111\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"112\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( true );\"}]]}],[\"$\",\"span\",\"113\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"114\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"115\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"##\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Helper functions\"}]]}],[\"$\",\"span\",\"116\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"117\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Supabase provides some helper functions that make it easier to write Policies.\"}]]}],[\"$\",\"span\",\"118\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"119\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"auth.uid()\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}]]}],[\"$\",\"span\",\"120\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"121\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Returns the ID of the user making the request.\"}]]}],[\"$\",\"span\",\"122\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"123\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"auth.jwt()\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}]]}],[\"$\",\"span\",\"124\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"125\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Returns the JWT of the user making the request. Anything that you store in the user's \"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"87\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"raw_app_meta_data\"}],[\"$\",\"span\",\"104\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" column or the \"}],[\"$\",\"span\",\"120\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"121\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"raw_user_meta_data\"}],[\"$\",\"span\",\"139\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"140\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" column will be accessible using this function. It's important to know the distinction between these two:\"}]]}],[\"$\",\"span\",\"126\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"127\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"raw_user_meta_data\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" - can be updated by the authenticated user using the \"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"supabase.auth.update()\"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" function. It is not a good place to store authorization data.\"}]]}],[\"$\",\"span\",\"128\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"-\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"raw_app_meta_data\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" - cannot be updated by the user, so it's a good place to store authorization data.\"}]]}],[\"$\",\"span\",\"129\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"130\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"The \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"auth.jwt()\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" function is extremely versatile. For example, if you store some team data inside \"}],[\"$\",\"span\",\"98\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"app_metadata\"}],[\"$\",\"span\",\"111\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"112\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", you can use it to determine whether a particular user belongs to a team. For example, if this was an array of IDs:\"}]]}],[\"$\",\"span\",\"131\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"132\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"133\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"User is in team\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"134\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" my_table\"}]]}],[\"$\",\"span\",\"135\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"136\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( team_id \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"in\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"jwt\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\u003e\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"app_metadata\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\u003e\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"teams\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"));\"}]]}],[\"$\",\"span\",\"137\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"138\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"139\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"MFA\"}]]}],[\"$\",\"span\",\"140\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"141\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"The \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"auth.jwt()\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" function can be used to check for \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Multi-Factor Authentication\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"](\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"/docs/guides/auth/auth-mfa#enforce-rules-for-mfa-logins\"}],[\"$\",\"span\",\"136\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"137\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\". For example, you could restrict a user from updating their profile unless they have at least 2 levels of authentication (Assurance Level 2):\"}]]}],[\"$\",\"span\",\"142\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"143\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"144\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Restrict updates.\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"145\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" profiles\"}]]}],[\"$\",\"span\",\"146\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" restrictive\"}]]}],[\"$\",\"span\",\"147\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"update\"}]]}],[\"$\",\"span\",\"148\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"149\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"  (\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"jwt\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"-\u003e\u003e\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"aal\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"aal2\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"150\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"151\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"152\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"153\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"##\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RLS performance recommendations\"}]]}],[\"$\",\"span\",\"154\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"155\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Every authorization system has an impact on performance. While row level security is powerful, the performance impact is important to keep in mind. This is especially true for queries that scan every row in a table - like many \"}],[\"$\",\"span\",\"227\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"228\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"select\"}],[\"$\",\"span\",\"234\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"235\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" operations, including those using limit, offset, and ordering.\"}]]}],[\"$\",\"span\",\"156\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"157\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Based on a series of \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"tests\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"](\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"https://github.com/GaryAustin1/RLS-Performance\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", we have a few recommendations for RLS:\"}]]}],[\"$\",\"span\",\"158\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"159\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Add indexes\"}]]}],[\"$\",\"span\",\"160\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"161\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Make sure you've added \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"indexes\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"](\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"/docs/guides/database/postgres/indexes\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" on any columns used within the Policies which are not already indexed (or primary keys). For a Policy like this:\"}]]}],[\"$\",\"span\",\"162\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"163\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"164\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access their own records\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"165\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"166\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( (\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id );\"}]]}],[\"$\",\"span\",\"167\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"168\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"169\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"You can add an index like:\"}]]}],[\"$\",\"span\",\"170\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"171\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"172\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"index\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"userid\"}]]}],[\"$\",\"span\",\"173\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"174\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" btree (user_id);\"}]]}],[\"$\",\"span\",\"175\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"176\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"177\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Call functions with \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"select\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}]]}],[\"$\",\"span\",\"178\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"179\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"You can use \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"select\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" statement to improve policies that use functions. For example, instead of this:\"}]]}],[\"$\",\"span\",\"180\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"181\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"182\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access their own records\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"183\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"184\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id );\"}]]}],[\"$\",\"span\",\"185\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"186\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"187\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"You can do:\"}]]}],[\"$\",\"span\",\"188\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"189\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"190\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access their own records\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"191\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"192\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( (\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id );\"}]]}],[\"$\",\"span\",\"193\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"194\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"195\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"This method works well for JWT functions like \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"auth.uid()\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" and \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"auth.jwt()\"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" as well as \"}],[\"$\",\"span\",\"87\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"security definer\"}],[\"$\",\"span\",\"104\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Functions. Wrapping the function causes an \"}],[\"$\",\"span\",\"149\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"150\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"initPlan\"}],[\"$\",\"span\",\"158\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"159\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" to be run by the Postgres optimizer, which allows it to \\\"cache\\\" the results per-statement, rather than calling the function on each row.\"}]]}],[\"$\",\"span\",\"196\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"197\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Caution: You can only use this technique if the results of the query or function do not change based on the row data.\"}]]}],[\"$\",\"span\",\"198\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"199\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Minimize joins\"}]]}],[\"$\",\"span\",\"200\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"201\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"You can often rewrite your Policies to avoid joins between the source and the target table. Instead, try to organize your policy to fetch all the relevant data from the target table into an array or set, then you can use an \"}],[\"$\",\"span\",\"224\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"225\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"IN\"}],[\"$\",\"span\",\"227\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"228\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" or \"}],[\"$\",\"span\",\"232\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"233\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"ANY\"}],[\"$\",\"span\",\"236\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"237\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" operation in your filter.\"}]]}],[\"$\",\"span\",\"202\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"203\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"For example, this is an example of a slow policy which joins the source \"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"test_table\"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" to the target \"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"team_user\"}],[\"$\",\"span\",\"109\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"110\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\":\"}]]}],[\"$\",\"span\",\"204\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"205\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"206\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access records belonging to their teams\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"207\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"208\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"209\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"  (\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"in\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"210\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id\"}]]}],[\"$\",\"span\",\"211\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" team_user\"}]]}],[\"$\",\"span\",\"212\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"where\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"team_user\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"team_id\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" team_id \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- joins to the source \\\"test_table.team_id\\\"\"}]]}],[\"$\",\"span\",\"213\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"  )\"}]]}],[\"$\",\"span\",\"214\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"215\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"216\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"217\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"We can rewrite this to avoid this join, and instead select the filter criteria into a set:\"}]]}],[\"$\",\"span\",\"218\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"219\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"220\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access records belonging to their teams\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" test_table\"}]]}],[\"$\",\"span\",\"221\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"222\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"223\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"  team_id \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"in\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"224\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" team_id\"}]]}],[\"$\",\"span\",\"225\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" team_user\"}]]}],[\"$\",\"span\",\"226\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"where\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" (\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- no join\"}]]}],[\"$\",\"span\",\"227\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"  )\"}]]}],[\"$\",\"span\",\"228\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"229\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"230\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"231\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"###\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Specify roles in your policies\"}]]}],[\"$\",\"span\",\"232\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"233\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Always use the Role of inside your policies, specified by the \"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"TO\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" operator. For example, instead of this query:\"}]]}],[\"$\",\"span\",\"234\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"235\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"236\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access their own records\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" rls_test\"}]]}],[\"$\",\"span\",\"237\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id );\"}]]}],[\"$\",\"span\",\"238\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"239\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"240\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"Use:\"}]]}],[\"$\",\"span\",\"241\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"242\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql\"}]]}],[\"$\",\"span\",\"243\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Users can access their own records\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" rls_test\"}]]}],[\"$\",\"span\",\"244\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"245\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" ( (\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"auth\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"uid\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\") \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" user_id );\"}]]}],[\"$\",\"span\",\"246\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"```\"}]]}],[\"$\",\"span\",\"247\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"248\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"This prevents the policy \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"( (select auth.uid()) = user_id )\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" from running for any \"}],[\"$\",\"span\",\"82\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"anon\"}],[\"$\",\"span\",\"87\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" users, since the execution stops at the \"}],[\"$\",\"span\",\"129\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"130\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"to authenticated\"}],[\"$\",\"span\",\"146\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"147\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" step.\"}]]}]]}]]}]}],[\"$\",\"$L2d\",null,{\"content\":\"$2e\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>