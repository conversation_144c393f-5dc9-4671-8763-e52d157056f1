<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../../supabase-dark.svg"/><link rel="preload" as="image" href="../../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU"/><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6845-f5139f1ee8df1826.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Use Supabase with Nuxt | Supabase Docs</title><meta name="description" content="Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="nuxtjs.html"/><meta property="og:title" content="Use Supabase with Nuxt | Supabase Docs"/><meta property="og:description" content="Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app."/><meta property="og:url" content="https://supabase.com/docs/guides/getting-started/quickstarts/nuxtjs"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=getting-started&amp;title=Use%20Supabase%20with%20Nuxt&amp;description=undefined"/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Use Supabase with Nuxt"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T19:13:36.523Z"/><meta property="article:modified_time" content="2025-07-31T19:13:36.523Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Use Supabase with Nuxt | Supabase Docs"/><meta name="twitter:description" content="Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../../favicon/favicon.ico"/><link rel="icon" href="../../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../../getting-started.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.9311 6.70099C12.9311 7.27834 12.9311 8.72172 11.9311 9.29907L5.56891 12.9723C4.56891 13.5496 3.31891 12.8279 3.31891 11.6732L3.31891 4.32682C3.31891 3.17212 4.56891 2.45043 5.56891 3.02778L11.9311 6.70099ZM11.4311 8.43304C11.7644 8.24059 11.7644 7.75947 11.4311 7.56702L5.06891 3.89381C4.73558 3.70136 4.31891 3.94192 4.31891 4.32682L4.31891 11.6732C4.31891 12.0581 4.73558 12.2987 5.06891 12.1063L11.4311 8.43304Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Start with Supabase</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../features.html">Features</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../architecture.html">Architecture</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Framework Quickstarts</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="nextjs.html">Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="reactjs.html">React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="nuxtjs.html">Nuxt</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="vue.html">Vue</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="hono.html">Hono</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="flutter.html">Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="ios-swiftui.html">iOS SwiftUI</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="kotlin.html">Android Kotlin</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="sveltekit.html">SvelteKit</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="laravel.html">Laravel PHP</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="ruby-on-rails.html">Ruby on Rails</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="solidjs.html">SolidJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="redwoodjs.html">RedwoodJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="refine.html">refine</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Web app demos</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-nextjs.html">Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-react.html">React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-vue-3.html">Vue 3</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-nuxt-3.html">Nuxt 3</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-angular.html">Angular</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-redwoodjs.html">RedwoodJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-solidjs.html">SolidJS</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-svelte.html">Svelte</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-sveltekit.html">SvelteKit</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-refine.html">refine</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Mobile tutorials</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-flutter.html">Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-expo-react-native.html">Expo React Native</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-kotlin.html">Android Kotlin</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-react.html">Ionic React</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-vue.html">Ionic Vue</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-ionic-angular.html">Ionic Angular</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../tutorials/with-swift.html">Swift</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">AI Tools</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ai-prompts.html">Prompts</a></li><div data-state="closed" id="radix-«R1jlmtqcrlb»" hidden="" role="region" aria-labelledby="radix-«Rjlmtqcrlb»" data-orientation="vertical" class="transition data-open:animate-slide-down data-closed:animate-slide-up ml-2" style="--radix-accordion-content-height:var(--radix-collapsible-content-height);--radix-accordion-content-width:var(--radix-collapsible-content-width)"></div></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../mcp.html">Model context protocol (MCP)</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Getting Started</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../../getting-started.html">Start with Supabase</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Framework Quickstarts</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="nuxtjs.html">Nuxt</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Use Supabase with Nuxt</p></h1><h2 class="mt-3 text-xl text-foreground-light"><p>Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.</p></h2><hr class="not-prose border-t-0 border-b my-8"/><div class="py-8"><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">1</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Create a Supabase project</h3><p>Go to <a href="https://database.new">database.new</a> and create a new Supabase project.</p><p>Alternatively, you can create a project using the Management API:</p><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># First, get your access token from https://supabase.com/dashboard/account/tokens</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">SUPABASE_ACCESS_TOKEN</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">your-access-token</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># List your organizations to get the organization ID</span></span><span class="block h-5"><span style="color:var(--code-token-function)">curl</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">-H</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Authorization: Bearer </span><span style="color:var(--code-token-parameter)">$SUPABASE_ACCESS_TOKEN</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-string)">https://api.supabase.com/v1/organizations</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Create a new project (replace &lt;org-id&gt; with your organization ID)</span></span><span class="block h-5"><span style="color:var(--code-token-function)">curl</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">-X</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">POST</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">https://api.supabase.com/v1/projects</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-constant)">-H</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Authorization: Bearer </span><span style="color:var(--code-token-parameter)">$SUPABASE_ACCESS_TOKEN</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-constant)">-H</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Content-Type: application/json</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-constant)">-d</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">{</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    &quot;organization_id&quot;: &quot;&lt;org-id&gt;&quot;,</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    &quot;name&quot;: &quot;My Project&quot;,</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    &quot;region&quot;: &quot;us-east-1&quot;,</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    &quot;db_pass&quot;: &quot;&lt;your-secure-password&gt;&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-string)">  }</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><p>When your project is up and running, go to the <a href="../../../../dashboard/project/_/%5B%5B...routeSlug%5D%5D.html">Table Editor</a>, create a new table and insert some data.</p><p>Alternatively, you can run the following snippet in your project&#x27;s <a href="../../../../dashboard/project/_/sql/%5B%5B...routeSlug%5D%5D.html">SQL Editor</a>. This will create a <code>instruments</code> table with some sample data.</p></div><div class="col-span-7 not-prose"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- Create the table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">table</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">instruments</span><span style="color:var(--code-foreground)"> (</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  id </span><span style="color:var(--code-token-keyword)">bigint</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">primary key</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">generated</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">always</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">identity</span><span style="color:var(--code-foreground)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">name</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">not null</span></span><span class="block h-5"><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- Insert some sample data into the table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">insert into</span><span style="color:var(--code-foreground)"> instruments (</span><span style="color:var(--code-token-keyword)">name</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">values</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  (</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">violin</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">),</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  (</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">viola</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">),</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  (</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">cello</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">);</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">alter</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">table</span><span style="color:var(--code-foreground)"> instruments </span><span style="color:var(--code-token-keyword)">enable</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">row</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">level</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">security</span><span style="color:var(--code-foreground)">;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base"></h3><p>Make the data in your table publicly readable by adding an RLS policy:</p></div><div class="col-span-7 not-prose"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">public can read instruments</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">public</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">instruments</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">select</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-foreground)"> anon</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-foreground)"> (true);</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">2</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Create a Nuxt app</h3><p>Create a Nuxt app using the <code>npx nuxi</code> command.</p></div><div class="col-span-7 not-prose"><div class="shiki-wrapper w-full space-y-2"><h6 class="w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground">Terminal</h6><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">npx</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">nuxi@latest</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">init</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">my-app</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div></div><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">3</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Install the Supabase client library</h3><p>The fastest way to get started is to use the <code>supabase-js</code> client library which provides a convenient interface for working with Supabase from a Nuxt app.</p><p>Navigate to the Nuxt app and install <code>supabase-js</code>.</p></div><div class="col-span-7 not-prose"><div class="shiki-wrapper w-full space-y-2"><h6 class="w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground">Terminal</h6><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">cd</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">my-app</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&amp;&amp;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">npm</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">install</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">@supabase/supabase-js</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div></div><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">4</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Declare Supabase Environment Variables</h3><p>Create a <code>.env</code> file and populate with your Supabase connection variables:</p><div class="max-w-[min(100%, 500px)] my-6"><h6 class="mt-0 mb-1 text-foreground">Project URL</h6><div class="flex flex-wrap gap-x-6"><button class="items-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-control bg-background hover:bg-accent hover:text-accent-foreground px-4 overflow-hidden h-auto min-h-10 flex justify-between border-none py-0 pl-0 pr-1 text-left" role="combobox" aria-expanded="false" type="button" aria-haspopup="dialog" aria-controls="radix-«R2l7hi9tlqtqcrlb»" data-state="closed">Loading...<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down ml-2 h-4 w-4 shrink-0 opacity-50"><path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path></svg></button></div><div class="flex items-center gap-2 mt-1"><input type="text" disabled="" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px] font-mono" value="Loading..."/><button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 py-2 px-0" disabled="" aria-label="Copy"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div class="max-w-[min(100%, 500px)] my-6"><h6 class="mt-0 mb-1 text-foreground">Anon key</h6><div class="flex flex-wrap gap-x-6"><button class="items-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-control bg-background hover:bg-accent hover:text-accent-foreground px-4 overflow-hidden h-auto min-h-10 flex justify-between border-none py-0 pl-0 pr-1 text-left" role="combobox" aria-expanded="false" type="button" aria-haspopup="dialog" aria-controls="radix-«R2n7hi9tlqtqcrlb»" data-state="closed">Loading...<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down ml-2 h-4 w-4 shrink-0 opacity-50"><path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path></svg></button></div><div class="flex items-center gap-2 mt-1"><input type="text" disabled="" class="flex w-full rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive text-sm leading-4 px-3 py-2 h-[34px] font-mono" value="Loading..."/><button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 py-2 px-0" disabled="" aria-label="Copy"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div><div class="col-span-7 not-prose"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class="flex space-x-1 flex-wrap flex-nowrap overflow-x-auto -mb-6" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«Rrhi9tlqtqcrlb»-content-.env.local" data-state="active" id="radix-«Rrhi9tlqtqcrlb»-trigger-.env.local" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-selection text-foreground border-stronger " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>.env.local</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Rrhi9tlqtqcrlb»-content-nuxt.config.tsx" data-state="inactive" id="radix-«Rrhi9tlqtqcrlb»-trigger-nuxt.config.tsx" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>nuxt.config.tsx</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rrhi9tlqtqcrlb»-trigger-.env.local" id="radix-«Rrhi9tlqtqcrlb»-content-.env.local" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span>SUPABASE_URL=&lt;SUBSTITUTE_SUPABASE_URL&gt;</span></span><span class="block h-5"><span>SUPABASE_ANON_KEY=&lt;SUBSTITUTE_SUPABASE_ANON_KEY&gt;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rrhi9tlqtqcrlb»-trigger-nuxt.config.tsx" hidden="" id="radix-«Rrhi9tlqtqcrlb»-content-nuxt.config.tsx" tabindex="0" class="focus:outline-none transition-height "></div></div></div></div></div><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">5</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Query data from the app</h3><p>In <code>app.vue</code>, create a Supabase client using your config values and replace the existing content with the following code.</p></div><div class="col-span-7 not-prose"><div class="shiki-wrapper w-full space-y-2"><h6 class="w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground">app.vue</h6><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">script</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">setup</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">createClient</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">@supabase/supabase-js</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">config</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">useRuntimeConfig</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">supabase</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">createClient</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">public</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">supabaseUrl</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">public</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">supabaseAnonKey</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">instruments</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">ref</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">[]</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">function</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">getInstruments</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">data</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">supabase</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">from</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">instruments</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">select</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">instruments</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">value</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">data</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-function)">onMounted</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-function)">getInstruments</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">script</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">template</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">ul</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">li </span><span style="color:var(--code-token-property)">v-for</span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">instrument in instruments</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-property)">:key</span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">instrument.id</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">{{ instrument.name }}</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">li</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">ul</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">template</span><span style="color:var(--code-token-punctuation)">&gt;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div></div><div class="relative pb-8 group"><div class=" absolute w-px left-[11px] pt-1 h-full "><div class=" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent "></div></div><div class=" absolute left-0 flex gap-3 items-center not-prose "><div class="flex items-center gap-6"><div class="border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm ">6</div></div></div><div class="grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12"><div class="col-span-5 ml-12 lg:ml-0"><h3 class="mt-0 text-foreground text-base">Start the app</h3><p>Start the app, navigate to <a href="http://localhost:3000">http://localhost:3000</a> in the browser, open the browser console, and you should see the list of instruments.</p></div><div class="col-span-7 not-prose"><div class="shiki-wrapper w-full space-y-2"><h6 class="w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground">Terminal</h6><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">npm</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">run</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">dev</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div></div></div></div></div>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>The community-maintained <a href="https://supabase.nuxtjs.org/">@nuxtjs/supabase</a> module provides an alternate DX for working with Supabase in Nuxt.</p></div></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/quickstarts/nuxtjs.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5"])</script><script>self.__next_f.push([1,"mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/ch"])</script><script>self.__next_f.push([1,"unks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a"])</script><script>self.__next_f.push([1,"0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFq"])</script><script>self.__next_f.push([1,"H29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?"])</script><script>self.__next_f.push([1,"dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b7798075"])</script><script>self.__next_f.push([1,"59f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3966\",\"static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5"])</script><script>self.__next_f.push([1,"mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3966\",\"static/chunks/app/guides/getting-started/layout-b3d2831af6736b71.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f"])</script><script>self.__next_f.push([1,"9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2"])</script><script>self.__next_f.push([1,"nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmA"])</script><script>self.__next_f.push([1,"KMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\""])</script><script>self.__next_f.push([1,",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0"])</script><script>self.__next_f.push([1,"be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"vX9AygYrcmcNOMiYTMgwB\",\"p\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d\",\"c\":[\"\",\"guides\",\"getting-started\",\"quickstarts\",\"nuxtjs\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"getting-started\",{\"children\":[[\"slug\",\"quickstarts/nuxtjs\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"getting-started\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"quickstarts/nuxtjs\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"aimcRcXuyDHHwRNRqc_1B\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\","])</script><script>self.__next_f.push([1,"\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Use Supabase with Nuxt\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-3 text-xl text-foreground-light\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.\"]}]]}],[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/quickstarts/nuxtjs.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 trans"])</script><script>self.__next_f.push([1,"ition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],false]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Use Supabase with Nuxt | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/getting-started/quickstarts/nuxtjs\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Use Supabase with Nuxt | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/getting-started/quickstarts/nuxtjs\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=getting-started\u0026title=Use%20Supabase%20with%20Nuxt\u0026description=undefined\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Use Supabase with Nuxt\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T19:13:36.523Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T19:13:36.523Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Use Supabase with Nuxt | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":{\"prompts\":[{\"name\":\"Bootstrap Next.js app with Supabase Auth\",\"url\":\"/guides/getting-started/ai-prompts/nextjs-supabase-auth\"},{\"name\":\"Writing Supabase Edge Functions\",\"url\":\"/guides/getting-started/ai-prompts/edge-functions\"},{\"name\":\"Database: Declarative Database Schema\",\"url\":\"/guides/getting-started/ai-prompts/declarative-database-schema\"},{\"name\":\"Database: Create RLS policies\",\"url\":\"/guides/getting-started/ai-prompts/database-rls-policies\"},{\"name\":\"Database: Create functions\",\"url\":\"/guides/getting-started/ai-prompts/database-functions\"},{\"name\":\"Database: Create migration\",\"url\":\"/guides/getting-started/ai-prompts/database-create-migration\"},{\"name\":\"Postgres SQL Style Guide\",\"url\":\"/guides/getting-started/ai-prompts/code-format-sql\"}]},\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"2f:I[51025,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ProjectConfigVariables\"]\n30:I[39651,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91"])</script><script>self.__next_f.push([1,"42\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Tabs\"]\n31:I[39651,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa"])</script><script>self.__next_f.push([1,"5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TabPanel\"]\n36:T4cd,M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"div\",null,{\"className\":\"py-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":1}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Create a Supabase project\"}],[[\"$\",\"p\",null,{\"children\":[\"Go to \",[\"$\",\"a\",null,{\"href\":\"https://database.new\",\"children\":\"database.new\"}],\" and create a new Supabase project.\"]}],[\"$\",\"p\",null,{\"children\":\"Alternatively, you can create a project using the Management API:\"}],\"$L2a\",[\"$\",\"p\",null,{\"children\":[\"When your project is up and running, go to the \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/editor\",\"children\":\"Table Editor\"}],\", create a new table and insert some data.\"]}],[\"$\",\"p\",null,{\"children\":[\"Alternatively, you can run the following snippet in your project's \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/sql/new\",\"children\":\"SQL Editor\"}],\". This will create a \",[\"$\",\"code\",null,{\"children\":\"instruments\"}],\" table with some sample data.\"]}]]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":\"$L2b\"}],[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"$undefined\"}],[\"$\",\"p\",null,{\"children\":\"Make the data in your table publicly readable by adding an RLS policy:\"}]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":\"$L2c\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":2}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Create a Nuxt app\"}],[\"$\",\"p\",null,{\"children\":[\"Create a Nuxt app using the \",[\"$\",\"code\",null,{\"children\":\"npx nuxi\"}],\" command.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":[\"$\",\"div\",null,{\"className\":\"shiki-wrapper w-full space-y-2\",\"children\":[[\"$\",\"h6\",null,{\"className\":\"w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground\",\"children\":\"Terminal\"}],\"$L2d\"]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":3}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Install the Supabase client library\"}],[[\"$\",\"p\",null,{\"children\":[\"The fastest way to get started is to use the \",[\"$\",\"code\",null,{\"children\":\"supabase-js\"}],\" client library which provides a convenient interface for working with Supabase from a Nuxt app.\"]}],[\"$\",\"p\",null,{\"children\":[\"Navigate to the Nuxt app and install \",[\"$\",\"code\",null,{\"children\":\"supabase-js\"}],\".\"]}]]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":[\"$\",\"div\",null,{\"className\":\"shiki-wrapper w-full space-y-2\",\"children\":[[\"$\",\"h6\",null,{\"className\":\"w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground\",\"children\":\"Terminal\"}],\"$L2e\"]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":4}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Declare Supabase Environment Variables\"}],[[\"$\",\"p\",null,{\"children\":[\"Create a \",[\"$\",\"code\",null,{\"children\":\".env\"}],\" file and populate with your Supabase connection variables:\"]}],[\"$\",\"$L2f\",null,{\"variable\":\"url\"}],[\"$\",\"$L2f\",null,{\"variable\":\"anonKey\"}]]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":[\"$\",\"$L30\",null,{\"listClassNames\":\"flex-nowrap overflow-x-auto -mb-6\",\"children\":[[\"$\",\"$L31\",null,{\"id\":\".env.local\",\"label\":\".env.local\",\"children\":\"$L32\"}],[\"$\",\"$L31\",null,{\"id\":\"nuxt.config.tsx\",\"label\":\"nuxt.config.tsx\",\"children\":\"$L33\"}]]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":5}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Query data from the app\"}],[\"$\",\"p\",null,{\"children\":[\"In \",[\"$\",\"code\",null,{\"children\":\"app.vue\"}],\", create a Supabase client using your config values and replace the existing content with the following code.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":[\"$\",\"div\",null,{\"className\":\"shiki-wrapper w-full space-y-2\",\"children\":[[\"$\",\"h6\",null,{\"className\":\"w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground\",\"children\":\"app.vue\"}],\"$L34\"]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"relative pb-8 group\",\"children\":[[\"$\",\"div\",null,{\"className\":\" absolute w-px left-[11px] pt-1 h-full \",\"children\":[\"$\",\"div\",null,{\"className\":\" absolute w-full h-full py-1 bg-border-control group-last:bg-transparent \"}]}],[\"$\",\"div\",null,{\"className\":\" absolute left-0 flex gap-3 items-center not-prose \",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"border bg-surface-100 border-control flex items-center justify-center rounded-full w-6 h-6 text-xs text-foreground font-normal font-mono dropshadow-sm \",\"children\":6}]}]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-12 lg:gap-10 lg:ml-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-5 ml-12 lg:ml-0\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"mt-0 text-foreground text-base\",\"children\":\"Start the app\"}],[\"$\",\"p\",null,{\"children\":[\"Start the app, navigate to \",[\"$\",\"a\",null,{\"href\":\"http://localhost:3000\",\"children\":\"http://localhost:3000\"}],\" in the browser, open the browser console, and you should see the list of instruments.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"col-span-7 not-prose\",\"children\":[\"$\",\"div\",null,{\"className\":\"shiki-wrapper w-full space-y-2\",\"children\":[[\"$\",\"h6\",null,{\"className\":\"w-fit flex items-center text-center shadow-sm rounded border border-stronger bg-selection px-2.5 py-1 text-xs text-foreground\",\"children\":\"Terminal\"}],\"$L35\"]}]}]]}]]}]]}],\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground [\u0026\u003esvg]:text-background mb-2 [\u0026\u003esvg]:bg-foreground-muted bg-surface-200/25 border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 21 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$36\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"The community-maintained \",[\"$\",\"a\",null,{\"href\":\"https://supabase.nuxtjs.org/\",\"children\":\"@nuxtjs/supabase\"}],\" module provides an alternate DX for working with Supabase in Nuxt.\"]}]}]]}]]\n"])</script><script>self.__next_f.push([1,"37:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9399\",\"static/chunks/app/guides/getting-started/%5B%5B...slug%5D%5D/page-aa471518bfbe8fed.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"CodeCopyButton\"]\n"])</script><script>self.__next_f.push([1,"2a:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# First, get your access token from https://supabase.com/dashboard/account/tokens\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"SUPABASE_ACCESS_TOKEN\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"your-access-token\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# List your organizations to get the organization ID\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"curl\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"-H\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Authorization: Bearer \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"$$SUPABASE_ACCESS_TOKEN\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"https://api.supabase.com/v1/organizations\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Create a new project (replace \u003corg-id\u003e with your organization ID)\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"curl\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"-X\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"POST\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"https://api.supabase.com/v1/projects\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"-H\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Authorization: Bearer \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"$$SUPABASE_ACCESS_TOKEN\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"-H\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Content-Type: application/json\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"-d\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    \\\"organization_id\\\": \\\"\u003corg-id\u003e\\\",\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    \\\"name\\\": \\\"My Project\\\",\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    \\\"region\\\": \\\"us-east-1\\\",\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    \\\"db_pass\\\": \\\"\u003cyour-secure-password\u003e\\\"\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"  }\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"# First, get your access token from https://supabase.com/dashboard/account/tokens\\nexport SUPABASE_ACCESS_TOKEN=\\\"your-access-token\\\"\\n\\n# List your organizations to get the organization ID\\ncurl -H \\\"Authorization: Bearer $SUPABASE_ACCESS_TOKEN\\\" \\\\\\n  https://api.supabase.com/v1/organizations\\n\\n# Create a new project (replace \u003corg-id\u003e with your organization ID)\\ncurl -X POST https://api.supabase.com/v1/projects \\\\\\n  -H \\\"Authorization: Bearer $SUPABASE_ACCESS_TOKEN\\\" \\\\\\n  -H \\\"Content-Type: application/json\\\" \\\\\\n  -d '{\\n    \\\"organization_id\\\": \\\"\u003corg-id\u003e\\\",\\n    \\\"name\\\": \\\"My Project\\\",\\n    \\\"region\\\": \\\"us-east-1\\\",\\n    \\\"db_pass\\\": \\\"\u003cyour-secure-password\u003e\\\"\\n  }'\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2b:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- Create the table\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"table\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"instruments\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  id \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"primary key\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"generated\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"always\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"identity\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"name\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"not null\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- Insert some sample data into the table\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"insert into\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" instruments (\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"name\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"values\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  (\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"violin\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"),\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  (\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"viola\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"),\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  (\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"cello\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\");\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"alter\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"table\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" instruments \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"enable\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"row\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"level\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"security\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"-- Create the table\\ncreate table instruments (\\n  id bigint primary key generated always as identity,\\n  name text not null\\n);\\n-- Insert some sample data into the table\\ninsert into instruments (name)\\nvalues\\n  ('violin'),\\n  ('viola'),\\n  ('cello');\\n\\nalter table instruments enable row level security;\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"public can read instruments\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"public\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"instruments\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" anon\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (true);\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"create policy \\\"public can read instruments\\\"\\non public.instruments\\nfor select to anon\\nusing (true);\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"npx\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"nuxi@latest\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"init\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my-app\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"npx nuxi@latest init my-app\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"cd\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my-app\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},"])</script><script>self.__next_f.push([1,"\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u0026\u0026\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"npm\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"install\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"@supabase/supabase-js\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"cd my-app \u0026\u0026 npm install @supabase/supabase-js\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n32:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"SUPABASE_URL=\u003cSUBSTITUTE_SUPABASE_URL\u003e\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"SUPABASE_ANON_KEY=\u003cSUBSTITUTE_SUPABASE_ANON_KEY\u003e\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"SUPABASE_URL=\u003cSUBSTITUTE_SUPABASE_URL\u003e\\nSUPABASE_ANON_KEY=\u003cSUBSTITUTE_SUPABASE_ANON_KEY\u003e\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"33:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"default\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"defineNuxtConfig\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"runtimeConfig\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"public\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"supabaseUrl\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"process\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_URL\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"supabaseAnonKey\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"process\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_ANON_KEY\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"export default defineNuxtConfig({\\n  runtimeConfig: {\\n    public: {\\n      supabaseUrl: process.env.SUPABASE_URL,\\n      supabaseAnonKey: process.env.SUPABASE_ANON_KEY,\\n    },\\n  },\\n});\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"34:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"script\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"setup\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"createClient\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@supabase/supabase-js\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"config\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"useRuntimeConfig\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createClient\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"config\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"public\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"supabaseUrl\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"config\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"public\"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"supabaseAnonKey\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"instruments\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ref\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[]\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"function\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"getInstruments\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"data\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"from\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"instruments\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"select\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"instruments\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"value\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onMounted\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"getInstruments\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"script\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"template\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ul\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"li \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"v-for\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"instrument in instruments\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\":key\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"instrument.id\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"{{ instrument.name }}\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"li\"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ul\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"template\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"\u003cscript setup\u003e\\nimport { createClient } from '@supabase/supabase-js'\\nconst config = useRuntimeConfig()\\nconst supabase = createClient(config.public.supabaseUrl, config.public.supabaseAnonKey)\\nconst instruments = ref([])\\n\\nasync function getInstruments() {\\n  const { data } = await supabase.from('instruments').select()\\n  instruments.value = data\\n}\\n\\nonMounted(() =\u003e {\\n  getInstruments()\\n})\\n\u003c/script\u003e\\n\\n\u003ctemplate\u003e\\n  \u003cul\u003e\\n    \u003cli v-for=\\\"instrument in instruments\\\" :key=\\\"instrument.id\\\"\u003e{{ instrument.name }}\u003c/li\u003e\\n  \u003c/ul\u003e\\n\u003c/template\u003e\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"35:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"npm\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"run\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"dev\"}]]}]]}]]}]}],[\"$\",\"$L37\",null,{\"content\":\"npm run dev\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>