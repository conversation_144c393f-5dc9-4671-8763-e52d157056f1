<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="preload" as="image" href="../../img/table-view.png"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU"/><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6845-f5139f1ee8df1826.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/guides/database/layout-e4414bc04de17de0.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Database | Supabase Docs</title><meta name="description" content="Use Supabase to manage your data."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="overview.html"/><meta property="og:title" content="Database | Supabase Docs"/><meta property="og:description" content="Use Supabase to manage your data."/><meta property="og:url" content="https://supabase.com/docs/guides/database/overview"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=database&amp;title=Database&amp;description=Use%20Supabase%20to%20manage%20your%20data."/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Database"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T19:13:36.542Z"/><meta property="article:modified_time" content="2025-07-31T19:13:36.542Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Database | Supabase Docs"/><meta name="twitter:description" content="Use Supabase to manage your data."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="overview.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 2.99915C2.5 2.17072 3.17157 1.49915 4 1.49915H12C12.8284 1.49915 13.5 2.17072 13.5 2.99915V4.99915C13.5 5.53212 13.222 6.00017 12.8032 6.26623V9.73377C13.222 9.99983 13.5 10.4679 13.5 11.0009V13.0009C13.5 13.8293 12.8284 14.5009 12 14.5009H4C3.17157 14.5009 2.5 13.8293 2.5 13.0009V11.0009C2.5 10.4615 2.78461 9.98872 3.21183 9.72437V6.27563C2.78461 6.01128 2.5 5.53845 2.5 4.99915V2.99915ZM12.0158 5.4989H3.98422C3.71538 5.49057 3.5 5.27001 3.5 4.99915V2.99915C3.5 2.723 3.72386 2.49915 4 2.49915H12C12.2761 2.49915 12.5 2.723 12.5 2.99915V4.99915C12.5 5.27001 12.2846 5.49057 12.0158 5.4989ZM4.21183 6.49915V9.4989H11.8032V6.49915H4.21183ZM4 10.5009C3.72386 10.5009 3.5 10.7247 3.5 11.0009V13.0009C3.5 13.277 3.72386 13.5009 4 13.5009H12C12.2761 13.5009 12.5 13.277 12.5 13.0009V11.0009C12.5 10.7247 12.2761 10.5009 12 10.5009H4Z" fill="currentColor"></path></svg><span class="  false text-brand">Database</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm text-brand-link" href="overview.html">Overview</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Fundamentals</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="connecting-to-postgres.html">Connecting to your database</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="import-data.html">Importing data</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="secure-data.html">Securing your data</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Working with your database (basics)</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="tables.html">Managing tables, views, and data</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="arrays.html">Working with arrays</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/indexes.html">Managing indexes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="joins-and-nesting.html">Querying joins and nested tables</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="json.html">JSON and unstructured data</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Working with your database (intermediate)</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/cascade-deletes.html">Implementing cascade deletes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/enums.html">Managing enums</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="functions.html">Managing database functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/triggers.html">Managing database triggers</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="webhooks.html">Managing database webhooks</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="full-text-search.html">Using Full Text Search</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="partitions.html">Partitioning your tables</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="connection-management.html">Managing connections</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">OrioleDB</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="orioledb.html">Overview</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Access and security</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/row-level-security.html">Row Level Security</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/column-level-security.html">Column Level Security</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="hardening-data-api.html">Hardening the Data API</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/custom-claims-and-role-based-access-control-rbac.html">Custom Claims &amp; RBAC</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/roles.html">Managing Postgres Roles</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../storage/schema/custom-roles.html">Using Custom Postgres Roles</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="vault.html">Managing secrets with Vault</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/roles-superuser.html">Superuser Access and Unsupported Operations</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Configuration, optimization, and testing</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/configuration.html">Database configuration</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="query-optimization.html">Query optimization</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="database-advisors.html">Database Advisors</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="testing.html">Testing your database</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="custom-postgres-config.html">Customizing Postgres config</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/timeouts.html">Timeouts</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="inspect.html">Debugging and monitoring</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="debugging-performance.html">Debugging performance issues</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="supavisor.html">Supavisor</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">ORM Quickstarts</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="prisma.html">Prisma</a></li><div data-state="closed" id="radix-«R36dmtqcrlb»" hidden="" role="region" aria-labelledby="radix-«R16dmtqcrlb»" data-orientation="vertical" class="transition data-open:animate-slide-down data-closed:animate-slide-up ml-2" style="--radix-accordion-content-height:var(--radix-collapsible-content-height);--radix-accordion-content-width:var(--radix-collapsible-content-width)"></div></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="drizzle.html">Drizzle</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres-js.html">Postgres.js</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">GUI quickstarts</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="pgadmin.html">pgAdmin</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="psql.html">PSQL</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="dbeaver.html">DBeaver</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="metabase.html">Metabase</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="beekeeper-studio.html">Beekeeper Studio</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Database replication</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="replication.html">Overview</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="replication/setting-up-replication.html">Setting up replication</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="replication/monitoring-replication.html">Monitoring replication</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="replication/faq.html">FAQ</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Extensions</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions.html">Overview</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/hypopg.html">HypoPG: Hypothetical indexes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/plv8.html">plv8 (deprecated)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/http.html">http: RESTful Client</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/index_advisor.html">index_advisor: Query optimization</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgaudit.html">PGAudit: Postgres Auditing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgjwt.html">pgjwt (deprecated)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgroonga.html">PGroonga: Multilingual Full Text Search</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgrouting.html">pgRouting: Geospatial Routing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_cron.html">pg_cron: Schedule Recurring Jobs</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_graphql.html">pg_graphql: GraphQL Support</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_hashids.html">pg_hashids: Short UIDs</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_jsonschema.html">pg_jsonschema: JSON Schema Validation</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_net.html">pg_net: Async Networking</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_plan_filter.html">pg_plan_filter: Restrict Total Cost</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/postgres_fdw.html">postgres_fdw: query data from an external Postgres server</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgvector.html">pgvector: Embeddings and vector similarity</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_stat_statements.html">pg_stat_statements: SQL Planning and Execution Statistics</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pg_repack.html">pg_repack: Storage Optimization</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/postgis.html">PostGIS: Geo queries</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgmq.html">pgmq: Queues</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgsodium.html">pgsodium (pending deprecation): Encryption Features</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/pgtap.html">pgTAP: Unit Testing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/plpgsql_check.html">plpgsql_check: PL/pgSQL Linter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/timescaledb.html">timescaledb (deprecated)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/uuid-ossp.html">uuid-ossp: Unique Identifiers</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/rum.html">RUM: inverted index for full-text search</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Foreign Data Wrappers</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/overview.html">Overview</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/auth0.html">Connecting to Auth0</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/airtable.html">Connecting to Airtable</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/cognito.html">Connecting to AWS Cognito</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/s3.html">Connecting to AWS S3</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/bigquery.html">Connecting to BigQuery</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/clerk.html">Connecting to Clerk</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/clickhouse.html">Connecting to ClickHouse</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/duckdb.html">Connecting to DuckDB</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/firebase.html">Connecting to Firebase</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/iceberg.html">Connecting to Iceberg</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/logflare.html">Connecting to Logflare</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/mssql.html">Connecting to MSSQL</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/notion.html">Connecting to Notion</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/paddle.html">Connecting to Paddle</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/redis.html">Connecting to Redis</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/snowflake.html">Connecting to Snowflake</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="extensions/wrappers/stripe.html">Connecting to Stripe</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Examples</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/dropping-all-tables-in-schema.html">Drop All Tables in Schema</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/first-row-in-group.html">Select First Row per Group</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/which-version-of-postgres.html">Print PostgreSQL Version</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres/setup-replication-external.html">Replicating from Supabase to External Postgres</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Database</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="overview.html">Database</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="overview.html">Overview</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Database</p></h1><hr class="not-prose border-t-0 border-b my-8"/><p>Every Supabase project comes with a full <a href="https://www.postgresql.org/">Postgres</a> database, a free and open source database which is considered one of the world&#x27;s most stable and advanced databases.</p>
<h2 id="features" class="group scroll-mt-24">Features<a href="#features" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="table-view" class="group scroll-mt-24">Table view<a href="#table-view" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You don&#x27;t have to be a database expert to start using Supabase. Our table view makes Postgres as easy to use as a spreadsheet.</p>
<p><img src="../../img/table-view.png" alt="Table View."/></p>
<h3 id="relationships" class="group scroll-mt-24">Relationships<a href="#relationships" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Dig into the relationships within your data.</p>
<video width="99%" loop="" muted="" playsInline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/relational-drilldown-zoom.mp4" type="video/mp4"/></video>
<h3 id="clone-tables" class="group scroll-mt-24">Clone tables<a href="#clone-tables" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can duplicate your tables, just like you would inside a spreadsheet.</p>
<video width="99%" muted="" playsInline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/duplicate-tables.mp4" type="video/mp4"/></video>
<h3 id="the-sql-editor" class="group scroll-mt-24">The SQL editor<a href="#the-sql-editor" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase comes with a SQL Editor. You can also save your favorite queries to run later!</p>
<video width="99%" muted="" playsInline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/favorites.mp4" type="video/mp4"/></video>
<h3 id="additional-features" class="group scroll-mt-24">Additional features<a href="#additional-features" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Supabase extends Postgres with realtime functionality using our <a href="https://github.com/supabase/realtime">Realtime Server</a>.</li>
<li>Every project is a full Postgres database, with <code>postgres</code> level access.</li>
<li>Supabase manages your database backups.</li>
<li>Import data directly from a CSV or excel spreadsheet.</li>
</ul>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>Database backups <strong>do not</strong> include objects stored via the Storage API, as the database only includes metadata about these objects. Restoring an old backup does not restore objects that have been deleted since then.</p></div></div>
<h3 id="extensions" class="group scroll-mt-24">Extensions<a href="#extensions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To expand the functionality of your Postgres database, you can use extensions.
You can enable Postgres extensions with the click of a button within the Supabase dashboard.</p>
<video width="99%" muted="" playsInline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/toggle-extensions.mp4" type="video/mp4"/></video>
<p><a href="extensions.html">Learn more</a> about all the extensions provided on Supabase.</p>
<h2 id="terminology" class="group scroll-mt-24">Terminology<a href="#terminology" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<!-- -->
<h3 id="postgres-or-postgresql" class="group scroll-mt-24">Postgres or PostgreSQL?<a href="#postgres-or-postgresql" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<!-- -->
<p>PostgreSQL the database was derived from the POSTGRES Project, a package written at the University of California at Berkeley in 1986. This package included a query language called &quot;PostQUEL&quot;.</p>
<p>In 1994, Postgres95 was built on top of POSTGRES code, adding an SQL language interpreter as a replacement for PostQUEL.</p>
<!-- -->
<p>Eventually, Postgres95 was renamed to PostgreSQL to reflect the SQL query capability.
After this, many people referred to it as Postgres since it&#x27;s less prone to confusion. Supabase is all about simplicity, so we also refer to it as Postgres.</p>
<h2 id="tips" class="group scroll-mt-24">Tips<a href="#tips" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Read about resetting your database password <a href="postgres/roles.html#passwords">here</a> and changing the timezone of your server <a href="postgres/configuration.html">here</a>.</p>
<h2 id="next-steps" class="group scroll-mt-24">Next steps<a href="#next-steps" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Read more about <a href="https://www.postgresql.org/about/">Postgres</a></li>
<li>Sign in: <a href="../../../dashboard/org.html">supabase.com/dashboard</a></li>
</ul><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/overview.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\""])</script><script>self.__next_f.push([1,",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/88"])</script><script>self.__next_f.push([1,"04-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js"])</script><script>self.__next_f.push([1,"?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU"])</script><script>self.__next_f.push([1,"\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifB"])</script><script>self.__next_f.push([1,"v5mM1txU\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29if"])</script><script>self.__next_f.push([1,"Bv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1521\",\"static/chunks/app/guides/database/layout-e4414bc04de17de0.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555"])</script><script>self.__next_f.push([1,".js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1521\",\"static/chunks/app/guides/database/layout-e4414bc04de17de0.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"31"])</script><script>self.__next_f.push([1,"7\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4"])</script><script>self.__next_f.push([1,".js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?d"])</script><script>self.__next_f.push([1,"pl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv"])</script><script>self.__next_f.push([1,"5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv"])</script><script>self.__next_f.push([1,"5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"vX9AygYrcmcNOMiYTMgwB\",\"p\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d\",\"c\":[\"\",\"guides\",\"database\",\"overview\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"database\",{\"children\":[[\"slug\",\"overview\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"database\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/9f06d6aa9c95feb3.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"overview\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"ySZBp9XN9Q5RMngWDuZyg\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static"])</script><script>self.__next_f.push([1,"/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl="])</script><script>self.__next_f.push([1,"dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Database\"]}]]}],\"$undefined\",[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/overview.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Database | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Use Supabase to manage your data.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/database/overview\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Database | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Use Supabase to manage your data.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/database/overview\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=database\u0026title=Database\u0026description=Use%20Supabase%20to%20manage%20your%20data.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Database\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T19:13:36.542Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T19:13:36.542Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Database | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Use Supabase to manage your data.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9028\",\"static/chunks/app/guides/database/%5B%5B...slug%5D%5D/page-8e1bba7aa7473bb5.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n2c:T4cd,M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.4"])</script><script>self.__next_f.push([1,"1455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"p\",null,{\"children\":[\"Every Supabase project comes with a full \",[\"$\",\"a\",null,{\"href\":\"https://www.postgresql.org/\",\"children\":\"Postgres\"}],\" database, a free and open source database which is considered one of the world's most stable and advanced databases.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Features\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Table view\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"You don't have to be a database expert to start using Supabase. Our table view makes Postgres as easy to use as a spreadsheet.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"img\",null,{\"src\":\"/docs/img/table-view.png\",\"alt\":\"Table View.\"}]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Relationships\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Dig into the relationships within your data.\"}],\"\\n\",[\"$\",\"video\",null,{\"width\":\"99%\",\"loop\":true,\"muted\":true,\"playsInline\":true,\"controls\":true,\"children\":[\"$\",\"source\",null,{\"src\":\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/relational-drilldown-zoom.mp4\",\"type\":\"video/mp4\"}]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Clone tables\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"You can duplicate your tables, just like you would inside a spreadsheet.\"}],\"\\n\",[\"$\",\"video\",null,{\"width\":\"99%\",\"muted\":true,\"playsInline\":true,\"controls\":true,\"children\":[\"$\",\"source\",null,{\"src\":\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/duplicate-tables.mp4\",\"type\":\"video/mp4\"}]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"The SQL editor\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Supabase comes with a SQL Editor. You can also save your favorite queries to run later!\"}],\"\\n\",[\"$\",\"video\",null,{\"width\":\"99%\",\"muted\":true,\"playsInline\":true,\"controls\":true,\"children\":[\"$\",\"source\",null,{\"src\":\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/favorites.mp4\",\"type\":\"video/mp4\"}]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Additional features\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"Supabase extends Postgres with realtime functionality using our \",[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/realtime\",\"children\":\"Realtime Server\"}],\".\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"Every project is a full Postgres database, with \",[\"$\",\"code\",null,{\"children\":\"postgres\"}],\" level access.\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Supabase manages your database backups.\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Import data directly from a CSV or excel spreadsheet.\"}],\"\\n\"]}],\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground [\u0026\u003esvg]:text-background mb-2 [\u0026\u003esvg]:bg-foreground-muted bg-surface-200/25 border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 21 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$2c\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"Database backups \",[\"$\",\"strong\",null,{\"children\":\"do not\"}],\" include objects stored via the Storage API, as the database only includes metadata about these objects. Restoring an old backup does not restore objects that have been deleted since then.\"]}]}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Extensions\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"To expand the functionality of your Postgres database, you can use extensions.\\nYou can enable Postgres extensions with the click of a button within the Supabase dashboard.\"}],\"\\n\",[\"$\",\"video\",null,{\"width\":\"99%\",\"muted\":true,\"playsInline\":true,\"controls\":true,\"children\":[\"$\",\"source\",null,{\"src\":\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/toggle-extensions.mp4\",\"type\":\"video/mp4\"}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[[\"$\",\"a\",null,{\"href\":\"/docs/guides/database/extensions\",\"children\":\"Learn more\"}],\" about all the extensions provided on Supabase.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Terminology\"}],\"\\n\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Postgres or PostgreSQL?\"}],\"\\n\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"PostgreSQL the database was derived from the POSTGRES Project, a package written at the University of California at Berkeley in 1986. This package included a query language called \\\"PostQUEL\\\".\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"In 1994, Postgres95 was built on top of POSTGRES code, adding an SQL language interpreter as a replacement for PostQUEL.\"}],\"\\n\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Eventually, Postgres95 was renamed to PostgreSQL to reflect the SQL query capability.\\nAfter this, many people referred to it as Postgres since it's less prone to confusion. Supabase is all about simplicity, so we also refer to it as Postgres.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Tips\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Read about resetting your database password \",[\"$\",\"a\",null,{\"href\":\"/docs/guides/database/managing-passwords\",\"children\":\"here\"}],\" and changing the timezone of your server \",[\"$\",\"a\",null,{\"href\":\"/docs/guides/database/managing-timezones\",\"children\":\"here\"}],\".\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Next steps\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"Read more about \",[\"$\",\"a\",null,{\"href\":\"https://www.postgresql.org/about/\",\"children\":\"Postgres\"}]]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"Sign in: \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard\",\"children\":\"supabase.com/dashboard\"}]]}],\"\\n\"]}]]\n"])</script></body></html>