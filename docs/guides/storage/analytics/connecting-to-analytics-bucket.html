<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../../supabase-dark.svg"/><link rel="preload" as="image" href="../../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/storage/layout-02a6ec5555893ec7.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Connecting to Analytics Buckets | Supabase Docs</title><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="connecting-to-analytics-bucket.html"/><meta property="og:title" content="Connecting to Analytics Buckets | Supabase Docs"/><meta property="og:url" content="https://supabase.com/docs/guides/storage/analytics/connecting-to-analytics-bucket"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=storage&amp;title=Connecting%20to%20Analytics%20Buckets&amp;description=undefined"/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Connecting to Analytics Buckets"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.393Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.393Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Connecting to Analytics Buckets | Supabase Docs"/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../../favicon/favicon.ico"/><link rel="icon" href="../../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../../storage.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg viewBox="0 0 16 16" width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="bevel"></path></svg><span class="  false hover:text-brand text-foreground">Storage</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../../storage.html">Overview</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstart.html">Quickstart</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Buckets</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../buckets/fundamentals.html">Fundamentals</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../buckets/creating-buckets.html">Creating Buckets</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Security</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../security/ownership.html">Ownership</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../security/access-control.html">Access Control</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Uploads</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../uploads/standard-uploads.html">Standard Uploads</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../uploads/resumable-uploads.html">Resumable Uploads</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../uploads/s3-uploads.html">S3 Uploads</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../uploads/file-limits.html">Limits</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Serving</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../serving/downloads.html">Serving assets</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../serving/image-transformations.html">Image Transformations</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../serving/bandwidth.html">Bandwidth &amp; Storage Egress</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Management</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../management/copy-move-objects.html">Copy / Move Objects</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../management/delete-objects.html">Delete Objects</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../management/pricing.html">Pricing</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">S3</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../s3/authentication.html">Authentication</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../s3/compatibility.html">API Compatibility</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Analytics Buckets</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="introduction.html">Introduction</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="creating-analytics-buckets.html">Creating Analytics Buckets</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="connecting-to-analytics-bucket.html">Connecting to Analytics Buckets</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="limits.html">Limits</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">CDN</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../cdn/fundamentals.html">Fundamentals</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../cdn/smart-cdn.html">Smart CDN</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../cdn/metrics.html">Metrics</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../debugging/logs.html">Logs</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../debugging/error-codes.html">Error Codes</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Schema</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../schema/design.html">Database Design</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../schema/helper-functions.html">Helper Functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../schema/custom-roles.html">Custom Roles</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Going to production</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../production/scaling.html">Scaling</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Storage</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../../storage.html">Storage</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Analytics Buckets</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="connecting-to-analytics-bucket.html">Connecting to Analytics Buckets</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Connecting to Analytics Buckets</p></h1><hr class="not-prose border-t-0 border-b my-8"/><div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded [&amp;&gt;svg]:text-warning-200 [&amp;&gt;svg]:bg-warning-600 mb-2 bg-alternative border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.15137 1.95117C9.30615 -0.0488281 12.1943 -0.0488281 13.3481 1.95117L20.7031 14.6992C21.8574 16.6992 20.4131 19.1992 18.104 19.1992H3.39502C1.08594 19.1992 -0.356933 16.6992 0.797364 14.6992L8.15137 1.95117ZM11.7666 16.0083C11.4971 16.2778 11.1313 16.4292 10.75 16.4292C10.3687 16.4292 10.0029 16.2778 9.7334 16.0083C9.46387 15.7388 9.3125 15.373 9.3125 14.9917C9.3125 14.9307 9.31641 14.8706 9.32373 14.811C9.33545 14.7197 9.35547 14.6304 9.38379 14.5439L9.41406 14.4609C9.48584 14.2803 9.59375 14.1147 9.7334 13.9751C10.0029 13.7056 10.3687 13.5542 10.75 13.5542C11.1313 13.5542 11.4971 13.7056 11.7666 13.9751C12.0361 14.2446 12.1875 14.6104 12.1875 14.9917C12.1875 15.373 12.0361 15.7388 11.7666 16.0083ZM10.75 4.69971C11.0317 4.69971 11.3022 4.81152 11.5015 5.01074C11.7007 5.20996 11.8125 5.48047 11.8125 5.76221V11.0747C11.8125 11.3564 11.7007 11.627 11.5015 11.8262C11.3022 12.0254 11.0317 12.1372 10.75 12.1372C10.4683 12.1372 10.1978 12.0254 9.99854 11.8262C9.79932 11.627 9.6875 11.3564 9.6875 11.0747V5.76221C9.6875 5.48047 9.79932 5.20996 9.99854 5.01074C10.1978 4.81152 10.4683 4.69971 10.75 4.69971Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>This feature is in <strong>Private Alpha</strong>. API stability and backward compatibility are not guaranteed at this stage. Reach out from this <a href="https://forms.supabase.com/analytics-buckets">Form</a> to request access</p></div></div>
<p>When interacting with Analytics Buckets, you authenticate against two main services - the Iceberg REST Catalog and the S3-Compatible Storage Endpoint.</p>
<p>The <strong>Iceberg REST Catalog</strong> acts as the central management system for Iceberg tables. It allows Iceberg clients, such as PyIceberg and Apache Spark, to perform metadata operations including:</p>
<ul>
<li>Creating and managing tables and namespaces</li>
<li>Tracking schemas and handling schema evolution</li>
<li>Managing partitions and snapshots</li>
<li>Ensuring transactional consistency and isolation</li>
</ul>
<p>The REST Catalog itself does not store the actual data. Instead, it stores metadata describing the structure, schema, and partitioning strategy of Iceberg tables.</p>
<p>Actual data storage and retrieval operations occur through the separate S3-compatible endpoint, optimized for reading and writing large analytical datasets stored in Parquet files.</p>
<h2 id="authentication" class="group scroll-mt-24">Authentication<a href="#authentication" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To connect to an Analytics Bucket, you will need</p>
<ul>
<li>
<p>An Iceberg client (Spark, PyIceberg, etc) which supports the REST Catalog interface.</p>
</li>
<li>
<p>S3 credentials to authenticate your Iceberg client with the underlying S3 Bucket.
To create S3 Credentials go to <a href="../../../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html"><strong>Project Settings &gt; Storage</strong></a>, for more information, see the <a href="../s3/authentication.html">S3 Authentication Guide</a>. We will support other authentication methods in the future.</p>
</li>
<li>
<p>The project reference and Service key for your Supabase project.
You can find your Service key in the Supabase Dashboard under <a href="../../../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html"><strong>Project Settings &gt; API</strong>.</a></p>
</li>
</ul>
<p>You will now have an <strong>Access Key</strong> and a <strong>Secret Key</strong> that you can use to authenticate your Iceberg client.</p>
<h2 id="connecting-via-pyiceberg" class="group scroll-mt-24">Connecting via PyIceberg<a href="#connecting-via-pyiceberg" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>PyIceberg is a Python client for Apache Iceberg, facilitating interaction with Iceberg Buckets.</p>
<p><strong>Installation</strong></p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">pip</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">install</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">pyiceberg</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">pyarrow</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Here&#x27;s a comprehensive example using PyIceberg with clearly separated configuration:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div><div class="w-full">57</div><div class="w-full">58</div><div class="w-full">59</div><div class="w-full">60</div><div class="w-full">61</div><div class="w-full">62</div><div class="w-full">63</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> pyiceberg</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-foreground)">catalog </span><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-foreground)"> load_catalog</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-foreground)"> pyarrow </span><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-foreground)"> pa</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-foreground)"> datetime</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Supabase project ref</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">PROJECT_REF</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">&lt;your-supabase-project-ref&gt;</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Configuration for Iceberg REST Catalog</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">WAREHOUSE</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">your-analytics-bucket-name</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">TOKEN</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SERVICE_KEY</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Configuration for S3-Compatible Storage</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_ACCESS_KEY</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">KEY</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_SECRET_KEY</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SECRET</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_REGION</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">PROJECT_REGION</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_ENDPOINT</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">f</span><span style="color:var(--code-token-string)">&quot;https://</span><span style="color:var(--code-token-constant)">{PROJECT_REF}</span><span style="color:var(--code-token-string)">.supabase.co/storage/v1/s3&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">CATALOG_URI</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">f</span><span style="color:var(--code-token-string)">&quot;https://</span><span style="color:var(--code-token-constant)">{PROJECT_REF}</span><span style="color:var(--code-token-string)">.supabase.co/storage/v1/iceberg&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Load the Iceberg catalog</span></span><span class="block h-5"><span style="color:var(--code-foreground)">catalog </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">load_catalog</span><span style="color:var(--code-token-punctuation)">(</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">analytics-bucket</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-parameter)">type</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">rest</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-parameter)">warehouse</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-constant)">WAREHOUSE</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-parameter)">uri</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-constant)">CATALOG_URI</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-parameter)">token</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-constant)">TOKEN</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-keyword)">**</span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">py-io-impl</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">pyiceberg.io.pyarrow.PyArrowFileIO</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">s3.endpoint</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_ENDPOINT</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">s3.access-key-id</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_ACCESS_KEY</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">s3.secret-access-key</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_SECRET_KEY</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">s3.region</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_REGION</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">s3.force-virtual-addressing</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">False</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Create namespace if it doesn&#x27;t exist</span></span><span class="block h-5"><span style="color:var(--code-foreground)">catalog</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">create_namespace_if_not_exists</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">default</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Define schema for your Iceberg table</span></span><span class="block h-5"><span style="color:var(--code-foreground)">schema </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">schema</span><span style="color:var(--code-token-punctuation)">([</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">field</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_id</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">int64</span><span style="color:var(--code-token-punctuation)">()),</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">field</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_name</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">string</span><span style="color:var(--code-token-punctuation)">()),</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">field</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_timestamp</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">timestamp</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">ms</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)),</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">])</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Create table (if it doesn&#x27;t exist already)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">table </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> catalog</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">create_table_if_not_exists</span><span style="color:var(--code-token-punctuation)">((</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">default</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">events</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">),</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-parameter)">schema</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-function)">schema</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Generate and insert sample data</span></span><span class="block h-5"><span style="color:var(--code-foreground)">current_time </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> datetime</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-foreground)">datetime</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">now</span><span style="color:var(--code-token-punctuation)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">data </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> pa</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">table</span><span style="color:var(--code-token-punctuation)">({</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_id</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">1</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">2</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">3</span><span style="color:var(--code-token-punctuation)">],</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_name</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">login</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">logout</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">purchase</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">],</span></span><span class="block h-5"><span style="color:var(--code-token-function)">    </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">event_timestamp</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-function)">current_time</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> current_time</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> current_time</span><span style="color:var(--code-token-punctuation)">],</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">})</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Append data to the Iceberg table</span></span><span class="block h-5"><span style="color:var(--code-foreground)">table</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">append</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-function)">data</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Scan table and print data as pandas DataFrame</span></span><span class="block h-5"><span style="color:var(--code-foreground)">df </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> table</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">scan</span><span style="color:var(--code-token-punctuation)">().</span><span style="color:var(--code-token-function)">to_pandas</span><span style="color:var(--code-token-punctuation)">()</span></span><span class="block h-5"><span style="color:var(--code-token-function)">print</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-function)">df</span><span style="color:var(--code-token-punctuation)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="connecting-via-apache-spark" class="group scroll-mt-24">Connecting via Apache Spark<a href="#connecting-via-apache-spark" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Apache Spark allows distributed analytical queries against Iceberg Buckets.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-foreground)"> pyspark</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-foreground)">sql </span><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-foreground)"> SparkSession</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Supabase project ref</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">PROJECT_REF</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">&lt;your-supabase-ref&gt;</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Configuration for Iceberg REST Catalog</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">WAREHOUSE</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">your-analytics-bucket-name</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">TOKEN</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SERVICE_KEY</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Configuration for S3-Compatible Storage</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_ACCESS_KEY</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">KEY</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_SECRET_KEY</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SECRET</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_REGION</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">PROJECT_REGION</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-constant)">S3_ENDPOINT</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">f</span><span style="color:var(--code-token-string)">&quot;https://</span><span style="color:var(--code-token-constant)">{PROJECT_REF}</span><span style="color:var(--code-token-string)">.supabase.co/storage/v1/s3&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-constant)">CATALOG_URI</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">f</span><span style="color:var(--code-token-string)">&quot;https://</span><span style="color:var(--code-token-constant)">{PROJECT_REF}</span><span style="color:var(--code-token-string)">.supabase.co/storage/v1/iceberg&quot;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># Initialize Spark session with Iceberg configuration</span></span><span class="block h-5"><span style="color:var(--code-foreground)">spark </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> SparkSession</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-foreground)">builder </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">master</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">local[*]</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">appName</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SupabaseIceberg</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.driver.host</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">127.0.0.1</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.driver.bindAddress</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">127.0.0.1</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">spark.jars.packages</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">org.apache.iceberg:iceberg-spark-runtime-3.5_2.12:1.6.1,org.apache.iceberg:iceberg-aws-bundle:1.6.1</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.extensions</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">org.apache.iceberg.spark.SparkCatalog</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.type</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">rest</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.uri</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">CATALOG_URI</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.warehouse</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">WAREHOUSE</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.token</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">TOKEN</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.s3.endpoint</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_ENDPOINT</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.s3.path-style-access</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">true</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.s3.access-key-id</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_ACCESS_KEY</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.s3.secret-access-key</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-constant)">S3_SECRET_KEY</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.catalog.my_catalog.s3.remote-signing-enabled</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">false</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">config</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">spark.sql.defaultCatalog</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-function)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">my_catalog</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">\</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">getOrCreate</span><span style="color:var(--code-token-punctuation)">()</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic"># SQL Operations</span></span><span class="block h-5"><span style="color:var(--code-foreground)">spark</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">sql</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">CREATE NAMESPACE IF NOT EXISTS analytics</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">spark</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">sql</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;&quot;&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    CREATE TABLE IF NOT EXISTS analytics.users (</span></span><span class="block h-5"><span style="color:var(--code-token-string)">        user_id BIGINT,</span></span><span class="block h-5"><span style="color:var(--code-token-string)">        username STRING</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    )</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    USING iceberg</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">&quot;&quot;&quot;</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">spark</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">sql</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;&quot;&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    INSERT INTO analytics.users (user_id, username)</span></span><span class="block h-5"><span style="color:var(--code-token-string)">    VALUES (1, &#x27;Alice&#x27;), (2, &#x27;Bob&#x27;), (3, &#x27;Charlie&#x27;)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">&quot;&quot;&quot;</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">result_df </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> spark</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">sql</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">SELECT * FROM analytics.users</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">result_df</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">show</span><span style="color:var(--code-token-punctuation)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="connecting-to-the-iceberg-rest-catalog-directly" class="group scroll-mt-24">Connecting to the Iceberg REST Catalog directly<a href="#connecting-to-the-iceberg-rest-catalog-directly" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To authenticate with the Iceberg REST Catalog directly, you need to provide a valid Supabase <strong>Service key</strong> as a Bearer token.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span>curl \</span></span><span class="block h-5"><span>  --request GET -sL \</span></span><span class="block h-5"><span>  --url &#x27;https://&lt;your-supabase-project&gt;.supabase.co/storage/v1/iceberg/v1/config?warehouse=&lt;bucket-name&gt;&#x27; \</span></span><span class="block h-5"><span>  --header &#x27;Authorization: Bearer &lt;your-service-key&gt;&#x27;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/storage/analytics/connecting-to-analytics-bucket.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\","])</script><script>self.__next_f.push([1,"\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/880"])</script><script>self.__next_f.push([1,"4-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?d"])</script><script>self.__next_f.push([1,"pl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\","])</script><script>self.__next_f.push([1,"\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEf"])</script><script>self.__next_f.push([1,"z667\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEf"])</script><script>self.__next_f.push([1,"z667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2615\",\"static/chunks/app/guides/storage/layout-02a6ec5555893ec7.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl"])</script><script>self.__next_f.push([1,"=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2615\",\"static/chunks/app/guides/storage/layout-02a6ec5555893ec7.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"stat"])</script><script>self.__next_f.push([1,"ic/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=d"])</script><script>self.__next_f.push([1,"pl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSR"])</script><script>self.__next_f.push([1,"ZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\""])</script><script>self.__next_f.push([1,"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"s"])</script><script>self.__next_f.push([1,"tyle\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"storage\",\"analytics\",\"connecting-to-analytics-bucket\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"storage\",{\"children\":[[\"slug\",\"analytics/connecting-to-analytics-bucket\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"storage\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"analytics/connecting-to-analytics-bucket\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"iBo0mnzh4zxZ8Y3Omf3W_\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/"])</script><script>self.__next_f.push([1,"chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dp"])</script><script>self.__next_f.push([1,"l_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Connecting to Analytics Buckets\"]}]]}],\"$undefined\",[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/storage/analytics/connecting-to-analytics-bucket.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var("])</script><script>self.__next_f.push([1,"--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Connecting to Analytics Buckets | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"3\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/storage/analytics/connecting-to-analytics-bucket\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"Connecting to Analytics Buckets | Supabase Docs\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/storage/analytics/connecting-to-analytics-bucket\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=storage\u0026title=Connecting%20to%20Analytics%20Buckets\u0026description=undefined\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:alt\",\"content\":\"Connecting to Analytics Buckets\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"11\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.393Z\"}],[\"$\",\"meta\",\"12\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.393Z\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"Connecting to Analytics Buckets | Supabase Docs\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"19\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2c:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2b:T45c,M8.15137 1.95117C9.30615 -0.0488281 12.1943 -0.0488281 13.3481 1.95117L20.7031 14.6992C21.8574 16.6992 20.4131 19.1992 18.104 19.1992H3.39502C1.08594 19.1992 -0.356933 16.6992 0.797364 14.6992L8.15137 1.95117ZM11.7666 16.0083C11.4971 16.2778 11.1313 16.4292 10.75 16.4292C10.3687 16.4292 10.0029 16.2778 9.7334 16.0083C9.46387 15.7388 9.3125 15.373 9.3125 14.9917C9.3125 14.9307 9.31641 14.8706 9.32373 14.811C9.33545 14.7197 9.35547 14.6304 9.38379 14.5439L9.41406 14.4609C9.48584 14.2803 9.59375 14.1147 9.7334 13.9751C10.0029 13.7056 1"])</script><script>self.__next_f.push([1,"0.3687 13.5542 10.75 13.5542C11.1313 13.5542 11.4971 13.7056 11.7666 13.9751C12.0361 14.2446 12.1875 14.6104 12.1875 14.9917C12.1875 15.373 12.0361 15.7388 11.7666 16.0083ZM10.75 4.69971C11.0317 4.69971 11.3022 4.81152 11.5015 5.01074C11.7007 5.20996 11.8125 5.48047 11.8125 5.76221V11.0747C11.8125 11.3564 11.7007 11.627 11.5015 11.8262C11.3022 12.0254 11.0317 12.1372 10.75 12.1372C10.4683 12.1372 10.1978 12.0254 9.99854 11.8262C9.79932 11.627 9.6875 11.3564 9.6875 11.0747V5.76221C9.6875 5.48047 9.79932 5.20996 9.99854 5.01074C10.1978 4.81152 10.4683 4.69971 10.75 4.69971Z"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded [\u0026\u003esvg]:text-warning-200 [\u0026\u003esvg]:bg-warning-600 mb-2 bg-alternative border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 22 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$2b\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"This feature is in \",[\"$\",\"strong\",null,{\"children\":\"Private Alpha\"}],\". API stability and backward compatibility are not guaranteed at this stage. Reach out from this \",[\"$\",\"a\",null,{\"href\":\"https://forms.supabase.com/analytics-buckets\",\"children\":\"Form\"}],\" to request access\"]}]}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"When interacting with Analytics Buckets, you authenticate against two main services - the Iceberg REST Catalog and the S3-Compatible Storage Endpoint.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"The \",[\"$\",\"strong\",null,{\"children\":\"Iceberg REST Catalog\"}],\" acts as the central management system for Iceberg tables. It allows Iceberg clients, such as PyIceberg and Apache Spark, to perform metadata operations including:\"]}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":\"Creating and managing tables and namespaces\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Tracking schemas and handling schema evolution\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Managing partitions and snapshots\"}],\"\\n\",[\"$\",\"li\",null,{\"children\":\"Ensuring transactional consistency and isolation\"}],\"\\n\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"The REST Catalog itself does not store the actual data. Instead, it stores metadata describing the structure, schema, and partitioning strategy of Iceberg tables.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Actual data storage and retrieval operations occur through the separate S3-compatible endpoint, optimized for reading and writing large analytical datasets stored in Parquet files.\"}],\"\\n\",[\"$\",\"$L2c\",null,{\"tag\":\"h2\",\"children\":\"Authentication\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"To connect to an Analytics Bucket, you will need\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"\\n\",[\"$\",\"p\",null,{\"children\":\"An Iceberg client (Spark, PyIceberg, etc) which supports the REST Catalog interface.\"}],\"\\n\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"\\n\",[\"$\",\"p\",null,{\"children\":[\"S3 credentials to authenticate your Iceberg client with the underlying S3 Bucket.\\nTo create S3 Credentials go to \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/settings/storage\",\"children\":[\"$\",\"strong\",null,{\"children\":\"Project Settings \u003e Storage\"}]}],\", for more information, see the \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/docs/guides/storage/s3/authentication\",\"children\":\"S3 Authentication Guide\"}],\". We will support other authentication methods in the future.\"]}],\"\\n\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"\\n\",[\"$\",\"p\",null,{\"children\":[\"The project reference and Service key for your Supabase project.\\nYou can find your Service key in the Supabase Dashboard under \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/settings/api-keys\",\"children\":[[\"$\",\"strong\",null,{\"children\":\"Project Settings \u003e API\"}],\".\"]}]]}],\"\\n\"]}],\"\\n\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You will now have an \",[\"$\",\"strong\",null,{\"children\":\"Access Key\"}],\" and a \",[\"$\",\"strong\",null,{\"children\":\"Secret Key\"}],\" that you can use to authenticate your Iceberg client.\"]}],\"\\n\",[\"$\",\"$L2c\",null,{\"tag\":\"h2\",\"children\":\"Connecting via PyIceberg\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"PyIceberg is a Python client for Apache Iceberg, facilitating interaction with Iceberg Buckets.\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"$\",\"strong\",null,{\"children\":\"Installation\"}]}],\"\\n\",\"$L2d\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Here's a comprehensive example using PyIceberg with clearly separated configuration:\"}],\"\\n\",\"$L2e\",\"\\n\",[\"$\",\"$L2c\",null,{\"tag\":\"h2\",\"children\":\"Connecting via Apache Spark\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Apache Spark allows distributed analytical queries against Iceberg Buckets.\"}],\"\\n\",\"$L2f\",\"\\n\",[\"$\",\"$L2c\",null,{\"tag\":\"h2\",\"children\":\"Connecting to the Iceberg REST Catalog directly\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"To authenticate with the Iceberg REST Catalog directly, you need to provide a valid Supabase \",[\"$\",\"strong\",null,{\"children\":\"Service key\"}],\" as a Bearer token.\"]}],\"\\n\",\"$L30\"]\n"])</script><script>self.__next_f.push([1,"31:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5450\",\"static/chunks/app/guides/storage/%5B%5B...slug%5D%5D/page-6b1f066fe66bea39.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"sty"])</script><script>self.__next_f.push([1,"le\":{\"color\":\"var(--code-token-function)\"},\"children\":\"pip\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"install\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"pyiceberg\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"pyarrow\"}]]}]]}]]}]}],[\"$\",\"$L31\",null,{\"content\":\"pip install pyiceberg pyarrow\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n32:T6cd,from pyiceberg.catalog import load_catalog\nimport pyarrow as pa\nimport datetime\n\n# Supabase project ref\nPROJECT_REF = \"\u003cyour-supabase-project-ref\u003e\"\n\n# Configuration for Iceberg REST Catalog\nWAREHOUSE = \"your-analytics-bucket-name\"\nTOKEN = \"SERVICE_KEY\"\n\n# Configuration for S3-Compatible Storage\nS3_ACCESS_KEY = \"KEY\"\nS3_SECRET_KEY = \"SECRET\"\nS3_REGION = \"PROJECT_REGION\"\n\nS3_ENDPOINT = f\"https://{PROJECT_REF}.supabase.co/storage/v1/s3\"\nCATALOG_URI = f\"https://{PROJECT_REF}.supabase.co/storage/v1/iceberg\"\n\n# Load the Iceberg catalog\ncatalog = load_catalog(\n    \"analytics-bucket\",\n    type=\"rest\",\n    warehouse=WAREHOUSE,\n    uri=CATALOG_URI,\n    token=TOKEN,\n    **{\n        \"py-io-impl\": \"pyiceberg.io.pyarrow.PyArrowFileIO\",\n        \"s3.endpoint\": S3_ENDPOINT,\n        \"s3.access-key-id\": S3_ACCESS_KEY,\n        \"s3.secret-access-key\": S3_SECRET_KEY,\n        \"s3.region\": S3_REGION,\n        \"s3.force-virtual-addressing\": False,\n    },\n)\n\n# Create namespace if it doesn't exist\ncatalog.create_namespace_if_not_exists(\"default\")\n\n# Define schema for your Iceberg table\nschema = pa.schema([\n    pa.field(\"event_id\", pa.int64()),\n    pa.field(\"event_name\", pa.string()),\n    pa.field(\"event_timestamp\", pa.timestamp(\"ms\")),\n])\n\n# Create table (if it doesn't exist already)\ntable = catalog.create_table_if_not_exists((\"default\", \"events\"), schem"])</script><script>self.__next_f.push([1,"a=schema)\n\n# Generate and insert sample data\ncurrent_time = datetime.datetime.now()\ndata = pa.table({\n    \"event_id\": [1, 2, 3],\n    \"event_name\": [\"login\", \"logout\", \"purchase\"],\n    \"event_timestamp\": [current_time, current_time, current_time],\n})\n\n# Append data to the Iceberg table\ntable.append(data)\n\n# Scan table and print data as pandas DataFrame\ndf = table.scan().to_pandas()\nprint(df)"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}],[\"$\",\"div\",\"56\",{\"className\":\"w-full\",\"children\":57}],[\"$\",\"div\",\"57\",{\"className\":\"w-full\",\"children\":58}],[\"$\",\"div\",\"58\",{\"className\":\"w-full\",\"children\":59}],[\"$\",\"div\",\"59\",{\"className\":\"w-full\",\"children\":60}],[\"$\",\"div\",\"60\",{\"className\":\"w-full\",\"children\":61}],[\"$\",\"div\",\"61\",{\"className\":\"w-full\",\"children\":62}],[\"$\",\"div\",\"62\",{\"className\":\"w-full\",\"children\":63}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pyiceberg\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"catalog \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" load_catalog\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pyarrow \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pa\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" datetime\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Supabase project ref\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"PROJECT_REF\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\u003cyour-supabase-project-ref\u003e\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Configuration for Iceberg REST Catalog\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"WAREHOUSE\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"your-analytics-bucket-name\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"TOKEN\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SERVICE_KEY\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Configuration for S3-Compatible Storage\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ACCESS_KEY\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"KEY\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_SECRET_KEY\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SECRET\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_REGION\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"PROJECT_REGION\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ENDPOINT\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"f\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"https://\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"{PROJECT_REF}\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".supabase.co/storage/v1/s3\\\"\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"CATALOG_URI\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"f\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"https://\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"{PROJECT_REF}\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".supabase.co/storage/v1/iceberg\\\"\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Load the Iceberg catalog\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"catalog \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"load_catalog\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"analytics-bucket\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"type\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"rest\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"warehouse\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"WAREHOUSE\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"uri\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"CATALOG_URI\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"token\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"TOKEN\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"**\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"py-io-impl\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"pyiceberg.io.pyarrow.PyArrowFileIO\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"s3.endpoint\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ENDPOINT\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"s3.access-key-id\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ACCESS_KEY\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"s3.secret-access-key\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_SECRET_KEY\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"s3.region\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_REGION\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"s3.force-virtual-addressing\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"False\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Create namespace if it doesn't exist\"}]]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"catalog\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"create_namespace_if_not_exists\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"default\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Define schema for your Iceberg table\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"schema \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pa\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"schema\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"([\"}]]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    pa\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"field\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_id\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" pa\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"int64\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()),\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    pa\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"field\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_name\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" pa\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"string\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()),\"}]]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    pa\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"field\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_timestamp\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" pa\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"timestamp\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"ms\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")),\"}]]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"])\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Create table (if it doesn't exist already)\"}]]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"table \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" catalog\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"create_table_if_not_exists\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"((\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"default\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"events\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"),\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"schema\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"schema\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Generate and insert sample data\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"current_time \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" datetime\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"datetime\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"now\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"data \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pa\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"table\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"({\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_id\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"3\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"],\"}]]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_name\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"login\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"logout\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"purchase\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"],\"}]]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"event_timestamp\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"current_time\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" current_time\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" current_time\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"],\"}]]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"})\"}]]}],[\"$\",\"span\",\"56\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"57\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Append data to the Iceberg table\"}]]}],[\"$\",\"span\",\"58\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"table\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"append\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"data\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"59\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"60\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Scan table and print data as pandas DataFrame\"}]]}],[\"$\",\"span\",\"61\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"df \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" table\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"scan\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"().\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"to_pandas\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"62\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"df\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L31\",null,{\"content\":\"$32\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"33:T86f,"])</script><script>self.__next_f.push([1,"from pyspark.sql import SparkSession\n\n# Supabase project ref\nPROJECT_REF = \"\u003cyour-supabase-ref\u003e\"\n\n# Configuration for Iceberg REST Catalog\nWAREHOUSE = \"your-analytics-bucket-name\"\nTOKEN = \"SERVICE_KEY\"\n\n# Configuration for S3-Compatible Storage\nS3_ACCESS_KEY = \"KEY\"\nS3_SECRET_KEY = \"SECRET\"\nS3_REGION = \"PROJECT_REGION\"\n\nS3_ENDPOINT = f\"https://{PROJECT_REF}.supabase.co/storage/v1/s3\"\nCATALOG_URI = f\"https://{PROJECT_REF}.supabase.co/storage/v1/iceberg\"\n\n# Initialize Spark session with Iceberg configuration\nspark = SparkSession.builder \\\n    .master(\"local[*]\") \\\n    .appName(\"SupabaseIceberg\") \\\n    .config(\"spark.driver.host\", \"127.0.0.1\") \\\n    .config(\"spark.driver.bindAddress\", \"127.0.0.1\") \\\n    .config('spark.jars.packages', 'org.apache.iceberg:iceberg-spark-runtime-3.5_2.12:1.6.1,org.apache.iceberg:iceberg-aws-bundle:1.6.1') \\\n    .config(\"spark.sql.extensions\", \"org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions\") \\\n    .config(\"spark.sql.catalog.my_catalog\", \"org.apache.iceberg.spark.SparkCatalog\") \\\n    .config(\"spark.sql.catalog.my_catalog.type\", \"rest\") \\\n    .config(\"spark.sql.catalog.my_catalog.uri\", CATALOG_URI) \\\n    .config(\"spark.sql.catalog.my_catalog.warehouse\", WAREHOUSE) \\\n    .config(\"spark.sql.catalog.my_catalog.token\", TOKEN) \\\n    .config(\"spark.sql.catalog.my_catalog.s3.endpoint\", S3_ENDPOINT) \\\n    .config(\"spark.sql.catalog.my_catalog.s3.path-style-access\", \"true\") \\\n    .config(\"spark.sql.catalog.my_catalog.s3.access-key-id\", S3_ACCESS_KEY) \\\n    .config(\"spark.sql.catalog.my_catalog.s3.secret-access-key\", S3_SECRET_KEY) \\\n    .config(\"spark.sql.catalog.my_catalog.s3.remote-signing-enabled\", \"false\") \\\n    .config(\"spark.sql.defaultCatalog\", \"my_catalog\") \\\n    .getOrCreate()\n\n# SQL Operations\nspark.sql(\"CREATE NAMESPACE IF NOT EXISTS analytics\")\n\nspark.sql(\"\"\"\n    CREATE TABLE IF NOT EXISTS analytics.users (\n        user_id BIGINT,\n        username STRING\n    )\n    USING iceberg\n\"\"\")\n\nspark.sql(\"\"\"\n    INSERT INTO analytics.users (user_id, username)\n    VALUES (1, 'Alice'), (2, 'Bob'), (3, 'Charlie')\n\"\"\")\n\nresult_df = spark.sql(\"SELECT * FROM analytics.users\")\nresult_df.show()"])</script><script>self.__next_f.push([1,"2f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" pyspark\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"sql \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" SparkSession\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Supabase project ref\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"PROJECT_REF\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\u003cyour-supabase-ref\u003e\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Configuration for Iceberg REST Catalog\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"WAREHOUSE\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"your-analytics-bucket-name\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"TOKEN\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SERVICE_KEY\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Configuration for S3-Compatible Storage\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ACCESS_KEY\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"KEY\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_SECRET_KEY\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SECRET\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_REGION\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"PROJECT_REGION\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ENDPOINT\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"f\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"https://\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"{PROJECT_REF}\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".supabase.co/storage/v1/s3\\\"\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"CATALOG_URI\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"f\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"https://\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"{PROJECT_REF}\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".supabase.co/storage/v1/iceberg\\\"\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# Initialize Spark session with Iceberg configuration\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"spark \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" SparkSession\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"builder \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"master\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"local[*]\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"appName\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SupabaseIceberg\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.driver.host\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"127.0.0.1\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.driver.bindAddress\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"127.0.0.1\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.jars.packages\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"org.apache.iceberg:iceberg-spark-runtime-3.5_2.12:1.6.1,org.apache.iceberg:iceberg-aws-bundle:1.6.1\"}],[\"$\",\"span\",\"135\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"136\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"137\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"138\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.extensions\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions\"}],[\"$\",\"span\",\"102\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"103\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"104\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"105\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"org.apache.iceberg.spark.SparkCatalog\"}],[\"$\",\"span\",\"82\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"85\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.type\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"rest\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.uri\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"CATALOG_URI\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.warehouse\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"WAREHOUSE\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.token\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"TOKEN\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.s3.endpoint\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ENDPOINT\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.s3.path-style-access\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"true\"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.s3.access-key-id\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_ACCESS_KEY\"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.s3.secret-access-key\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"S3_SECRET_KEY\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"80\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.catalog.my_catalog.s3.remote-signing-enabled\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"false\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"config\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"spark.sql.defaultCatalog\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"my_catalog\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\\\"}]]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"getOrCreate\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"# SQL Operations\"}]]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"spark\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"sql\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"CREATE NAMESPACE IF NOT EXISTS analytics\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"spark\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"sql\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\\\"\\\"\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    CREATE TABLE IF NOT EXISTS analytics.users (\"}]]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"        user_id BIGINT,\"}]]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"        username STRING\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    )\"}]]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    USING iceberg\"}]]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\\\"\\\"\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"spark\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"sql\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\\\"\\\"\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    INSERT INTO analytics.users (user_id, username)\"}]]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"    VALUES (1, 'Alice'), (2, 'Bob'), (3, 'Charlie')\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\\\"\\\"\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"result_df \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" spark\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"sql\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"SELECT * FROM analytics.users\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"result_df\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"show\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L31\",null,{\"content\":\"$33\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"30:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"curl \\\\\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"  --request GET -sL \\\\\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"  --url 'https://\u003cyour-supabase-project\u003e.supabase.co/storage/v1/iceberg/v1/config?warehouse=\u003cbucket-name\u003e' \\\\\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"  --header 'Authorization: Bearer \u003cyour-service-key\u003e'\"}]]}]]}]]}]}],[\"$\",\"$L31\",null,{\"content\":\"curl \\\\\\n  --request GET -sL \\\\\\n  --url 'https://\u003cyour-supabase-project\u003e.supabase.co/storage/v1/iceberg/v1/config?warehouse=\u003cbucket-name\u003e' \\\\\\n  --header 'Authorization: Bearer \u003cyour-service-key\u003e'\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>