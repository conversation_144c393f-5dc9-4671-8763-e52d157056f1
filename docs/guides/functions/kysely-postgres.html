<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Type-Safe SQL with Kysely | Supabase Docs</title><meta name="description" content="Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="kysely-postgres.html"/><meta property="og:title" content="Type-Safe SQL with Kysely | Supabase Docs"/><meta property="og:description" content="Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database."/><meta property="og:url" content="https://supabase.com/docs/guides/functions/kysely-postgres"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=functions&amp;title=Type-Safe%20SQL%20with%20Kysely&amp;description=Combining%20Kysely%20with%20Deno%20Postgres%20gives%20you%20a%20convenient%20developer%20experience%20for%20interacting%20directly%20with%20your%20Postgres%20database."/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Type-Safe SQL with Kysely"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.389Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.389Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Type-Safe SQL with Kysely | Supabase Docs"/><meta name="twitter:description" content="Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../functions.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.62624 10.8978C1.22391 10.0142 1 9.03261 1 8C1 4.13401 4.13401 1 8 1C9.03686 1 10.0223 1.22575 10.9087 1.63122C11.2997 1.37784 11.766 1.23071 12.2665 1.23071C13.6473 1.23071 14.7665 2.35 14.7665 3.73071C14.7665 4.23073 14.6197 4.69646 14.3669 5.08716C14.7736 5.97467 15 6.96155 15 8C15 11.866 11.866 15 8 15C6.94896 15 5.95081 14.768 5.05508 14.3521C4.67477 14.5858 4.22715 14.7206 3.74805 14.7206C2.36733 14.7206 1.24805 13.6013 1.24805 12.2206C1.24805 11.7349 1.38656 11.2815 1.62624 10.8978ZM2 8C2 4.68629 4.68629 2 8 2C8.75898 2 9.48416 2.14069 10.1515 2.39715C9.90768 2.7831 9.76654 3.24042 9.76654 3.73071C9.76654 3.77457 9.76768 3.81815 9.76991 3.86145C9.22664 3.6288 8.62833 3.5 7.99994 3.5C5.51466 3.5 3.49994 5.51472 3.49994 8C3.49994 8.61006 3.62134 9.19177 3.8413 9.72228C3.81035 9.72115 3.77927 9.72058 3.74805 9.72058C3.24584 9.72058 2.77822 9.86866 2.38647 10.1235C2.13679 9.46389 2 8.74838 2 8ZM5.83493 13.5976C6.50608 13.8574 7.23593 14 8 14C11.3137 14 14 11.3137 14 8C14 7.23965 13.8588 6.51324 13.6015 5.84486C13.2152 6.08924 12.7574 6.23071 12.2665 6.23071C12.2232 6.23071 12.1802 6.22961 12.1374 6.22743C12.3707 6.77139 12.4999 7.3706 12.4999 8C12.4999 10.4853 10.4852 12.5 7.99994 12.5C7.37809 12.5 6.78569 12.3739 6.24695 12.1458C6.24768 12.1706 6.24805 12.1956 6.24805 12.2206C6.24805 12.7294 6.09603 13.2027 5.83493 13.5976ZM10.7665 3.73071C10.7665 2.90229 11.4381 2.23071 12.2665 2.23071C13.095 2.23071 13.7665 2.90229 13.7665 3.73071C13.7665 4.55914 13.095 5.23071 12.2665 5.23071C11.4381 5.23071 10.7665 4.55914 10.7665 3.73071ZM5.40407 10.3477C5.48532 10.4196 5.56185 10.4967 5.63315 10.5785C6.25623 11.1507 7.08729 11.5 7.99994 11.5C9.93294 11.5 11.4999 9.933 11.4999 8C11.4999 6.067 9.93294 4.5 7.99994 4.5C6.06695 4.5 4.49994 6.067 4.49994 8C4.49994 8.90336 4.84218 9.72678 5.40407 10.3477ZM3.74805 10.7206C4.11285 10.7206 4.44724 10.8508 4.70725 11.0673C4.77215 11.1369 4.83923 11.2045 4.90838 11.2699C5.12065 11.5287 5.24805 11.8598 5.24805 12.2206C5.24805 13.049 4.57647 13.7206 3.74805 13.7206C2.91962 13.7206 2.24805 13.049 2.24805 12.2206C2.24805 11.3921 2.91962 10.7206 3.74805 10.7206Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Edge Functions</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../functions.html">Overview</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Getting started</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quickstart-dashboard.html">Quickstart (Dashboard)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quickstart.html">Quickstart (CLI)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="development-environment.html">Development Environment</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Configuration</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="secrets.html">Environment Variables</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="dependencies.html">Managing Dependencies</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="function-configuration.html">Function Configuration</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Development</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="error-handling.html">Error Handling</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="routing.html">Routing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="deploy.html">Deploy to Production</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="debugging-tools.html">Local Debugging with DevTools</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="unit-test.html">Testing your Functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="logging.html">Logging</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="troubleshooting.html">Troubleshooting</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Platform</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="regional-invocation.html">Regional invocations</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="status-codes.html">Status codes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="limits.html">Limits</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="pricing.html">Pricing</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Integrations</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="auth.html">Supabase Auth</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="connect-to-postgres.html">Supabase Database (Postgres)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="storage-caching.html">Supabase Storage</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Advanced Features</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="background-tasks.html">Background Tasks</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="ephemeral-storage.html">File Storage</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="websockets.html">WebSockets</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="routing.html">Custom Routing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="wasm.html">Wasm Modules</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="ai-models.html">AI Models</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Examples</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/auth-send-email-hook-react-email-resend.html">Auth Send Email Hook</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="cors.html">CORS support for invoking from the browser</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="schedule-functions.html">Scheduling Functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/push-notifications.html">Sending Push Notifications</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/amazon-bedrock-image-generator.html">Generating AI images</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/og-image.html">Generating OG images </a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/semantic-search.html">Semantic AI Search</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/cloudflare-turnstile.html">CAPTCHA support with Cloudflare Turnstile</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/discord-bot.html">Building a Discord Bot</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/telegram-bot.html">Building a Telegram Bot</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/stripe-webhooks.html">Handling Stripe Webhooks </a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/rate-limiting.html">Rate-limiting with Redis</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/screenshots.html">Taking Screenshots with Puppeteer</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/slack-bot-mention.html">Slack Bot responding to mentions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/image-manipulation.html">Image Transformation &amp; Optimization</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Third-Party Tools</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="dart-edge.html">Dart Edge on Supabase</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/screenshots.html">Browserless.io</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ai/examples/huggingface-image-captioning.html">Hugging Face</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/sentry-monitoring.html">Monitoring with Sentry</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ai/examples/openai.html">OpenAI API</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/auth-send-email-hook-react-email-resend.html">React Email</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/send-emails.html">Sending Emails with Resend</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/upstash-redis.html">Upstash Redis</a></li></div><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="kysely-postgres.html">Type-Safe SQL with Kysely</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/elevenlabs-generate-speech-stream.html">Text To Speech with ElevenLabs</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="examples/elevenlabs-transcribe-speech.html">Speech Transcription with ElevenLabs</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Edge Functions</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../functions.html">Edge Functions</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Third-Party Tools</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="kysely-postgres.html">Type-Safe SQL with Kysely</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Type-Safe SQL with Kysely</p></h1><hr class="not-prose border-t-0 border-b my-8"/><div class="video-container"><iframe src="https://www.youtube-nocookie.com/embed/zd9a_Lk3jAc" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen=""></iframe></div>
<p>Supabase Edge Functions can <a href="connect-to-postgres.html">connect directly to your Postgres database</a> to execute SQL queries. <a href="https://github.com/kysely-org/kysely#kysely">Kysely</a> is a type-safe and autocompletion-friendly typescript SQL query builder.</p>
<p>Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database.</p>
<h2 id="code" class="group scroll-mt-24">Code<a href="#code" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Find the example on <a href="https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/kysely-postgres">GitHub</a></p>
<p>Get your database connection credentials from your <a href="../../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html">Supabase Dashboard</a> and store them in an <code>.env</code> file:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-parameter)">DB_HOSTNAME</span><span style="color:var(--code-token-keyword)">=</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">DB_PASSWORD</span><span style="color:var(--code-token-keyword)">=</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">DB_SSL_CERT</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">-----BEGIN CERTIFICATE-----</span></span><span class="block h-5"><span style="color:var(--code-token-string)">GET YOUR CERT FROM YOUR PROJECT DASHBOARD</span></span><span class="block h-5"><span style="color:var(--code-token-string)">-----END CERTIFICATE-----</span><span style="color:var(--code-token-punctuation)">&quot;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Create a <code>DenoPostgresDriver.ts</code> file to manage the connection to Postgres via <a href="https://deno-postgres.com/">deno-postgres</a>:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div><div class="w-full">57</div><div class="w-full">58</div><div class="w-full">59</div><div class="w-full">60</div><div class="w-full">61</div><div class="w-full">62</div><div class="w-full">63</div><div class="w-full">64</div><div class="w-full">65</div><div class="w-full">66</div><div class="w-full">67</div><div class="w-full">68</div><div class="w-full">69</div><div class="w-full">70</div><div class="w-full">71</div><div class="w-full">72</div><div class="w-full">73</div><div class="w-full">74</div><div class="w-full">75</div><div class="w-full">76</div><div class="w-full">77</div><div class="w-full">78</div><div class="w-full">79</div><div class="w-full">80</div><div class="w-full">81</div><div class="w-full">82</div><div class="w-full">83</div><div class="w-full">84</div><div class="w-full">85</div><div class="w-full">86</div><div class="w-full">87</div><div class="w-full">88</div><div class="w-full">89</div><div class="w-full">90</div><div class="w-full">91</div><div class="w-full">92</div><div class="w-full">93</div><div class="w-full">94</div><div class="w-full">95</div><div class="w-full">96</div><div class="w-full">97</div><div class="w-full">98</div><div class="w-full">99</div><div class="w-full">100</div><div class="w-full">101</div><div class="w-full">102</div><div class="w-full">103</div><div class="w-full">104</div><div class="w-full">105</div><div class="w-full">106</div><div class="w-full">107</div><div class="w-full">108</div><div class="w-full">109</div><div class="w-full">110</div><div class="w-full">111</div><div class="w-full">112</div><div class="w-full">113</div><div class="w-full">114</div><div class="w-full">115</div><div class="w-full">116</div><div class="w-full">117</div><div class="w-full">118</div><div class="w-full">119</div><div class="w-full">120</div><div class="w-full">121</div><div class="w-full">122</div><div class="w-full">123</div><div class="w-full">124</div><div class="w-full">125</div><div class="w-full">126</div><div class="w-full">127</div><div class="w-full">128</div><div class="w-full">129</div><div class="w-full">130</div><div class="w-full">131</div><div class="w-full">132</div><div class="w-full">133</div><div class="w-full">134</div><div class="w-full">135</div><div class="w-full">136</div><div class="w-full">137</div><div class="w-full">138</div><div class="w-full">139</div><div class="w-full">140</div><div class="w-full">141</div><div class="w-full">142</div><div class="w-full">143</div><div class="w-full">144</div><div class="w-full">145</div><div class="w-full">146</div><div class="w-full">147</div><div class="w-full">148</div><div class="w-full">149</div><div class="w-full">150</div><div class="w-full">151</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">Driver</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">PostgresCursorConstructor</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">QueryResult</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">TransactionSettings</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://esm.sh/kysely@0.23.4</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">freeze</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">isFunction</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://esm.sh/kysely@0.23.4/dist/esm/util/object-utils.js</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">extendStackTrace</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://esm.sh/kysely@0.23.4/dist/esm/util/stack-trace-utils.js</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">Pool</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">PoolClient</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://deno.land/x/postgres@v0.17.0/mod.ts</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresDialectConfig</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">pool</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Pool</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">|</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">Pool</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">cursor</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresCursorConstructor</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-function)">onCreateConnection</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">PRIVATE_RELEASE_METHOD</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">Symbol</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">class</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresDriver</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">implements</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">Driver</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">readonly</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresDialectConfig</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">readonly</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">#connections</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">WeakMap</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">PoolClient</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">#pool</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Pool</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">constructor</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">config</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresDialectConfig</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">freeze</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">...</span><span style="color:var(--code-token-parameter)">config</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">init</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#pool</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">isFunction</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">pool</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-keyword)">?</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">pool</span><span style="color:var(--code-foreground)">() </span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">pool</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">acquireConnection</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">client</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#pool</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">connect</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">let</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#connections</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">client</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnection</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">client</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-property)">cursor</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">cursor</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">??</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">null</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#connections</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">set</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">client</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">      </span><span style="color:var(--code-token-comment);font-style:italic">// The driver must take care of calling `onCreateConnection` when a new</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">      </span><span style="color:var(--code-token-comment);font-style:italic">// connection is created. The `pg` module doesn&#x27;t provide an async hook</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">      </span><span style="color:var(--code-token-comment);font-style:italic">// for the connection creation. We need to call the method explicitly.</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">?.</span><span style="color:var(--code-token-parameter)">onCreateConnection</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#config</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">onCreateConnection</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">beginTransaction</span><span style="color:var(--code-token-punctuation)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">settings</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">TransactionSettings</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">settings</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">isolationLevel</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">executeQuery</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-parameter)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">raw</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-string)">start transaction isolation level </span><span style="color:var(--code-token-punctuation)">${</span><span style="color:var(--code-token-parameter)">settings</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">isolationLevel</span><span style="color:var(--code-token-punctuation)">}`</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      )</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">else</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">executeQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">raw</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">begin</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">commitTransaction</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">executeQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">raw</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">commit</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">rollbackTransaction</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">DatabaseConnection</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">executeQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">raw</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">rollback</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">releaseConnection</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnection</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">connection</span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">PRIVATE_RELEASE_METHOD</span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">destroy</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#pool</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">pool</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#pool</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#pool</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">undefined</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">pool</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">end</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnectionOptions</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">cursor</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresCursorConstructor</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">|</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">null</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">class</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnection</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">implements</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">DatabaseConnection</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">#client</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PoolClient</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">#options</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnectionOptions</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">constructor</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">client</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PoolClient</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">options</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresConnectionOptions</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#client</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">client</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#options</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">options</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">executeQuery</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">O</span><span style="color:var(--code-token-punctuation)">&gt;(</span><span style="color:var(--code-token-parameter)">compiledQuery</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Promise</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">QueryResult</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">O</span><span style="color:var(--code-token-punctuation)">&gt;&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">try</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">result</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#client</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">queryObject</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">O</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">compiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">sql</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">[</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">        </span><span style="color:var(--code-token-keyword)">...</span><span style="color:var(--code-token-parameter)">compiledQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">parameters</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">command</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">===</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">INSERT</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">||</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">command</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">===</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">UPDATE</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">||</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">command</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">===</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">DELETE</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      ) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">numAffectedRows</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">BigInt</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">rowCount</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">||</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">0</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-property)">numUpdatedOrDeletedRows</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">numAffectedRows</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-parameter)">numAffectedRows</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-property)">rows</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">rows</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">??</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">[],</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">any</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-property)">rows</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">result</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">rows</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">??</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">[],</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">catch</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">throw</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">extendStackTrace</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Error</span><span style="color:var(--code-foreground)">())</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">*</span><span style="color:var(--code-token-function)">streamQuery</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">O</span><span style="color:var(--code-token-punctuation)">&gt;(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">_compiledQuery</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">CompiledQuery</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">chunkSize</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">number</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">AsyncIterableIterator</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">QueryResult</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">O</span><span style="color:var(--code-token-punctuation)">&gt;&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#options</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">cursor</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">throw</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Error</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">&#x27;cursor&#x27; is not present in your postgres dialect config. It&#x27;s required to make streaming work in postgres.</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      )</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-parameter)">Number</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">isInteger</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">chunkSize</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-keyword)">||</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">chunkSize</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">&lt;=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">0</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">throw</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Error</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">chunkSize must be a positive integer</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// stream not available</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">null</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">PRIVATE_RELEASE_METHOD</span><span style="color:var(--code-token-punctuation)">]()</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">void</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">this</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">#client</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">release</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Create an <code>index.ts</code> file to execute a query on incoming requests:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div><div class="w-full">57</div><div class="w-full">58</div><div class="w-full">59</div><div class="w-full">60</div><div class="w-full">61</div><div class="w-full">62</div><div class="w-full">63</div><div class="w-full">64</div><div class="w-full">65</div><div class="w-full">66</div><div class="w-full">67</div><div class="w-full">68</div><div class="w-full">69</div><div class="w-full">70</div><div class="w-full">71</div><div class="w-full">72</div><div class="w-full">73</div><div class="w-full">74</div><div class="w-full">75</div><div class="w-full">76</div><div class="w-full">77</div><div class="w-full">78</div><div class="w-full">79</div><div class="w-full">80</div><div class="w-full">81</div><div class="w-full">82</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">serve</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://deno.land/std@0.175.0/http/server.ts</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">Pool</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://deno.land/x/postgres@v0.17.0/mod.ts</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">Kysely</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">Generated</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">PostgresAdapter</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">PostgresIntrospector</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">PostgresQueryCompiler</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://esm.sh/kysely@0.23.4</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">PostgresDriver</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">./DenoPostgresDriver.ts</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-string)">Function &quot;kysely-postgres&quot; up and running!</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">AnimalTable</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">id</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Generated</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">bigint</span><span style="color:var(--code-token-punctuation)">&gt;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">animal</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">string</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">created_at</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Date</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">// Keys of this interface are table names.</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Database</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">animals</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">AnimalTable</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">// Create a database pool with one connection.</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">pool</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">Pool</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">tls</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-property)">caCertificates</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">DB_SSL_CERT</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">database</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">postgres</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">hostname</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">DB_HOSTNAME</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">user</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">postgres</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">port</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">5432</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-property)">password</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">DB_PASSWORD</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-constant)">1</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">// You&#x27;d create one of these when you start your app.</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">db</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">Kysely</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">Database</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-property)">dialect</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-function)">createAdapter</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresAdapter</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-function)">createDriver</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresDriver</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">pool</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-function)">createIntrospector</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">db</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">Kysely</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">unknown</span><span style="color:var(--code-token-punctuation)">&gt;)</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresIntrospector</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">db</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-function)">createQueryCompiler</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">PostgresQueryCompiler</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-function)">serve</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">_req</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">try</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Run a query</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">animals</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">db</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">selectFrom</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">animals</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">select</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">id</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">animal</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">created_at</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">execute</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Neat, it&#x27;s properly typed \o/</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">animals</span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">0</span><span style="color:var(--code-token-punctuation)">].</span><span style="color:var(--code-token-parameter)">created_at</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">getFullYear</span><span style="color:var(--code-foreground)">())</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Encode the result as pretty printed JSON</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">body</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">JSON</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">stringify</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-parameter)">animals</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">key</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">value</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-keyword)">typeof</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">value</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">===</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">bigint</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">?</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">value</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">toString</span><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">value</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-constant)">2</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Return the response with the correct content type header</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Response</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">body</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">status</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">200</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">headers</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Content-Type</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">application/json; charset=utf-8</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">catch</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">error</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Response</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-function)">String</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-token-punctuation)">?.</span><span style="color:var(--code-token-parameter)">message</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">??</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">err</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">status</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">500</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/functions/kysely-postgres.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8"])</script><script>self.__next_f.push([1,"804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970."])</script><script>self.__next_f.push([1,"js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz6"])</script><script>self.__next_f.push([1,"67\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L7"])</script><script>self.__next_f.push([1,"6FNhybEfz667\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7"])</script><script>self.__next_f.push([1,"L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3759\",\"static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561"])</script><script>self.__next_f.push([1,"c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3759\",\"static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz"])</script><script>self.__next_f.push([1,"667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d2"])</script><script>self.__next_f.push([1,"04d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785"])</script><script>self.__next_f.push([1,"b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42M"])</script><script>self.__next_f.push([1,"eC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42"])</script><script>self.__next_f.push([1,"MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"functions\",\"kysely-postgres\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"functions\",{\"children\":[[\"slug\",\"kysely-postgres\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"functions\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"kysely-postgres\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"IdzQdU0RSWsuFQGL7ZQnq\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"stati"])</script><script>self.__next_f.push([1,"c/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dp"])</script><script>self.__next_f.push([1,"l=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Type-Safe SQL with Kysely\"]}]]}],\"$undefined\",[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/functions/kysely-postgres.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"])</script><script>self.__next_f.push([1,"\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Type-Safe SQL with Kysely | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/functions/kysely-postgres\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Type-Safe SQL with Kysely | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/functions/kysely-postgres\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=functions\u0026title=Type-Safe%20SQL%20with%20Kysely\u0026description=Combining%20Kysely%20with%20Deno%20Postgres%20gives%20you%20a%20convenient%20developer%20experience%20for%20interacting%20directly%20with%20your%20Postgres%20database.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Type-Safe SQL with Kysely\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.389Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.389Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Type-Safe SQL with Kysely | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n29:[[\"$\",\"div\",null,{\"class\":\"video-container\",\"children\":[\"$\",\"iframe\",null,{\"src\":\"https://www.youtube-nocookie.com/embed/zd9a_Lk3jAc\",\"frameBorder\":\"1\",\"allow\":\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\"allowFullScreen\":true}]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Supabase Edge Functions can \",[\"$\",\"a\",null,{\"href\":\"/docs/guides/functions/connect-to-postgres\",\"children\":\"connect directly to your Postgres database\"}],\" to execute SQL queries. \",[\"$\",\"a\",null,{\"href\":\"https://github.com/kysel"])</script><script>self.__next_f.push([1,"y-org/kysely#kysely\",\"children\":\"Kysely\"}],\" is a type-safe and autocompletion-friendly typescript SQL query builder.\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Combining Kysely with Deno Postgres gives you a convenient developer experience for interacting directly with your Postgres database.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Code\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Find the example on \",[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/kysely-postgres\",\"children\":\"GitHub\"}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Get your database connection credentials from your \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/settings/database\",\"children\":\"Supabase Dashboard\"}],\" and store them in an \",[\"$\",\"code\",null,{\"children\":\".env\"}],\" file:\"]}],\"\\n\",\"$L2c\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Create a \",[\"$\",\"code\",null,{\"children\":\"DenoPostgresDriver.ts\"}],\" file to manage the connection to Postgres via \",[\"$\",\"a\",null,{\"href\":\"https://deno-postgres.com/\",\"children\":\"deno-postgres\"}],\":\"]}],\"\\n\",\"$L2d\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Create an \",[\"$\",\"code\",null,{\"children\":\"index.ts\"}],\" file to execute a query on incoming requests:\"]}],\"\\n\",\"$L2e\"]\n"])</script><script>self.__next_f.push([1,"2f:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n"])</script><script>self.__next_f.push([1,"2c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"DB_HOSTNAME\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"DB_PASSWORD\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"DB_SSL_CERT\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"-----BEGIN CERTIFICATE-----\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"GET YOUR CERT FROM YOUR PROJECT DASHBOARD\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"-----END CERTIFICATE-----\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}]]}]]}]}],[\"$\",\"$L2f\",null,{\"content\":\"DB_HOSTNAME=\\nDB_PASSWORD=\\nDB_SSL_CERT=\\\"-----BEGIN CERTIFICATE-----\\nGET YOUR CERT FROM YOUR PROJECT DASHBOARD\\n-----END CERTIFICATE-----\\\"\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"30:T10e1,"])</script><script>self.__next_f.push([1,"import {\n  CompiledQuery,\n  DatabaseConnection,\n  Driver,\n  PostgresCursorConstructor,\n  QueryResult,\n  TransactionSettings,\n} from 'https://esm.sh/kysely@0.23.4'\nimport { freeze, isFunction } from 'https://esm.sh/kysely@0.23.4/dist/esm/util/object-utils.js'\nimport { extendStackTrace } from 'https://esm.sh/kysely@0.23.4/dist/esm/util/stack-trace-utils.js'\nimport { Pool, PoolClient } from 'https://deno.land/x/postgres@v0.17.0/mod.ts'\n\nexport interface PostgresDialectConfig {\n  pool: Pool | (() =\u003e Promise\u003cPool\u003e)\n  cursor?: PostgresCursorConstructor\n  onCreateConnection?: (connection: DatabaseConnection) =\u003e Promise\u003cvoid\u003e\n}\n\nconst PRIVATE_RELEASE_METHOD = Symbol()\n\nexport class PostgresDriver implements Driver {\n  readonly #config: PostgresDialectConfig\n  readonly #connections = new WeakMap\u003cPoolClient, DatabaseConnection\u003e()\n  #pool?: Pool\n\n  constructor(config: PostgresDialectConfig) {\n    this.#config = freeze({ ...config })\n  }\n\n  async init(): Promise\u003cvoid\u003e {\n    this.#pool = isFunction(this.#config.pool) ? await this.#config.pool() : this.#config.pool\n  }\n\n  async acquireConnection(): Promise\u003cDatabaseConnection\u003e {\n    const client = await this.#pool!.connect()\n    let connection = this.#connections.get(client)\n\n    if (!connection) {\n      connection = new PostgresConnection(client, {\n        cursor: this.#config.cursor ?? null,\n      })\n      this.#connections.set(client, connection)\n\n      // The driver must take care of calling `onCreateConnection` when a new\n      // connection is created. The `pg` module doesn't provide an async hook\n      // for the connection creation. We need to call the method explicitly.\n      if (this.#config?.onCreateConnection) {\n        await this.#config.onCreateConnection(connection)\n      }\n    }\n\n    return connection\n  }\n\n  async beginTransaction(\n    connection: DatabaseConnection,\n    settings: TransactionSettings\n  ): Promise\u003cvoid\u003e {\n    if (settings.isolationLevel) {\n      await connection.executeQuery(\n        CompiledQuery.raw(`start transaction isolation level ${settings.isolationLevel}`)\n      )\n    } else {\n      await connection.executeQuery(CompiledQuery.raw('begin'))\n    }\n  }\n\n  async commitTransaction(connection: DatabaseConnection): Promise\u003cvoid\u003e {\n    await connection.executeQuery(CompiledQuery.raw('commit'))\n  }\n\n  async rollbackTransaction(connection: DatabaseConnection): Promise\u003cvoid\u003e {\n    await connection.executeQuery(CompiledQuery.raw('rollback'))\n  }\n\n  async releaseConnection(connection: PostgresConnection): Promise\u003cvoid\u003e {\n    connection[PRIVATE_RELEASE_METHOD]()\n  }\n\n  async destroy(): Promise\u003cvoid\u003e {\n    if (this.#pool) {\n      const pool = this.#pool\n      this.#pool = undefined\n      await pool.end()\n    }\n  }\n}\n\ninterface PostgresConnectionOptions {\n  cursor: PostgresCursorConstructor | null\n}\n\nclass PostgresConnection implements DatabaseConnection {\n  #client: PoolClient\n  #options: PostgresConnectionOptions\n\n  constructor(client: PoolClient, options: PostgresConnectionOptions) {\n    this.#client = client\n    this.#options = options\n  }\n\n  async executeQuery\u003cO\u003e(compiledQuery: CompiledQuery): Promise\u003cQueryResult\u003cO\u003e\u003e {\n    try {\n      const result = await this.#client.queryObject\u003cO\u003e(compiledQuery.sql, [\n        ...compiledQuery.parameters,\n      ])\n\n      if (\n        result.command === 'INSERT' ||\n        result.command === 'UPDATE' ||\n        result.command === 'DELETE'\n      ) {\n        const numAffectedRows = BigInt(result.rowCount || 0)\n\n        return {\n          numUpdatedOrDeletedRows: numAffectedRows,\n          numAffectedRows,\n          rows: result.rows ?? [],\n        } as any\n      }\n\n      return {\n        rows: result.rows ?? [],\n      }\n    } catch (err) {\n      throw extendStackTrace(err, new Error())\n    }\n  }\n\n  async *streamQuery\u003cO\u003e(\n    _compiledQuery: CompiledQuery,\n    chunkSize: number\n  ): AsyncIterableIterator\u003cQueryResult\u003cO\u003e\u003e {\n    if (!this.#options.cursor) {\n      throw new Error(\n        \"'cursor' is not present in your postgres dialect config. It's required to make streaming work in postgres.\"\n      )\n    }\n\n    if (!Number.isInteger(chunkSize) || chunkSize \u003c= 0) {\n      throw new Error('chunkSize must be a positive integer')\n    }\n\n    // stream not available\n    return null\n  }\n\n  [PRIVATE_RELEASE_METHOD](): void {\n    this.#client.release()\n  }\n}"])</script><script>self.__next_f.push([1,"2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}],[\"$\",\"div\",\"56\",{\"className\":\"w-full\",\"children\":57}],[\"$\",\"div\",\"57\",{\"className\":\"w-full\",\"children\":58}],[\"$\",\"div\",\"58\",{\"className\":\"w-full\",\"children\":59}],[\"$\",\"div\",\"59\",{\"className\":\"w-full\",\"children\":60}],[\"$\",\"div\",\"60\",{\"className\":\"w-full\",\"children\":61}],[\"$\",\"div\",\"61\",{\"className\":\"w-full\",\"children\":62}],[\"$\",\"div\",\"62\",{\"className\":\"w-full\",\"children\":63}],[\"$\",\"div\",\"63\",{\"className\":\"w-full\",\"children\":64}],[\"$\",\"div\",\"64\",{\"className\":\"w-full\",\"children\":65}],[\"$\",\"div\",\"65\",{\"className\":\"w-full\",\"children\":66}],[\"$\",\"div\",\"66\",{\"className\":\"w-full\",\"children\":67}],[\"$\",\"div\",\"67\",{\"className\":\"w-full\",\"children\":68}],[\"$\",\"div\",\"68\",{\"className\":\"w-full\",\"children\":69}],[\"$\",\"div\",\"69\",{\"className\":\"w-full\",\"children\":70}],[\"$\",\"div\",\"70\",{\"className\":\"w-full\",\"children\":71}],[\"$\",\"div\",\"71\",{\"className\":\"w-full\",\"children\":72}],[\"$\",\"div\",\"72\",{\"className\":\"w-full\",\"children\":73}],[\"$\",\"div\",\"73\",{\"className\":\"w-full\",\"children\":74}],[\"$\",\"div\",\"74\",{\"className\":\"w-full\",\"children\":75}],[\"$\",\"div\",\"75\",{\"className\":\"w-full\",\"children\":76}],[\"$\",\"div\",\"76\",{\"className\":\"w-full\",\"children\":77}],[\"$\",\"div\",\"77\",{\"className\":\"w-full\",\"children\":78}],[\"$\",\"div\",\"78\",{\"className\":\"w-full\",\"children\":79}],[\"$\",\"div\",\"79\",{\"className\":\"w-full\",\"children\":80}],[\"$\",\"div\",\"80\",{\"className\":\"w-full\",\"children\":81}],[\"$\",\"div\",\"81\",{\"className\":\"w-full\",\"children\":82}],[\"$\",\"div\",\"82\",{\"className\":\"w-full\",\"children\":83}],[\"$\",\"div\",\"83\",{\"className\":\"w-full\",\"children\":84}],[\"$\",\"div\",\"84\",{\"className\":\"w-full\",\"children\":85}],[\"$\",\"div\",\"85\",{\"className\":\"w-full\",\"children\":86}],[\"$\",\"div\",\"86\",{\"className\":\"w-full\",\"children\":87}],[\"$\",\"div\",\"87\",{\"className\":\"w-full\",\"children\":88}],[\"$\",\"div\",\"88\",{\"className\":\"w-full\",\"children\":89}],[\"$\",\"div\",\"89\",{\"className\":\"w-full\",\"children\":90}],[\"$\",\"div\",\"90\",{\"className\":\"w-full\",\"children\":91}],[\"$\",\"div\",\"91\",{\"className\":\"w-full\",\"children\":92}],[\"$\",\"div\",\"92\",{\"className\":\"w-full\",\"children\":93}],[\"$\",\"div\",\"93\",{\"className\":\"w-full\",\"children\":94}],[\"$\",\"div\",\"94\",{\"className\":\"w-full\",\"children\":95}],[\"$\",\"div\",\"95\",{\"className\":\"w-full\",\"children\":96}],[\"$\",\"div\",\"96\",{\"className\":\"w-full\",\"children\":97}],[\"$\",\"div\",\"97\",{\"className\":\"w-full\",\"children\":98}],[\"$\",\"div\",\"98\",{\"className\":\"w-full\",\"children\":99}],[\"$\",\"div\",\"99\",{\"className\":\"w-full\",\"children\":100}],[\"$\",\"div\",\"100\",{\"className\":\"w-full\",\"children\":101}],[\"$\",\"div\",\"101\",{\"className\":\"w-full\",\"children\":102}],[\"$\",\"div\",\"102\",{\"className\":\"w-full\",\"children\":103}],[\"$\",\"div\",\"103\",{\"className\":\"w-full\",\"children\":104}],[\"$\",\"div\",\"104\",{\"className\":\"w-full\",\"children\":105}],[\"$\",\"div\",\"105\",{\"className\":\"w-full\",\"children\":106}],[\"$\",\"div\",\"106\",{\"className\":\"w-full\",\"children\":107}],[\"$\",\"div\",\"107\",{\"className\":\"w-full\",\"children\":108}],[\"$\",\"div\",\"108\",{\"className\":\"w-full\",\"children\":109}],[\"$\",\"div\",\"109\",{\"className\":\"w-full\",\"children\":110}],[\"$\",\"div\",\"110\",{\"className\":\"w-full\",\"children\":111}],[\"$\",\"div\",\"111\",{\"className\":\"w-full\",\"children\":112}],[\"$\",\"div\",\"112\",{\"className\":\"w-full\",\"children\":113}],[\"$\",\"div\",\"113\",{\"className\":\"w-full\",\"children\":114}],[\"$\",\"div\",\"114\",{\"className\":\"w-full\",\"children\":115}],[\"$\",\"div\",\"115\",{\"className\":\"w-full\",\"children\":116}],[\"$\",\"div\",\"116\",{\"className\":\"w-full\",\"children\":117}],[\"$\",\"div\",\"117\",{\"className\":\"w-full\",\"children\":118}],[\"$\",\"div\",\"118\",{\"className\":\"w-full\",\"children\":119}],[\"$\",\"div\",\"119\",{\"className\":\"w-full\",\"children\":120}],[\"$\",\"div\",\"120\",{\"className\":\"w-full\",\"children\":121}],[\"$\",\"div\",\"121\",{\"className\":\"w-full\",\"children\":122}],[\"$\",\"div\",\"122\",{\"className\":\"w-full\",\"children\":123}],[\"$\",\"div\",\"123\",{\"className\":\"w-full\",\"children\":124}],[\"$\",\"div\",\"124\",{\"className\":\"w-full\",\"children\":125}],[\"$\",\"div\",\"125\",{\"className\":\"w-full\",\"children\":126}],[\"$\",\"div\",\"126\",{\"className\":\"w-full\",\"children\":127}],[\"$\",\"div\",\"127\",{\"className\":\"w-full\",\"children\":128}],[\"$\",\"div\",\"128\",{\"className\":\"w-full\",\"children\":129}],[\"$\",\"div\",\"129\",{\"className\":\"w-full\",\"children\":130}],[\"$\",\"div\",\"130\",{\"className\":\"w-full\",\"children\":131}],[\"$\",\"div\",\"131\",{\"className\":\"w-full\",\"children\":132}],[\"$\",\"div\",\"132\",{\"className\":\"w-full\",\"children\":133}],[\"$\",\"div\",\"133\",{\"className\":\"w-full\",\"children\":134}],[\"$\",\"div\",\"134\",{\"className\":\"w-full\",\"children\":135}],[\"$\",\"div\",\"135\",{\"className\":\"w-full\",\"children\":136}],[\"$\",\"div\",\"136\",{\"className\":\"w-full\",\"children\":137}],[\"$\",\"div\",\"137\",{\"className\":\"w-full\",\"children\":138}],[\"$\",\"div\",\"138\",{\"className\":\"w-full\",\"children\":139}],[\"$\",\"div\",\"139\",{\"className\":\"w-full\",\"children\":140}],[\"$\",\"div\",\"140\",{\"className\":\"w-full\",\"children\":141}],[\"$\",\"div\",\"141\",{\"className\":\"w-full\",\"children\":142}],[\"$\",\"div\",\"142\",{\"className\":\"w-full\",\"children\":143}],[\"$\",\"div\",\"143\",{\"className\":\"w-full\",\"children\":144}],[\"$\",\"div\",\"144\",{\"className\":\"w-full\",\"children\":145}],[\"$\",\"div\",\"145\",{\"className\":\"w-full\",\"children\":146}],[\"$\",\"div\",\"146\",{\"className\":\"w-full\",\"children\":147}],[\"$\",\"div\",\"147\",{\"className\":\"w-full\",\"children\":148}],[\"$\",\"div\",\"148\",{\"className\":\"w-full\",\"children\":149}],[\"$\",\"div\",\"149\",{\"className\":\"w-full\",\"children\":150}],[\"$\",\"div\",\"150\",{\"className\":\"w-full\",\"children\":151}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Driver\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PostgresCursorConstructor\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"QueryResult\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"TransactionSettings\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://esm.sh/kysely@0.23.4\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"freeze\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isFunction\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://esm.sh/kysely@0.23.4/dist/esm/util/object-utils.js\"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"extendStackTrace\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://esm.sh/kysely@0.23.4/dist/esm/util/stack-trace-utils.js\"}],[\"$\",\"span\",\"97\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Pool\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PoolClient\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://deno.land/x/postgres@v0.17.0/mod.ts\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresDialectConfig\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Pool\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"|\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Pool\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"cursor\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresCursorConstructor\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onCreateConnection\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"PRIVATE_RELEASE_METHOD\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Symbol\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"class\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresDriver\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"implements\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"Driver\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"readonly\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresDialectConfig\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"readonly\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#connections\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"WeakMap\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PoolClient\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Pool\"}]]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"constructor\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"config\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresDialectConfig\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"freeze\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"...\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"config\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"init\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"isFunction\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"() \"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"82\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"89\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"90\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pool\"}]]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"acquireConnection\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"client\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"connect\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#connections\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnection\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"cursor\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"cursor\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"??\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"null\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#connections\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"set\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// The driver must take care of calling `onCreateConnection` when a new\"}]]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// connection is created. The `pg` module doesn't provide an async hook\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// for the connection creation. We need to call the method explicitly.\"}]]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"?.\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"onCreateConnection\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#config\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onCreateConnection\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"beginTransaction\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"56\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"settings\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TransactionSettings\"}]]}],[\"$\",\"span\",\"57\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"58\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"settings\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isolationLevel\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"59\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"executeQuery\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"60\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"raw\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"start transaction isolation level \"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"$${\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"settings\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isolationLevel\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}`\"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"61\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      )\"}]]}],[\"$\",\"span\",\"62\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"else\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"63\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"executeQuery\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"raw\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"begin\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"64\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"65\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"66\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"67\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"commitTransaction\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"68\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"executeQuery\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"raw\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"commit\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"69\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"70\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"71\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"rollbackTransaction\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"72\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"executeQuery\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"raw\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"rollback\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"73\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"74\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"75\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"releaseConnection\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnection\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"76\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"connection\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"PRIVATE_RELEASE_METHOD\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"77\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"78\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"79\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"destroy\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"80\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"81\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}]]}],[\"$\",\"span\",\"82\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#pool\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"undefined\"}]]}],[\"$\",\"span\",\"83\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"end\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"84\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"85\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"86\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"87\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"88\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnectionOptions\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"89\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"cursor\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresCursorConstructor\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"|\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"null\"}]]}],[\"$\",\"span\",\"90\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"91\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"92\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"class\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnection\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"implements\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"DatabaseConnection\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"93\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#client\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PoolClient\"}]]}],[\"$\",\"span\",\"94\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#options\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnectionOptions\"}]]}],[\"$\",\"span\",\"95\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"96\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"constructor\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PoolClient\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"options\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresConnectionOptions\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"97\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#client\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}]]}],[\"$\",\"span\",\"98\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#options\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"options\"}]]}],[\"$\",\"span\",\"99\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"100\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"101\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"executeQuery\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"O\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"compiledQuery\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Promise\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"QueryResult\"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"O\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\u003e\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"102\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"try\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"103\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"result\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#client\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"queryObject\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"O\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"compiledQuery\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"sql\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}]]}],[\"$\",\"span\",\"104\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"...\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"compiledQuery\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"parameters\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"105\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"106\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"107\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],[\"$\",\"span\",\"108\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"command\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"===\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"INSERT\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"||\"}]]}],[\"$\",\"span\",\"109\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"command\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"===\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"UPDATE\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"||\"}]]}],[\"$\",\"span\",\"110\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"command\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"===\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"DELETE\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"111\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      ) \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"112\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"numAffectedRows\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"BigInt\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"rowCount\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"||\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"113\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"114\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"115\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"numUpdatedOrDeletedRows\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"numAffectedRows\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"116\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"numAffectedRows\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"117\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"rows\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"rows\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"??\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[],\"}]]}],[\"$\",\"span\",\"118\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"any\"}]]}],[\"$\",\"span\",\"119\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"120\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"121\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"122\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"rows\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"result\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"rows\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"??\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[],\"}]]}],[\"$\",\"span\",\"123\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"124\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"catch\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"125\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"throw\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"extendStackTrace\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Error\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"())\"}]]}],[\"$\",\"span\",\"126\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"127\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"128\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"129\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"*\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"streamQuery\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"O\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e(\"}]]}],[\"$\",\"span\",\"130\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"_compiledQuery\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"CompiledQuery\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"131\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"chunkSize\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"number\"}]]}],[\"$\",\"span\",\"132\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AsyncIterableIterator\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"QueryResult\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"O\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\u003e\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"133\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#options\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"cursor\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"134\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"throw\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Error\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"135\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"'cursor' is not present in your postgres dialect config. It's required to make streaming work in postgres.\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"136\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      )\"}]]}],[\"$\",\"span\",\"137\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"138\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"139\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Number\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"isInteger\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"chunkSize\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"||\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"chunkSize\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"\u003c=\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"140\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"throw\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Error\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"chunkSize must be a positive integer\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"141\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"142\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"143\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// stream not available\"}]]}],[\"$\",\"span\",\"144\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"null\"}]]}],[\"$\",\"span\",\"145\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"146\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"147\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"PRIVATE_RELEASE_METHOD\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]()\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"void\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"148\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"this\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"#client\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"release\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"149\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"150\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L2f\",null,{\"content\":\"$30\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"31:T7f4,import { serve } from 'https://deno.land/std@0.175.0/http/server.ts'\nimport { Pool } from 'https://deno.land/x/postgres@v0.17.0/mod.ts'\nimport {\n  Kysely,\n  Generated,\n  PostgresAdapter,\n  PostgresIntrospector,\n  PostgresQueryCompiler,\n} from 'https://esm.sh/kysely@0.23.4'\nimport { PostgresDriver } from './DenoPostgresDriver.ts'\n\nconsole.log(`Function \"kysely-postgres\" up and running!`)\n\ninterface AnimalTable {\n  id: Generated\u003cbigint\u003e\n  animal: string\n  created_at: Date\n}\n\n// Keys of this interface are table names.\ninterface Database {\n  animals: AnimalTable\n}\n\n// Create a database pool with one connection.\nconst pool = new Pool(\n  {\n    tls: { caCertificates: [Deno.env.get('DB_SSL_CERT')!] },\n    database: 'postgres',\n    hostname: Deno.env.get('DB_HOSTNAME'),\n    user: 'postgres',\n    port: 5432,\n    password: Deno.env.get('DB_PASSWORD'),\n  },\n  1\n)\n\n// You'd create one of these when you start your app.\nconst db = new Kysely\u003cDatabase\u003e({\n  dialect: {\n    createAdapter() {\n      return new PostgresAdapter()\n    },\n    createDriver() {\n      return new PostgresDriver({ pool })\n    },\n    createIntrospector(db: Kysely\u003cunknown\u003e) {\n      return new PostgresIntrospector(db)\n    },\n    createQueryCompiler() {\n      return new PostgresQueryCompiler()\n    },\n  },\n})\n\nserve(async (_req) =\u003e {\n  try {\n    // Run a query\n    const animals = await db.selectFrom('animals').select(['id', 'animal', 'created_at']).execute()\n\n    // Neat, it's properly typed \\o/\n    console.log(animals[0].created_at.getFullYear())\n\n    // Encode the result as pretty printed JSON\n    const body = JSON.stringify(\n      animals,\n      (key, value) =\u003e (typeof value === 'bigint' ? value.toString() : value),\n      2\n    )\n\n    // Return the response with the correct content type header\n    return new Response(body, {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json; charset=utf-8',\n      },\n    })\n  } catch (err) {\n    console.error(err)\n    return new Response(String(err?.message ?? err), { status: 500 })\n  }\n})"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}],[\"$\",\"div\",\"56\",{\"className\":\"w-full\",\"children\":57}],[\"$\",\"div\",\"57\",{\"className\":\"w-full\",\"children\":58}],[\"$\",\"div\",\"58\",{\"className\":\"w-full\",\"children\":59}],[\"$\",\"div\",\"59\",{\"className\":\"w-full\",\"children\":60}],[\"$\",\"div\",\"60\",{\"className\":\"w-full\",\"children\":61}],[\"$\",\"div\",\"61\",{\"className\":\"w-full\",\"children\":62}],[\"$\",\"div\",\"62\",{\"className\":\"w-full\",\"children\":63}],[\"$\",\"div\",\"63\",{\"className\":\"w-full\",\"children\":64}],[\"$\",\"div\",\"64\",{\"className\":\"w-full\",\"children\":65}],[\"$\",\"div\",\"65\",{\"className\":\"w-full\",\"children\":66}],[\"$\",\"div\",\"66\",{\"className\":\"w-full\",\"children\":67}],[\"$\",\"div\",\"67\",{\"className\":\"w-full\",\"children\":68}],[\"$\",\"div\",\"68\",{\"className\":\"w-full\",\"children\":69}],[\"$\",\"div\",\"69\",{\"className\":\"w-full\",\"children\":70}],[\"$\",\"div\",\"70\",{\"className\":\"w-full\",\"children\":71}],[\"$\",\"div\",\"71\",{\"className\":\"w-full\",\"children\":72}],[\"$\",\"div\",\"72\",{\"className\":\"w-full\",\"children\":73}],[\"$\",\"div\",\"73\",{\"className\":\"w-full\",\"children\":74}],[\"$\",\"div\",\"74\",{\"className\":\"w-full\",\"children\":75}],[\"$\",\"div\",\"75\",{\"className\":\"w-full\",\"children\":76}],[\"$\",\"div\",\"76\",{\"className\":\"w-full\",\"children\":77}],[\"$\",\"div\",\"77\",{\"className\":\"w-full\",\"children\":78}],[\"$\",\"div\",\"78\",{\"className\":\"w-full\",\"children\":79}],[\"$\",\"div\",\"79\",{\"className\":\"w-full\",\"children\":80}],[\"$\",\"div\",\"80\",{\"className\":\"w-full\",\"children\":81}],[\"$\",\"div\",\"81\",{\"className\":\"w-full\",\"children\":82}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"serve\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://deno.land/std@0.175.0/http/server.ts\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Pool\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://deno.land/x/postgres@v0.17.0/mod.ts\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Kysely\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Generated\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PostgresAdapter\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PostgresIntrospector\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PostgresQueryCompiler\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://esm.sh/kysely@0.23.4\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"PostgresDriver\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"./DenoPostgresDriver.ts\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Function \\\"kysely-postgres\\\" up and running!\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AnimalTable\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"id\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Generated\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"animal\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"string\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"created_at\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Date\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Keys of this interface are table names.\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Database\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"animals\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AnimalTable\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Create a database pool with one connection.\"}]]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Pool\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"tls\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"caCertificates\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"DB_SSL_CERT\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"database\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"postgres\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"hostname\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"DB_HOSTNAME\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"user\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"postgres\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"port\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"5432\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"password\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"DB_PASSWORD\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"1\"}]]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// You'd create one of these when you start your app.\"}]]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"db\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Kysely\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Database\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"dialect\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createAdapter\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresAdapter\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createDriver\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresDriver\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pool\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createIntrospector\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"db\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Kysely\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"unknown\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e)\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresIntrospector\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"db\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createQueryCompiler\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PostgresQueryCompiler\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"serve\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"_req\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"56\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"try\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"57\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Run a query\"}]]}],[\"$\",\"span\",\"58\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"animals\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"db\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"selectFrom\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"animals\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"select\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"id\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"animal\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"74\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"created_at\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"87\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",\"88\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"89\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"90\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"execute\"}],[\"$\",\"span\",\"97\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"59\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"60\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Neat, it's properly typed \\\\o/\"}]]}],[\"$\",\"span\",\"61\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"animals\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"].\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"created_at\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"getFullYear\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"())\"}]]}],[\"$\",\"span\",\"62\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"63\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Encode the result as pretty printed JSON\"}]]}],[\"$\",\"span\",\"64\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"body\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"JSON\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"stringify\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"65\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"animals\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"66\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"key\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"value\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"typeof\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"value\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"===\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"bigint\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"value\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"toString\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"69\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"value\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"67\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"2\"}]]}],[\"$\",\"span\",\"68\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"69\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"70\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Return the response with the correct content type header\"}]]}],[\"$\",\"span\",\"71\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Response\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"body\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"72\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"status\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"200\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"73\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"headers\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"74\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Content-Type\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"application/json; charset=utf-8\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"75\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"76\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"77\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"catch\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"78\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"error\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"79\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Response\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"String\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"?.\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"message\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"??\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"status\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"500\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"80\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"81\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L2f\",null,{\"content\":\"$31\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>