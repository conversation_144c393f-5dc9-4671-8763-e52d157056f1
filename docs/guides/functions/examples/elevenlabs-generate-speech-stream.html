<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../../supabase-dark.svg"/><link rel="preload" as="image" href="../../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Streaming Speech with ElevenLabs | Supabase Docs</title><meta name="description" content="Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="elevenlabs-generate-speech-stream.html"/><meta property="og:title" content="Streaming Speech with ElevenLabs | Supabase Docs"/><meta property="og:description" content="Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN."/><meta property="og:url" content="https://supabase.com/docs/guides/functions/examples/elevenlabs-generate-speech-stream"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=functions&amp;title=Streaming%20Speech%20with%20ElevenLabs&amp;description=undefined"/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Streaming Speech with ElevenLabs"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.124Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.124Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Streaming Speech with ElevenLabs | Supabase Docs"/><meta name="twitter:description" content="Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../../favicon/favicon.ico"/><link rel="icon" href="../../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../../functions.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.62624 10.8978C1.22391 10.0142 1 9.03261 1 8C1 4.13401 4.13401 1 8 1C9.03686 1 10.0223 1.22575 10.9087 1.63122C11.2997 1.37784 11.766 1.23071 12.2665 1.23071C13.6473 1.23071 14.7665 2.35 14.7665 3.73071C14.7665 4.23073 14.6197 4.69646 14.3669 5.08716C14.7736 5.97467 15 6.96155 15 8C15 11.866 11.866 15 8 15C6.94896 15 5.95081 14.768 5.05508 14.3521C4.67477 14.5858 4.22715 14.7206 3.74805 14.7206C2.36733 14.7206 1.24805 13.6013 1.24805 12.2206C1.24805 11.7349 1.38656 11.2815 1.62624 10.8978ZM2 8C2 4.68629 4.68629 2 8 2C8.75898 2 9.48416 2.14069 10.1515 2.39715C9.90768 2.7831 9.76654 3.24042 9.76654 3.73071C9.76654 3.77457 9.76768 3.81815 9.76991 3.86145C9.22664 3.6288 8.62833 3.5 7.99994 3.5C5.51466 3.5 3.49994 5.51472 3.49994 8C3.49994 8.61006 3.62134 9.19177 3.8413 9.72228C3.81035 9.72115 3.77927 9.72058 3.74805 9.72058C3.24584 9.72058 2.77822 9.86866 2.38647 10.1235C2.13679 9.46389 2 8.74838 2 8ZM5.83493 13.5976C6.50608 13.8574 7.23593 14 8 14C11.3137 14 14 11.3137 14 8C14 7.23965 13.8588 6.51324 13.6015 5.84486C13.2152 6.08924 12.7574 6.23071 12.2665 6.23071C12.2232 6.23071 12.1802 6.22961 12.1374 6.22743C12.3707 6.77139 12.4999 7.3706 12.4999 8C12.4999 10.4853 10.4852 12.5 7.99994 12.5C7.37809 12.5 6.78569 12.3739 6.24695 12.1458C6.24768 12.1706 6.24805 12.1956 6.24805 12.2206C6.24805 12.7294 6.09603 13.2027 5.83493 13.5976ZM10.7665 3.73071C10.7665 2.90229 11.4381 2.23071 12.2665 2.23071C13.095 2.23071 13.7665 2.90229 13.7665 3.73071C13.7665 4.55914 13.095 5.23071 12.2665 5.23071C11.4381 5.23071 10.7665 4.55914 10.7665 3.73071ZM5.40407 10.3477C5.48532 10.4196 5.56185 10.4967 5.63315 10.5785C6.25623 11.1507 7.08729 11.5 7.99994 11.5C9.93294 11.5 11.4999 9.933 11.4999 8C11.4999 6.067 9.93294 4.5 7.99994 4.5C6.06695 4.5 4.49994 6.067 4.49994 8C4.49994 8.90336 4.84218 9.72678 5.40407 10.3477ZM3.74805 10.7206C4.11285 10.7206 4.44724 10.8508 4.70725 11.0673C4.77215 11.1369 4.83923 11.2045 4.90838 11.2699C5.12065 11.5287 5.24805 11.8598 5.24805 12.2206C5.24805 13.049 4.57647 13.7206 3.74805 13.7206C2.91962 13.7206 2.24805 13.049 2.24805 12.2206C2.24805 11.3921 2.91962 10.7206 3.74805 10.7206Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Edge Functions</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../../functions.html">Overview</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Getting started</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstart-dashboard.html">Quickstart (Dashboard)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../quickstart.html">Quickstart (CLI)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../development-environment.html">Development Environment</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Configuration</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../secrets.html">Environment Variables</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../dependencies.html">Managing Dependencies</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../function-configuration.html">Function Configuration</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Development</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../error-handling.html">Error Handling</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../routing.html">Routing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../deploy.html">Deploy to Production</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../debugging-tools.html">Local Debugging with DevTools</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../unit-test.html">Testing your Functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../logging.html">Logging</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../troubleshooting.html">Troubleshooting</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Platform</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../regional-invocation.html">Regional invocations</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../status-codes.html">Status codes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../limits.html">Limits</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../pricing.html">Pricing</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Integrations</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../auth.html">Supabase Auth</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../connect-to-postgres.html">Supabase Database (Postgres)</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../storage-caching.html">Supabase Storage</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Advanced Features</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../background-tasks.html">Background Tasks</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ephemeral-storage.html">File Storage</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../websockets.html">WebSockets</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../routing.html">Custom Routing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../wasm.html">Wasm Modules</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../ai-models.html">AI Models</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Examples</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="auth-send-email-hook-react-email-resend.html">Auth Send Email Hook</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../cors.html">CORS support for invoking from the browser</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../schedule-functions.html">Scheduling Functions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="push-notifications.html">Sending Push Notifications</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="amazon-bedrock-image-generator.html">Generating AI images</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="og-image.html">Generating OG images </a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="semantic-search.html">Semantic AI Search</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="cloudflare-turnstile.html">CAPTCHA support with Cloudflare Turnstile</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="discord-bot.html">Building a Discord Bot</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="telegram-bot.html">Building a Telegram Bot</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="stripe-webhooks.html">Handling Stripe Webhooks </a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="rate-limiting.html">Rate-limiting with Redis</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="screenshots.html">Taking Screenshots with Puppeteer</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="slack-bot-mention.html">Slack Bot responding to mentions</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="image-manipulation.html">Image Transformation &amp; Optimization</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Third-Party Tools</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../dart-edge.html">Dart Edge on Supabase</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="screenshots.html">Browserless.io</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../../ai/examples/huggingface-image-captioning.html">Hugging Face</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="sentry-monitoring.html">Monitoring with Sentry</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../../ai/examples/openai.html">OpenAI API</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="auth-send-email-hook-react-email-resend.html">React Email</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="send-emails.html">Sending Emails with Resend</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="upstash-redis.html">Upstash Redis</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../kysely-postgres.html">Type-Safe SQL with Kysely</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="elevenlabs-generate-speech-stream.html">Text To Speech with ElevenLabs</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="elevenlabs-transcribe-speech.html">Speech Transcription with ElevenLabs</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Edge Functions</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../../functions.html">Edge Functions</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Third-Party Tools</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="elevenlabs-generate-speech-stream.html">Text To Speech with ElevenLabs</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Streaming Speech with ElevenLabs</p></h1><h2 class="mt-3 text-xl text-foreground-light"><p>Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN.</p></h2><hr class="not-prose border-t-0 border-b my-8"/><h2 id="introduction" class="group scroll-mt-24">Introduction<a href="#introduction" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>In this tutorial you will learn how to build an edge API to generate, stream, store, and cache speech using Supabase Edge Functions, Supabase Storage, and <a href="https://elevenlabs.io/text-to-speech">ElevenLabs text to speech API</a>.</p>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>Find the <a href="https://github.com/elevenlabs/elevenlabs-examples/tree/main/examples/text-to-speech/supabase/stream-and-cache-storage">example project on GitHub</a>.</p></div></div>
<h2 id="requirements" class="group scroll-mt-24">Requirements<a href="#requirements" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>An ElevenLabs account with an <a href="https://supabase.com/app/settings/api-keys">API key</a>.</li>
<li>A <a href="../../../../index.html">Supabase</a> account (you can sign up for a free account via <a href="https://database.new">database.new</a>).</li>
<li>The <a href="../../local-development.html">Supabase CLI</a> installed on your machine.</li>
<li>The <a href="https://docs.deno.com/runtime/getting_started/installation/">Deno runtime</a> installed on your machine and optionally <a href="https://docs.deno.com/runtime/getting_started/setup_your_environment">setup in your favourite IDE</a>.</li>
</ul>
<h2 id="setup" class="group scroll-mt-24">Setup<a href="#setup" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="create-a-supabase-project-locally" class="group scroll-mt-24">Create a Supabase project locally<a href="#create-a-supabase-project-locally" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>After installing the <a href="../../local-development.html">Supabase CLI</a>, run the following command to create a new Supabase project locally:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">init</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="configure-the-storage-bucket" class="group scroll-mt-24">Configure the storage bucket<a href="#configure-the-storage-bucket" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can configure the Supabase CLI to automatically generate a storage bucket by adding this configuration in the <code>config.toml</code> file:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-function)">storage</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-function)">buckets</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-function)">audio</span><span style="color:var(--code-token-punctuation)">]</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">public</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">false</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">file_size_limit</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">50MiB</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">allowed_mime_types</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">audio/mp3</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">]</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">objects_path</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">./audio</span><span style="color:var(--code-token-punctuation)">&quot;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>Upon running <code>supabase start</code> this will create a new storage bucket in your local Supabase project. Should you want to push this to your hosted Supabase project, you can run <code>supabase seed buckets --linked</code>.</p></div></div>
<h3 id="configure-background-tasks-for-supabase-edge-functions" class="group scroll-mt-24">Configure background tasks for Supabase Edge Functions<a href="#configure-background-tasks-for-supabase-edge-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To use background tasks in Supabase Edge Functions when developing locally, you need to add the following configuration in the <code>config.toml</code> file:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-function)">edge_runtime</span><span style="color:var(--code-token-punctuation)">]</span></span><span class="block h-5"><span style="color:var(--code-token-parameter)">policy</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">per_worker</span><span style="color:var(--code-token-punctuation)">&quot;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>When running with <code>per_worker</code> policy, Function won&#x27;t auto-reload on edits. You will need to manually restart it by running <code>supabase functions serve</code>.</p></div></div>
<h3 id="create-a-supabase-edge-function-for-speech-generation" class="group scroll-mt-24">Create a Supabase Edge Function for speech generation<a href="#create-a-supabase-edge-function-for-speech-generation" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Create a new Edge Function by running the following command:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">functions</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">text-to-speech</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>If you&#x27;re using VS Code or Cursor, select <code>y</code> when the CLI prompts &quot;Generate VS Code settings for Deno? [y/N]&quot;!</p>
<h3 id="set-up-the-environment-variables" class="group scroll-mt-24">Set up the environment variables<a href="#set-up-the-environment-variables" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Within the <code>supabase/functions</code> directory, create a new <code>.env</code> file and add the following variables:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span># Find / create an API key at https://elevenlabs.io/app/settings/api-keys</span></span><span class="block h-5"><span>ELEVENLABS_API_KEY=your_api_key</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="dependencies" class="group scroll-mt-24">Dependencies<a href="#dependencies" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The project uses a couple of dependencies:</p>
<ul>
<li>The <a href="https://supabase.com/docs/reference/javascript">@supabase/supabase-js</a> library to interact with the Supabase database.</li>
<li>The ElevenLabs <a href="https://supabase.com/docs/quickstart">JavaScript SDK</a> to interact with the text-to-speech API.</li>
<li>The open-source <a href="https://www.npmjs.com/package/object-hash">object-hash</a> to generate a hash from the request parameters.</li>
</ul>
<p>Since Supabase Edge Function uses the <a href="https://deno.land/">Deno runtime</a>, you don&#x27;t need to install the dependencies, rather you can <a href="https://docs.deno.com/examples/npm/">import</a> them via the <code>npm:</code> prefix.</p>
<h2 id="code-the-supabase-edge-function" class="group scroll-mt-24">Code the Supabase Edge Function<a href="#code-the-supabase-edge-function" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>In your newly created <code>supabase/functions/text-to-speech/index.ts</code> file, add the following code:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div><div class="w-full">19</div><div class="w-full">20</div><div class="w-full">21</div><div class="w-full">22</div><div class="w-full">23</div><div class="w-full">24</div><div class="w-full">25</div><div class="w-full">26</div><div class="w-full">27</div><div class="w-full">28</div><div class="w-full">29</div><div class="w-full">30</div><div class="w-full">31</div><div class="w-full">32</div><div class="w-full">33</div><div class="w-full">34</div><div class="w-full">35</div><div class="w-full">36</div><div class="w-full">37</div><div class="w-full">38</div><div class="w-full">39</div><div class="w-full">40</div><div class="w-full">41</div><div class="w-full">42</div><div class="w-full">43</div><div class="w-full">44</div><div class="w-full">45</div><div class="w-full">46</div><div class="w-full">47</div><div class="w-full">48</div><div class="w-full">49</div><div class="w-full">50</div><div class="w-full">51</div><div class="w-full">52</div><div class="w-full">53</div><div class="w-full">54</div><div class="w-full">55</div><div class="w-full">56</div><div class="w-full">57</div><div class="w-full">58</div><div class="w-full">59</div><div class="w-full">60</div><div class="w-full">61</div><div class="w-full">62</div><div class="w-full">63</div><div class="w-full">64</div><div class="w-full">65</div><div class="w-full">66</div><div class="w-full">67</div><div class="w-full">68</div><div class="w-full">69</div><div class="w-full">70</div><div class="w-full">71</div><div class="w-full">72</div><div class="w-full">73</div><div class="w-full">74</div><div class="w-full">75</div><div class="w-full">76</div><div class="w-full">77</div><div class="w-full">78</div><div class="w-full">79</div><div class="w-full">80</div><div class="w-full">81</div><div class="w-full">82</div><div class="w-full">83</div><div class="w-full">84</div><div class="w-full">85</div><div class="w-full">86</div><div class="w-full">87</div><div class="w-full">88</div><div class="w-full">89</div><div class="w-full">90</div><div class="w-full">91</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">// Setup type definitions for built-in Supabase Runtime APIs</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">jsr:@supabase/functions-js/edge-runtime.d.ts</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">createClient</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">npm:@supabase/supabase-js@2</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">ElevenLabsClient</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">npm:elevenlabs@1.52.0</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-constant)">*</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-parameter)">hash</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">npm:object-hash</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">supabase</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">createClient</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">SUPABASE_URL</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">SUPABASE_SERVICE_ROLE_KEY</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-keyword)">!</span></span><span class="block h-5"><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">client</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">ElevenLabsClient</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-property)">apiKey</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">env</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">ELEVENLABS_API_KEY</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">// Upload audio to Supabase Storage in a background task</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">function</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">uploadAudioToStorage</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">stream</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">ReadableStream</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">requestHash</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">string</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">data</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">error</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">supabase</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">storage</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">from</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">audio</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">upload</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">`${</span><span style="color:var(--code-token-parameter)">requestHash</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string)">.mp3</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">stream</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">contentType</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">audio/mp3</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Storage upload result</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">error</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-parameter)">Deno</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">serve</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">req</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">  </span><span style="color:var(--code-token-comment);font-style:italic">// To secure your function for production, you can for example validate the request origin,</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">  </span><span style="color:var(--code-token-comment);font-style:italic">// or append a user access token and validate it with Supabase Auth.</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Request origin</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">req</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">headers</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">host</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">url</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">URL</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">req</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">url</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">params</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">URLSearchParams</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">url</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">search</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">text</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">params</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">text</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">voiceId</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">params</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">voiceId</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">??</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">JBFqnCBsd6RMkjVDRZzb</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">requestHash</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">hash</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">MD5</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">text</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">voiceId</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Request hash</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">requestHash</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">  </span><span style="color:var(--code-token-comment);font-style:italic">// Check storage for existing audio file</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">data</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">supabase</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">storage</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">from</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">audio</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">createSignedUrl</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">`${</span><span style="color:var(--code-token-parameter)">requestHash</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string)">.mp3</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">60</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Audio file found in storage</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">storageRes</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">fetch</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">signedUrl</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">storageRes</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">ok</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">storageRes</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-parameter)">text</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Response</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">JSON</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">stringify</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">error</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Text parameter is required</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">status</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">400</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">headers</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Content-Type</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">application/json</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">try</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">ElevenLabs API call</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">response</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">client</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">textToSpeech</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">convertAsStream</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">voiceId</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-property)">output_format</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">mp3_44100_128</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-property)">model_id</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">eleven_multilingual_v2</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-parameter)">text</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">stream</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">ReadableStream</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-function)">start</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">controller</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">chunk</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">of</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">response</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">          </span><span style="color:var(--code-token-parameter)">controller</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">enqueue</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">chunk</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-parameter)">controller</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">close</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Branch stream to Supabase Storage</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">browserStream</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-constant)">storageStream</span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-parameter)">stream</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">tee</span><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Upload to Supabase Storage in the background</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">EdgeRuntime</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">waitUntil</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-function)">uploadAudioToStorage</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">storageStream</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">requestHash</span><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">    </span><span style="color:var(--code-token-comment);font-style:italic">// Return the streaming response immediately</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Response</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">browserStream</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">headers</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">        </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Content-Type</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">audio/mpeg</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">catch</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">error</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-parameter)">console</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">log</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">error</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">error</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">Response</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">JSON</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">stringify</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-property)">error</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-parameter)">error</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">message</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">status</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">500</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><span style="color:var(--code-token-property)">headers</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Content-Type</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">application/json</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="run-locally" class="group scroll-mt-24">Run locally<a href="#run-locally" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To run the function locally, run the following commands:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">start</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Once the local Supabase stack is up and running, run the following command to start the function and observe the logs:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">functions</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">serve</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="try-it-out" class="group scroll-mt-24">Try it out<a href="#try-it-out" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Navigate to <code>http://127.0.0.1:54321/functions/v1/text-to-speech?text=hello%20world</code> to hear the function in action.</p>
<p>Afterwards, navigate to <code>http://127.0.0.1:54323/project/default/storage/buckets/audio</code> to see the audio file in your local Supabase Storage bucket.</p>
<h2 id="deploy-to-supabase" class="group scroll-mt-24">Deploy to Supabase<a href="#deploy-to-supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>If you haven&#x27;t already, create a new Supabase account at <a href="https://database.new">database.new</a> and link the local project to your Supabase account:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">link</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>Once done, run the following command to deploy the function:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">functions</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">deploy</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="set-the-function-secrets" class="group scroll-mt-24">Set the function secrets<a href="#set-the-function-secrets" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Now that you have all your secrets set locally, you can run the following command to set the secrets in your Supabase project:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-function)">supabase</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">secrets</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">set</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">--env-file</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-string)">supabase/functions/.env</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="test-the-function" class="group scroll-mt-24">Test the function<a href="#test-the-function" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The function is designed in a way that it can be used directly as a source for an <code>&lt;audio&gt;</code> element.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">audio</span></span><span class="block h-5"><span style="color:var(--code-token-function)">  </span><span style="color:var(--code-token-property)">src</span><span style="color:var(--code-token-punctuation)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">https://${SUPABASE_PROJECT_REF}.supabase.co/functions/v1/text-to-speech?text=Hello%2C%20world!&amp;voiceId=JBFqnCBsd6RMkjVDRZzb</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-function)">  </span><span style="color:var(--code-token-property)">controls</span></span><span class="block h-5"><span style="color:var(--code-foreground)">/</span><span style="color:var(--code-token-punctuation)">&gt;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<p>You can find an example frontend implementation in the complete code example on <a href="https://github.com/elevenlabs/elevenlabs-examples/tree/main/examples/text-to-speech/supabase/stream-and-cache-storage/src/pages/Index.tsx">GitHub</a>.</p><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/functions/examples/elevenlabs-generate-speech-stream.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="relative pl-5"><button class="w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch video guide</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=16&amp;q=75 16w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=32&amp;q=75 32w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=48&amp;q=75 48w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=64&amp;q=75 64w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=96&amp;q=75 96w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=128&amp;q=75 128w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=256&amp;q=75 256w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=384&amp;q=75 384w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/docs/_next/image?url=https%3A%2F%2Fimg.youtube.com%2Fvi%2F4Roog4PAmZ8%2F0.jpg&amp;w=3840&amp;q=75"/></div></button></div><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8"])</script><script>self.__next_f.push([1,"804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970."])</script><script>self.__next_f.push([1,"js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz6"])</script><script>self.__next_f.push([1,"67\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L7"])</script><script>self.__next_f.push([1,"6FNhybEfz667\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7"])</script><script>self.__next_f.push([1,"L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3759\",\"static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561"])</script><script>self.__next_f.push([1,"c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3759\",\"static/chunks/app/guides/functions/layout-bc2223d466e1f792.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz"])</script><script>self.__next_f.push([1,"667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d2"])</script><script>self.__next_f.push([1,"04d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785"])</script><script>self.__next_f.push([1,"b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42M"])</script><script>self.__next_f.push([1,"eC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42"])</script><script>self.__next_f.push([1,"MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"functions\",\"examples\",\"elevenlabs-generate-speech-stream\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"functions\",{\"children\":[[\"slug\",\"examples/elevenlabs-generate-speech-stream\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"functions\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"examples/elevenlabs-generate-speech-stream\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"fFzTvKx-Qt7M1HhDfeFYy\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"stati"])</script><script>self.__next_f.push([1,"c/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dp"])</script><script>self.__next_f.push([1,"l=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Streaming Speech with ElevenLabs\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-3 text-xl text-foreground-light\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN.\"]}]]}],[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/functions/examples/elevenlabs-generate-speech-stream.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v"])</script><script>self.__next_f.push([1,"6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"4Roog4PAmZ8\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Streaming Speech with ElevenLabs | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/functions/examples/elevenlabs-generate-speech-stream\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Streaming Speech with ElevenLabs | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/functions/examples/elevenlabs-generate-speech-stream\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=functions\u0026title=Streaming%20Speech%20with%20ElevenLabs\u0026description=undefined\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Streaming Speech with ElevenLabs\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.124Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.124Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Streaming Speech with ElevenLabs | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Generate and stream speech through Supabase Edge Functions. Store speech in Supabase Storage and cache responses via built-in CDN.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2c:T4cd,M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6."])</script><script>self.__next_f.push([1,"41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z2f:T4cd,M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z31:T4cd,M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C"])</script><script>self.__next_f.push([1,"20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Introduction\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"In this tutorial you will learn how to build an edge API to generate, stream, store, and cache speech using Supabase Edge Functions, Supabase Storage, and \",[\"$\",\"a\",null,{\"href\":\"https://elevenlabs.io/text-to-speech\",\"children\":\"ElevenLabs text to speech API\"}],\".\"]}],\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground [\u0026\u003esvg]:text-background mb-2 [\u0026\u003esvg]:bg-foreground-muted bg-surface-200/25 border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 21 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$2c\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"Find the \",[\"$\",\"a\",null,{\"href\":\"https://github.com/elevenlabs/elevenlabs-examples/tree/main/examples/text-to-speech/supabase/stream-and-cache-storage\",\"children\":\"example project on GitHub\"}],\".\"]}]}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Requirements\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"An ElevenLabs account with an \",[\"$\",\"a\",null,{\"href\":\"/app/settings/api-keys\",\"children\":\"API key\"}],\".\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"A \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com\",\"children\":\"Supabase\"}],\" account (you can sign up for a free account via \",[\"$\",\"a\",null,{\"href\":\"https://database.new\",\"children\":\"database.new\"}],\").\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"The \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/docs/guides/local-development\",\"children\":\"Supabase CLI\"}],\" installed on your machine.\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"The \",[\"$\",\"a\",null,{\"href\":\"https://docs.deno.com/runtime/getting_started/installation/\",\"children\":\"Deno runtime\"}],\" installed on your machine and optionally \",[\"$\",\"a\",null,{\"href\":\"https://docs.deno.com/runtime/getting_started/setup_your_environment\",\"children\":\"setup in your favourite IDE\"}],\".\"]}],\"\\n\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Setup\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Create a Supabase project locally\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"After installing the \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/docs/guides/local-development\",\"children\":\"Supabase CLI\"}],\", run the following command to create a new Supabase project locally:\"]}],\"\\n\",\"$L2d\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Configure the storage bucket\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can configure the Supabase CLI to automatically generate a storage bucket by adding this configuration in the \",[\"$\",\"code\",null,{\"children\":\"config.toml\"}],\" file:\"]}],\"\\n\",\"$L2e\",\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground [\u0026\u003esvg]:text-background mb-2 [\u0026\u003esvg]:bg-foreground-muted bg-surface-200/25 border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 21 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$2f\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"Upon running \",[\"$\",\"code\",null,{\"children\":\"supabase start\"}],\" this will create a new storage bucket in your local Supabase project. Should you want to push this to your hosted Supabase project, you can run \",[\"$\",\"code\",null,{\"children\":\"supabase seed buckets --linked\"}],\".\"]}]}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Configure background tasks for Supabase Edge Functions\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"To use background tasks in Supabase Edge Functions when developing locally, you need to add the following configuration in the \",[\"$\",\"code\",null,{\"children\":\"config.toml\"}],\" file:\"]}],\"\\n\",\"$L30\",\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground [\u0026\u003esvg]:text-background mb-2 [\u0026\u003esvg]:bg-foreground-muted bg-surface-200/25 border border-default\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 21 20\",\"className\":\"w-6 h-6\",\"fill\":\"currentColor\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$31\"}]}],[\"$\",\"div\",null,{\"className\":\"text mt [\u0026_p]:mb-1.5 [\u0026_p]:mt-0 mt-0.5 [\u0026_p:last-child]:mb-0\",\"children\":[\"$\",\"p\",null,{\"children\":[\"When running with \",[\"$\",\"code\",null,{\"children\":\"per_worker\"}],\" policy, Function won't auto-reload on edits. You will need to manually restart it by running \",[\"$\",\"code\",null,{\"children\":\"supabase functions serve\"}],\".\"]}]}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Create a Supabase Edge Function for speech generation\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Create a new Edge Function by running the following command:\"}],\"\\n\",\"$L32\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"If you're using VS Code or Cursor, select \",[\"$\",\"code\",null,{\"children\":\"y\"}],\" when the CLI prompts \\\"Generate VS Code settings for Deno? [y/N]\\\"!\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Set up the environment variables\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Within the \",[\"$\",\"code\",null,{\"children\":\"supabase/functions\"}],\" directory, create a new \",[\"$\",\"code\",null,{\"children\":\".env\"}],\" file and add the following variables:\"]}],\"\\n\",\"$L33\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Dependencies\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"The project uses a couple of dependencies:\"}],\"\\n\",[\"$\",\"ul\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[\"The \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/docs/reference/javascript\",\"children\":\"@supabase/supabase-js\"}],\" library to interact with the Supabase database.\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"The ElevenLabs \",[\"$\",\"a\",null,{\"href\":\"/docs/quickstart\",\"children\":\"JavaScript SDK\"}],\" to interact with the text-to-speech API.\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[\"The open-source \",[\"$\",\"a\",null,{\"href\":\"https://www.npmjs.com/package/object-hash\",\"children\":\"object-hash\"}],\" to generate a hash from the request parameters.\"]}],\"\\n\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Since Supabase Edge Function uses the \",[\"$\",\"a\",null,{\"href\":\"https://deno.land/\",\"children\":\"Deno runtime\"}],\", you don't need to install the dependencies, rather you can \",[\"$\",\"a\",null,{\"href\":\"https://docs.deno.com/examples/npm/\",\"children\":\"import\"}],\" them via the \",[\"$\",\"code\",null,{\"children\":\"npm:\"}],\" prefix.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Code the Supabase Edge Function\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"In your newly created \",[\"$\",\"code\",null,{\"children\":\"supabase/functions/text-to-speech/index.ts\"}],\" file, add the following code:\"]}],\"\\n\",\"$L34\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Run locally\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"To run the function locally, run the following commands:\"}],\"\\n\",\"$L35\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Once the local Supabase stack is up and running, run the following command to start the function and observe the logs:\"}],\"\\n\",\"$L36\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Try it out\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Navigate to \",[\"$\",\"code\",null,{\"children\":\"http://127.0.0.1:54321/functions/v1/text-to-speech?text=hello%20world\"}],\" to hear the function in action.\"]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Afterwards, navigate to \",[\"$\",\"code\",null,{\"children\":\"http://127.0.0.1:54323/project/default/storage/buckets/audio\"}],\" to see the audio file in your local Supabase Storage bucket.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Deploy to Supabase\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"If you haven't already, create a new Supabase account at \",[\"$\",\"a\",null,{\"href\":\"https://database.new\",\"children\":\"database.new\"}],\" and link the local project to your Supabase account:\"]}],\"\\n\",\"$L37\",\"\\n\",[\"$\",\"p\",null,{\"children\":\"Once done, run the following command to deploy the function:\"}],\"\\n\",\"$L38\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Set the function secrets\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Now that you have all your secrets set locally, you can run the following command to set the secrets in your Supabase project:\"}],\"\\n\",\"$L39\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Test the function\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"The function is designed in a way that it can be used directly as a source for an \",[\"$\",\"code\",null,{\"children\":\"\u003caudio\u003e\"}],\" element.\"]}],\"\\n\",\"$L3a\",\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can find an example frontend implementation in the complete code example on \",[\"$\",\"a\",null,{\"href\":\"https://github.com/elevenlabs/elevenlabs-examples/tree/main/examples/text-to-speech/supabase/stream-and-cache-storage/src/pages/Index.tsx\",\"children\":\"GitHub\"}],\".\"]}]]\n"])</script><script>self.__next_f.push([1,"3b:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"6482\",\"static/chunks/app/guides/functions/%5B%5B...slug%5D%5D/page-20e4c6f9a899dccb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"s"])</script><script>self.__next_f.push([1,"tyle\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"init\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase init\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"storage\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"buckets\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"audio\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"public\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"false\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"file_size_limit\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"50MiB\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"allowed_mime_types\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"audio/mp3\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"objects_path\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"./audio\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"[storage.buckets.audio]\\npublic = false\\nfile_size_limit = \\\"50MiB\\\"\\nallowed_mime_types = [\\\"audio/mp3\\\"]\\nobjects_path = \\\"./audio\\\"\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"30:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"edge_runtime\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"per_worker\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"[edge_runtime]\\npolicy = \\\"per_worker\\\"\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n32:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 ov"])</script><script>self.__next_f.push([1,"erflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"functions\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"new\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"text-to-speech\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase functions new text-to-speech\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n33:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"# Find / create an API key at https://elevenlabs.io/app/settings/api-keys\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"$undefined\"},\"children\":\"ELEVENLABS_API_KEY=your_api_key\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"# Find / create an API key at https://elevenlabs.io/app/settings/api-keys\\nELEVENLABS_API_KEY=your_api_key\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n3c:Tb32,"])</script><script>self.__next_f.push([1,"// Setup type definitions for built-in Supabase Runtime APIs\nimport 'jsr:@supabase/functions-js/edge-runtime.d.ts'\nimport { createClient } from 'npm:@supabase/supabase-js@2'\nimport { ElevenLabsClient } from 'npm:elevenlabs@1.52.0'\nimport * as hash from 'npm:object-hash'\n\nconst supabase = createClient(\n  Deno.env.get('SUPABASE_URL')!,\n  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!\n)\n\nconst client = new ElevenLabsClient({\n  apiKey: Deno.env.get('ELEVENLABS_API_KEY'),\n})\n\n// Upload audio to Supabase Storage in a background task\nasync function uploadAudioToStorage(stream: ReadableStream, requestHash: string) {\n  const { data, error } = await supabase.storage\n    .from('audio')\n    .upload(`${requestHash}.mp3`, stream, {\n      contentType: 'audio/mp3',\n    })\n\n  console.log('Storage upload result', { data, error })\n}\n\nDeno.serve(async (req) =\u003e {\n  // To secure your function for production, you can for example validate the request origin,\n  // or append a user access token and validate it with Supabase Auth.\n  console.log('Request origin', req.headers.get('host'))\n  const url = new URL(req.url)\n  const params = new URLSearchParams(url.search)\n  const text = params.get('text')\n  const voiceId = params.get('voiceId') ?? 'JBFqnCBsd6RMkjVDRZzb'\n\n  const requestHash = hash.MD5({ text, voiceId })\n  console.log('Request hash', requestHash)\n\n  // Check storage for existing audio file\n  const { data } = await supabase.storage.from('audio').createSignedUrl(`${requestHash}.mp3`, 60)\n\n  if (data) {\n    console.log('Audio file found in storage', data)\n    const storageRes = await fetch(data.signedUrl)\n    if (storageRes.ok) return storageRes\n  }\n\n  if (!text) {\n    return new Response(JSON.stringify({ error: 'Text parameter is required' }), {\n      status: 400,\n      headers: { 'Content-Type': 'application/json' },\n    })\n  }\n\n  try {\n    console.log('ElevenLabs API call')\n    const response = await client.textToSpeech.convertAsStream(voiceId, {\n      output_format: 'mp3_44100_128',\n      model_id: 'eleven_multilingual_v2',\n      text,\n    })\n\n    const stream = new ReadableStream({\n      async start(controller) {\n        for await (const chunk of response) {\n          controller.enqueue(chunk)\n        }\n        controller.close()\n      },\n    })\n\n    // Branch stream to Supabase Storage\n    const [browserStream, storageStream] = stream.tee()\n\n    // Upload to Supabase Storage in the background\n    EdgeRuntime.waitUntil(uploadAudioToStorage(storageStream, requestHash))\n\n    // Return the streaming response immediately\n    return new Response(browserStream, {\n      headers: {\n        'Content-Type': 'audio/mpeg',\n      },\n    })\n  } catch (error) {\n    console.log('error', { error })\n    return new Response(JSON.stringify({ error: error.message }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' },\n    })\n  }\n})"])</script><script>self.__next_f.push([1,"34:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}],[\"$\",\"div\",\"19\",{\"className\":\"w-full\",\"children\":20}],[\"$\",\"div\",\"20\",{\"className\":\"w-full\",\"children\":21}],[\"$\",\"div\",\"21\",{\"className\":\"w-full\",\"children\":22}],[\"$\",\"div\",\"22\",{\"className\":\"w-full\",\"children\":23}],[\"$\",\"div\",\"23\",{\"className\":\"w-full\",\"children\":24}],[\"$\",\"div\",\"24\",{\"className\":\"w-full\",\"children\":25}],[\"$\",\"div\",\"25\",{\"className\":\"w-full\",\"children\":26}],[\"$\",\"div\",\"26\",{\"className\":\"w-full\",\"children\":27}],[\"$\",\"div\",\"27\",{\"className\":\"w-full\",\"children\":28}],[\"$\",\"div\",\"28\",{\"className\":\"w-full\",\"children\":29}],[\"$\",\"div\",\"29\",{\"className\":\"w-full\",\"children\":30}],[\"$\",\"div\",\"30\",{\"className\":\"w-full\",\"children\":31}],[\"$\",\"div\",\"31\",{\"className\":\"w-full\",\"children\":32}],[\"$\",\"div\",\"32\",{\"className\":\"w-full\",\"children\":33}],[\"$\",\"div\",\"33\",{\"className\":\"w-full\",\"children\":34}],[\"$\",\"div\",\"34\",{\"className\":\"w-full\",\"children\":35}],[\"$\",\"div\",\"35\",{\"className\":\"w-full\",\"children\":36}],[\"$\",\"div\",\"36\",{\"className\":\"w-full\",\"children\":37}],[\"$\",\"div\",\"37\",{\"className\":\"w-full\",\"children\":38}],[\"$\",\"div\",\"38\",{\"className\":\"w-full\",\"children\":39}],[\"$\",\"div\",\"39\",{\"className\":\"w-full\",\"children\":40}],[\"$\",\"div\",\"40\",{\"className\":\"w-full\",\"children\":41}],[\"$\",\"div\",\"41\",{\"className\":\"w-full\",\"children\":42}],[\"$\",\"div\",\"42\",{\"className\":\"w-full\",\"children\":43}],[\"$\",\"div\",\"43\",{\"className\":\"w-full\",\"children\":44}],[\"$\",\"div\",\"44\",{\"className\":\"w-full\",\"children\":45}],[\"$\",\"div\",\"45\",{\"className\":\"w-full\",\"children\":46}],[\"$\",\"div\",\"46\",{\"className\":\"w-full\",\"children\":47}],[\"$\",\"div\",\"47\",{\"className\":\"w-full\",\"children\":48}],[\"$\",\"div\",\"48\",{\"className\":\"w-full\",\"children\":49}],[\"$\",\"div\",\"49\",{\"className\":\"w-full\",\"children\":50}],[\"$\",\"div\",\"50\",{\"className\":\"w-full\",\"children\":51}],[\"$\",\"div\",\"51\",{\"className\":\"w-full\",\"children\":52}],[\"$\",\"div\",\"52\",{\"className\":\"w-full\",\"children\":53}],[\"$\",\"div\",\"53\",{\"className\":\"w-full\",\"children\":54}],[\"$\",\"div\",\"54\",{\"className\":\"w-full\",\"children\":55}],[\"$\",\"div\",\"55\",{\"className\":\"w-full\",\"children\":56}],[\"$\",\"div\",\"56\",{\"className\":\"w-full\",\"children\":57}],[\"$\",\"div\",\"57\",{\"className\":\"w-full\",\"children\":58}],[\"$\",\"div\",\"58\",{\"className\":\"w-full\",\"children\":59}],[\"$\",\"div\",\"59\",{\"className\":\"w-full\",\"children\":60}],[\"$\",\"div\",\"60\",{\"className\":\"w-full\",\"children\":61}],[\"$\",\"div\",\"61\",{\"className\":\"w-full\",\"children\":62}],[\"$\",\"div\",\"62\",{\"className\":\"w-full\",\"children\":63}],[\"$\",\"div\",\"63\",{\"className\":\"w-full\",\"children\":64}],[\"$\",\"div\",\"64\",{\"className\":\"w-full\",\"children\":65}],[\"$\",\"div\",\"65\",{\"className\":\"w-full\",\"children\":66}],[\"$\",\"div\",\"66\",{\"className\":\"w-full\",\"children\":67}],[\"$\",\"div\",\"67\",{\"className\":\"w-full\",\"children\":68}],[\"$\",\"div\",\"68\",{\"className\":\"w-full\",\"children\":69}],[\"$\",\"div\",\"69\",{\"className\":\"w-full\",\"children\":70}],[\"$\",\"div\",\"70\",{\"className\":\"w-full\",\"children\":71}],[\"$\",\"div\",\"71\",{\"className\":\"w-full\",\"children\":72}],[\"$\",\"div\",\"72\",{\"className\":\"w-full\",\"children\":73}],[\"$\",\"div\",\"73\",{\"className\":\"w-full\",\"children\":74}],[\"$\",\"div\",\"74\",{\"className\":\"w-full\",\"children\":75}],[\"$\",\"div\",\"75\",{\"className\":\"w-full\",\"children\":76}],[\"$\",\"div\",\"76\",{\"className\":\"w-full\",\"children\":77}],[\"$\",\"div\",\"77\",{\"className\":\"w-full\",\"children\":78}],[\"$\",\"div\",\"78\",{\"className\":\"w-full\",\"children\":79}],[\"$\",\"div\",\"79\",{\"className\":\"w-full\",\"children\":80}],[\"$\",\"div\",\"80\",{\"className\":\"w-full\",\"children\":81}],[\"$\",\"div\",\"81\",{\"className\":\"w-full\",\"children\":82}],[\"$\",\"div\",\"82\",{\"className\":\"w-full\",\"children\":83}],[\"$\",\"div\",\"83\",{\"className\":\"w-full\",\"children\":84}],[\"$\",\"div\",\"84\",{\"className\":\"w-full\",\"children\":85}],[\"$\",\"div\",\"85\",{\"className\":\"w-full\",\"children\":86}],[\"$\",\"div\",\"86\",{\"className\":\"w-full\",\"children\":87}],[\"$\",\"div\",\"87\",{\"className\":\"w-full\",\"children\":88}],[\"$\",\"div\",\"88\",{\"className\":\"w-full\",\"children\":89}],[\"$\",\"div\",\"89\",{\"className\":\"w-full\",\"children\":90}],[\"$\",\"div\",\"90\",{\"className\":\"w-full\",\"children\":91}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Setup type definitions for built-in Supabase Runtime APIs\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"jsr:@supabase/functions-js/edge-runtime.d.ts\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"createClient\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"npm:@supabase/supabase-js@2\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"ElevenLabsClient\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"npm:elevenlabs@1.52.0\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"*\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"hash\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"npm:object-hash\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createClient\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"SUPABASE_URL\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"SUPABASE_SERVICE_ROLE_KEY\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"client\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ElevenLabsClient\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"apiKey\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"env\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"ELEVENLABS_API_KEY\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Upload audio to Supabase Storage in a background task\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"function\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"uploadAudioToStorage\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"stream\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReadableStream\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"string\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"80\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"data\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"error\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"storage\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"from\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"audio\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"19\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"upload\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`${\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".mp3\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"stream\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"20\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"contentType\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"audio/mp3\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"21\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"22\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"23\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Storage upload result\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"error\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"24\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"25\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"26\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Deno\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"serve\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"req\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"27\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// To secure your function for production, you can for example validate the request origin,\"}]]}],[\"$\",\"span\",\"28\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// or append a user access token and validate it with Supabase Auth.\"}]]}],[\"$\",\"span\",\"29\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Request origin\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"req\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"headers\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"host\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"30\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"url\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"URL\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"req\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"url\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"31\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"params\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"URLSearchParams\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"url\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"search\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"32\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"text\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"params\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"33\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"voiceId\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"params\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"voiceId\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"??\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"JBFqnCBsd6RMkjVDRZzb\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"34\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"35\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"hash\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"MD5\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"text\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"voiceId\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"36\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Request hash\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"37\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"38\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Check storage for existing audio file\"}]]}],[\"$\",\"span\",\"39\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"data\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"storage\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"from\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"audio\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createSignedUrl\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`${\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"86\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"87\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\".mp3\"}],[\"$\",\"span\",\"91\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"92\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"93\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"60\"}],[\"$\",\"span\",\"96\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"40\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"41\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"42\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Audio file found in storage\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"43\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"storageRes\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"fetch\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"signedUrl\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"44\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"storageRes\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"ok\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"storageRes\"}]]}],[\"$\",\"span\",\"45\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"46\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"47\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"text\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"48\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Response\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"JSON\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"stringify\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"error\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Text parameter is required\"}],[\"$\",\"span\",\"75\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"76\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"80\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"49\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"status\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"400\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"50\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"headers\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Content-Type\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"application/json\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"51\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"52\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"53\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"54\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"try\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"55\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"ElevenLabs API call\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"56\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"response\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"client\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"textToSpeech\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"convertAsStream\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"voiceId\"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"57\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"output_format\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"mp3_44100_128\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"58\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"model_id\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"eleven_multilingual_v2\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"59\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"text\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"60\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"61\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"62\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"stream\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReadableStream\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"63\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"start\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"controller\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"64\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"chunk\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"of\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"response\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"65\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"          \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"controller\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"enqueue\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"chunk\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"66\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"67\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"controller\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"close\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"68\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"69\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"70\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"71\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Branch stream to Supabase Storage\"}]]}],[\"$\",\"span\",\"72\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"browserStream\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"storageStream\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"stream\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"tee\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"73\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"74\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Upload to Supabase Storage in the background\"}]]}],[\"$\",\"span\",\"75\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"EdgeRuntime\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"waitUntil\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"uploadAudioToStorage\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"storageStream\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"requestHash\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"76\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"77\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Return the streaming response immediately\"}]]}],[\"$\",\"span\",\"78\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Response\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"browserStream\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"79\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"headers\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"80\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Content-Type\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"audio/mpeg\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"81\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"82\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"83\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"catch\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"error\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"84\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"console\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"log\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"error\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"error\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"85\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Response\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"JSON\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"stringify\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"error\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"error\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"message\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"86\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"status\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"500\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"87\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"headers\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Content-Type\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"application/json\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"88\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"89\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"90\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"$3c\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"35:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"start\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase start\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n36:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"functions\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"serve\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase functions serve\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n37:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border "])</script><script>self.__next_f.push([1,"border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"link\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase link\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n38:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"functions\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"deploy\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase functions deploy\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n39:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\","])</script><script>self.__next_f.push([1,"null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabase\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"secrets\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"set\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"--env-file\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"supabase/functions/.env\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"supabase secrets set --env-file supabase/functions/.env\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3a:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"audio\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"src\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"=\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"https://${SUPABASE_PROJECT_REF}.supabase.co/functions/v1/text-to-speech?text=Hello%2C%20world!\u0026voiceId=JBFqnCBsd6RMkjVDRZzb\"}],[\"$\",\"span\",\"130\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"controls\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"/\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}]]}]]}]}],[\"$\",\"$L3b\",null,{\"content\":\"\u003caudio\\n  src=\\\"https://${SUPABASE_PROJECT_REF}.supabase.co/functions/v1/text-to-speech?text=Hello%2C%20world!\u0026voiceId=JBFqnCBsd6RMkjVDRZzb\\\"\\n  controls\\n/\u003e\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>