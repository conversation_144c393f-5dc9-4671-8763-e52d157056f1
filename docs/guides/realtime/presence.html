<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Presence | Supabase Docs</title><meta name="description" content="Share state between users with Realtime Presence."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="presence.html"/><meta property="og:title" content="Presence | Supabase Docs"/><meta property="og:description" content="Share state between users with Realtime Presence."/><meta property="og:url" content="https://supabase.com/docs/guides/realtime/presence"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=realtime&amp;title=Presence&amp;description=Share%20state%20between%20users%20with%20Realtime%20Presence."/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Presence"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Presence | Supabase Docs"/><meta name="twitter:description" content="Share state between users with Realtime Presence."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../realtime.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Realtime</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../realtime.html">Overview</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="concepts.html">Concepts</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Usage</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="broadcast.html">Broadcast</a></li></div><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="presence.html">Presence</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres-changes.html">Postgres Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="settings.html">Settings</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Security</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="authorization.html">Authorization</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Guides</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="subscribing-to-database-changes.html">Subscribing to Database Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-with-nextjs.html">Using Realtime with Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-user-presence.html">Using Realtime Presence with Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-listening-flutter.html">Listening to Postgres Changes with Flutter</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Deep dive</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quotas.html">Quotas</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="pricing.html">Pricing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="architecture.html">Architecture</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="protocol.html">Protocol</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="benchmarks.html">Benchmarks</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="error_codes.html">Operational Error Codes</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Realtime</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../realtime.html">Realtime</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Usage</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="presence.html">Presence</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Presence</p></h1><h2 class="mt-3 text-xl text-foreground-light"><p>Share state between users with Realtime Presence.</p></h2><hr class="not-prose border-t-0 border-b my-8"/><p>Let&#x27;s explore how to implement Realtime Presence to track state between multiple users.</p>
<h2 id="usage" class="group scroll-mt-24">Usage<a href="#usage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>You can use the Supabase client libraries to track Presence state between users.</p>
<h3 id="initialize-the-client" class="group scroll-mt-24">Initialize the client<a href="#initialize-the-client" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Go to your Supabase project&#x27;s <a href="../../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html">API Settings</a> and grab the <code>URL</code> and <code>anon</code> public API key.</p>
<div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right flex-wrap" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R2bi9tlqtqcrlb»-content-js" data-state="active" id="radix-«R2bi9tlqtqcrlb»-trigger-js" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>JavaScript</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2bi9tlqtqcrlb»-content-dart" data-state="inactive" id="radix-«R2bi9tlqtqcrlb»-trigger-dart" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Dart</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2bi9tlqtqcrlb»-content-swift" data-state="inactive" id="radix-«R2bi9tlqtqcrlb»-trigger-swift" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Swift</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2bi9tlqtqcrlb»-content-kotlin" data-state="inactive" id="radix-«R2bi9tlqtqcrlb»-trigger-kotlin" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Kotlin</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2bi9tlqtqcrlb»-content-python" data-state="inactive" id="radix-«R2bi9tlqtqcrlb»-trigger-python" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Python</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2bi9tlqtqcrlb»-trigger-js" id="radix-«R2bi9tlqtqcrlb»-content-js" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">createClient</button><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">@supabase/supabase-js</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">SUPABASE_URL</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">https://&lt;project&gt;.supabase.co</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">SUPABASE_KEY</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">&lt;your-anon-key&gt;</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">createClient</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">SUPABASE_URL</button><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">SUPABASE_KEY</button><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2bi9tlqtqcrlb»-trigger-dart" hidden="" id="radix-«R2bi9tlqtqcrlb»-content-dart" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2bi9tlqtqcrlb»-trigger-swift" hidden="" id="radix-«R2bi9tlqtqcrlb»-content-swift" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2bi9tlqtqcrlb»-trigger-kotlin" hidden="" id="radix-«R2bi9tlqtqcrlb»-content-kotlin" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2bi9tlqtqcrlb»-trigger-python" hidden="" id="radix-«R2bi9tlqtqcrlb»-content-python" tabindex="0" class="focus:outline-none transition-height "></div></div>
<h3 id="sync-and-track-state" class="group scroll-mt-24">Sync and track state<a href="#sync-and-track-state" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right flex-wrap" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R2fi9tlqtqcrlb»-content-js" data-state="active" id="radix-«R2fi9tlqtqcrlb»-trigger-js" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>JavaScript</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2fi9tlqtqcrlb»-content-dart" data-state="inactive" id="radix-«R2fi9tlqtqcrlb»-trigger-dart" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Dart</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2fi9tlqtqcrlb»-content-swift" data-state="inactive" id="radix-«R2fi9tlqtqcrlb»-trigger-swift" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Swift</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2fi9tlqtqcrlb»-content-kotlin" data-state="inactive" id="radix-«R2fi9tlqtqcrlb»-trigger-kotlin" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Kotlin</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2fi9tlqtqcrlb»-content-python" data-state="inactive" id="radix-«R2fi9tlqtqcrlb»-trigger-python" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Python</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2fi9tlqtqcrlb»-trigger-js" id="radix-«R2fi9tlqtqcrlb»-content-js" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><p>Listen to the <code>sync</code>, <code>join</code>, and <code>leave</code> events triggered whenever any client joins or leaves the channel or changes their slice of state:</p><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">room_01</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><button class="" data-state="closed">roomOne</button></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">presence</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">sync</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">newState</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">presenceState</button><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">sync</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">newState</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">presence</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">join</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">({</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">key</button><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">newPresences</button><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">})</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">join</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">key</button><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">newPresences</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">presence</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">leave</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">({</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">key</button><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">leftPresences</button><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">})</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">leave</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">key</button><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">leftPresences</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">subscribe</button><span style="color:var(--code-foreground)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2fi9tlqtqcrlb»-trigger-dart" hidden="" id="radix-«R2fi9tlqtqcrlb»-content-dart" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2fi9tlqtqcrlb»-trigger-swift" hidden="" id="radix-«R2fi9tlqtqcrlb»-content-swift" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2fi9tlqtqcrlb»-trigger-kotlin" hidden="" id="radix-«R2fi9tlqtqcrlb»-content-kotlin" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2fi9tlqtqcrlb»-trigger-python" hidden="" id="radix-«R2fi9tlqtqcrlb»-content-python" tabindex="0" class="focus:outline-none transition-height "></div></div>
<h3 id="sending-state" class="group scroll-mt-24">Sending state<a href="#sending-state" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can send state to all subscribers using <code>track()</code>:</p>
<div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right flex-wrap" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R2li9tlqtqcrlb»-content-js" data-state="active" id="radix-«R2li9tlqtqcrlb»-trigger-js" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>JavaScript</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2li9tlqtqcrlb»-content-dart" data-state="inactive" id="radix-«R2li9tlqtqcrlb»-trigger-dart" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Dart</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2li9tlqtqcrlb»-content-swift" data-state="inactive" id="radix-«R2li9tlqtqcrlb»-trigger-swift" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Swift</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2li9tlqtqcrlb»-content-kotlin" data-state="inactive" id="radix-«R2li9tlqtqcrlb»-trigger-kotlin" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Kotlin</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2li9tlqtqcrlb»-content-python" data-state="inactive" id="radix-«R2li9tlqtqcrlb»-trigger-python" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Python</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2li9tlqtqcrlb»-trigger-js" id="radix-«R2li9tlqtqcrlb»-content-js" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">room_01</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">userStatus</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><button class="" data-state="closed">user</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">user-1</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><button class="" data-state="closed">online_at</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">Date</button><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">toISOString</button><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">subscribe</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">status</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> (</span><button class="" data-state="closed">status</button><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">!==</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">SUBSCRIBED</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">presenceTrackStatus</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">track</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">userStatus</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">presenceTrackStatus</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2li9tlqtqcrlb»-trigger-dart" hidden="" id="radix-«R2li9tlqtqcrlb»-content-dart" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2li9tlqtqcrlb»-trigger-swift" hidden="" id="radix-«R2li9tlqtqcrlb»-content-swift" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2li9tlqtqcrlb»-trigger-kotlin" hidden="" id="radix-«R2li9tlqtqcrlb»-content-kotlin" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2li9tlqtqcrlb»-trigger-python" hidden="" id="radix-«R2li9tlqtqcrlb»-content-python" tabindex="0" class="focus:outline-none transition-height "></div></div>
<p>A client will receive state from any other client that is subscribed to the same topic (in this case <code>room_01</code>). It will also automatically trigger its own <code>sync</code> and <code>join</code> event handlers.</p>
<h3 id="stop-tracking" class="group scroll-mt-24">Stop tracking<a href="#stop-tracking" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can stop tracking presence using the <code>untrack()</code> method. This will trigger the <code>sync</code> and <code>leave</code> event handlers.</p>
<div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right flex-wrap" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R2ti9tlqtqcrlb»-content-js" data-state="active" id="radix-«R2ti9tlqtqcrlb»-trigger-js" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>JavaScript</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2ti9tlqtqcrlb»-content-dart" data-state="inactive" id="radix-«R2ti9tlqtqcrlb»-trigger-dart" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Dart</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2ti9tlqtqcrlb»-content-swift" data-state="inactive" id="radix-«R2ti9tlqtqcrlb»-trigger-swift" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Swift</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2ti9tlqtqcrlb»-content-kotlin" data-state="inactive" id="radix-«R2ti9tlqtqcrlb»-trigger-kotlin" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Kotlin</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R2ti9tlqtqcrlb»-content-python" data-state="inactive" id="radix-«R2ti9tlqtqcrlb»-trigger-python" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Python</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2ti9tlqtqcrlb»-trigger-js" id="radix-«R2ti9tlqtqcrlb»-content-js" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">untrackPresence</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">async</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">presenceUntrackStatus</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">roomOne</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">untrack</button><span style="color:var(--code-foreground)">()</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">presenceUntrackStatus</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span></span><span class="block h-5"></span><span class="block h-5"><button class="" data-state="closed">untrackPresence</button><span style="color:var(--code-foreground)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2ti9tlqtqcrlb»-trigger-dart" hidden="" id="radix-«R2ti9tlqtqcrlb»-content-dart" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2ti9tlqtqcrlb»-trigger-swift" hidden="" id="radix-«R2ti9tlqtqcrlb»-content-swift" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2ti9tlqtqcrlb»-trigger-kotlin" hidden="" id="radix-«R2ti9tlqtqcrlb»-content-kotlin" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R2ti9tlqtqcrlb»-trigger-python" hidden="" id="radix-«R2ti9tlqtqcrlb»-content-python" tabindex="0" class="focus:outline-none transition-height "></div></div>
<h2 id="presence-options" class="group scroll-mt-24">Presence options<a href="#presence-options" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>You can pass configuration options while initializing the Supabase Client.</p>
<h3 id="presence-key" class="group scroll-mt-24">Presence key<a href="#presence-key" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>By default, Presence will generate a unique <code>UUIDv1</code> key on the server to track a client channel&#x27;s state. If you prefer, you can provide a custom key when creating the channel. This key should be unique among clients.</p>
<div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right flex-wrap" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R37i9tlqtqcrlb»-content-js" data-state="active" id="radix-«R37i9tlqtqcrlb»-trigger-js" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>JavaScript</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R37i9tlqtqcrlb»-content-dart" data-state="inactive" id="radix-«R37i9tlqtqcrlb»-trigger-dart" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Dart</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R37i9tlqtqcrlb»-content-swift" data-state="inactive" id="radix-«R37i9tlqtqcrlb»-trigger-swift" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Swift</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R37i9tlqtqcrlb»-content-kotlin" data-state="inactive" id="radix-«R37i9tlqtqcrlb»-trigger-kotlin" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Kotlin</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R37i9tlqtqcrlb»-content-python" data-state="inactive" id="radix-«R37i9tlqtqcrlb»-trigger-python" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Python</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R37i9tlqtqcrlb»-trigger-js" id="radix-«R37i9tlqtqcrlb»-content-js" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">createClient</button><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-keyword)">from</span><span style="color:var(--code-token-string-expression)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">@supabase/supabase-js</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">createClient</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">SUPABASE_URL</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">SUPABASE_ANON_KEY</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">channelC</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">test</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><button class="" data-state="closed">config</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><button class="" data-state="closed">presence</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">      </span><button class="" data-state="closed">key</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">userId-123</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-variable)">  </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R37i9tlqtqcrlb»-trigger-dart" hidden="" id="radix-«R37i9tlqtqcrlb»-content-dart" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R37i9tlqtqcrlb»-trigger-swift" hidden="" id="radix-«R37i9tlqtqcrlb»-content-swift" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R37i9tlqtqcrlb»-trigger-kotlin" hidden="" id="radix-«R37i9tlqtqcrlb»-content-kotlin" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R37i9tlqtqcrlb»-trigger-python" hidden="" id="radix-«R37i9tlqtqcrlb»-content-python" tabindex="0" class="focus:outline-none transition-height "></div></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/presence.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\""])</script><script>self.__next_f.push([1,"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804"])</script><script>self.__next_f.push([1,"-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl"])</script><script>self.__next_f.push([1,"=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1"])</script><script>self.__next_f.push([1,"026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\""])</script><script>self.__next_f.push([1,",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GS"])</script><script>self.__next_f.push([1,"RZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v4"])</script><script>self.__next_f.push([1,"2MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\","])</script><script>self.__next_f.push([1,"\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,":HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"realtime\",\"presence\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"realtime\",{\"children\":[[\"slug\",\"presence\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"realtime\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"presence\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"j95PAFBgMzE1FcGF9N6qY\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Presence\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-3 text-xl text-foreground-light\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Share state between users with Realtime Presence.\"]}]]}],[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/presence.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex co"])</script><script>self.__next_f.push([1,"l-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Presence | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Share state between users with Realtime Presence.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/realtime/presence\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Presence | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Share state between users with Realtime Presence.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/realtime/presence\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=realtime\u0026title=Presence\u0026description=Share%20state%20between%20users%20with%20Realtime%20Presence.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Presence\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Presence | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Share state between users with Realtime Presence.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2c:I[39651,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-"])</script><script>self.__next_f.push([1,"d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Tabs\"]\n2d:I[39651,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7"])</script><script>self.__next_f.push([1,"L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TabPanel\"]\n"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"p\",null,{\"children\":\"Let's explore how to implement Realtime Presence to track state between multiple users.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Usage\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"You can use the Supabase client libraries to track Presence state between users.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Initialize the client\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Go to your Supabase project's \",[\"$\",\"a\",null,{\"href\":\"https://supabase.com/dashboard/project/_/settings/api\",\"children\":\"API Settings\"}],\" and grab the \",[\"$\",\"code\",null,{\"children\":\"URL\"}],\" and \",[\"$\",\"code\",null,{\"children\":\"anon\"}],\" public API key.\"]}],\"\\n\",[\"$\",\"$L2c\",null,{\"scrollable\":true,\"size\":\"small\",\"type\":\"underlined\",\"defaultActiveId\":\"js\",\"queryGroup\":\"language\",\"children\":[[\"$\",\"$L2d\",null,{\"id\":\"js\",\"label\":\"JavaScript\",\"children\":\"$L2e\"}],[\"$\",\"$L2d\",null,{\"id\":\"dart\",\"label\":\"Dart\",\"children\":\"$L2f\"}],[\"$\",\"$L2d\",null,{\"id\":\"swift\",\"label\":\"Swift\",\"children\":\"$L30\"}],[\"$\",\"$L2d\",null,{\"id\":\"kotlin\",\"label\":\"Kotlin\",\"children\":\"$L31\"}],[\"$\",\"$L2d\",null,{\"id\":\"python\",\"label\":\"Python\",\"children\":\"$L32\"}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Sync and track state\"}],\"\\n\",[\"$\",\"$L2c\",null,{\"scrollable\":true,\"size\":\"small\",\"type\":\"underlined\",\"defaultActiveId\":\"js\",\"queryGroup\":\"language\",\"children\":[[\"$\",\"$L2d\",null,{\"id\":\"js\",\"label\":\"JavaScript\",\"children\":[[\"$\",\"p\",null,{\"children\":[\"Listen to the \",[\"$\",\"code\",null,{\"children\":\"sync\"}],\", \",[\"$\",\"code\",null,{\"children\":\"join\"}],\", and \",[\"$\",\"code\",null,{\"children\":\"leave\"}],\" events triggered whenever any client joins or leaves the channel or changes their slice of state:\"]}],\"$L33\"]}],[\"$\",\"$L2d\",null,{\"id\":\"dart\",\"label\":\"Dart\",\"children\":\"$L34\"}],[\"$\",\"$L2d\",null,{\"id\":\"swift\",\"label\":\"Swift\",\"children\":[[\"$\",\"p\",null,{\"children\":[\"Listen to the presence change stream, emitting a new \",[\"$\",\"code\",null,{\"children\":\"PresenceAction\"}],\" whenever someone joins or leaves:\"]}],\"$L35\"]}],[\"$\",\"$L2d\",null,{\"id\":\"kotlin\",\"label\":\"Kotlin\",\"children\":[[\"$\",\"p\",null,{\"children\":[\"Listen to the presence change flow, emitting new a new \",[\"$\",\"code\",null,{\"children\":\"PresenceAction\"}],\" whenever someone joins or leaves:\"]}],\"$L36\"]}],[\"$\",\"$L2d\",null,{\"id\":\"python\",\"label\":\"Python\",\"children\":[[\"$\",\"p\",null,{\"children\":[\"Listen to the \",[\"$\",\"code\",null,{\"children\":\"sync\"}],\", \",[\"$\",\"code\",null,{\"children\":\"join\"}],\", and \",[\"$\",\"code\",null,{\"children\":\"leave\"}],\" events triggered whenever any client joins or leaves the channel or changes their slice of state:\"]}],\"$L37\"]}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Sending state\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can send state to all subscribers using \",[\"$\",\"code\",null,{\"children\":\"track()\"}],\":\"]}],\"\\n\",[\"$\",\"$L2c\",null,{\"scrollable\":true,\"size\":\"small\",\"type\":\"underlined\",\"defaultActiveId\":\"js\",\"queryGroup\":\"language\",\"children\":[[\"$\",\"$L2d\",null,{\"id\":\"js\",\"label\":\"JavaScript\",\"children\":\"$L38\"}],[\"$\",\"$L2d\",null,{\"id\":\"dart\",\"label\":\"Dart\",\"children\":\"$L39\"}],[\"$\",\"$L2d\",null,{\"id\":\"swift\",\"label\":\"Swift\",\"children\":\"$L3a\"}],[\"$\",\"$L2d\",null,{\"id\":\"kotlin\",\"label\":\"Kotlin\",\"children\":\"$L3b\"}],[\"$\",\"$L2d\",null,{\"id\":\"python\",\"label\":\"Python\",\"children\":\"$L3c\"}]]}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"A client will receive state from any other client that is subscribed to the same topic (in this case \",[\"$\",\"code\",null,{\"children\":\"room_01\"}],\"). It will also automatically trigger its own \",[\"$\",\"code\",null,{\"children\":\"sync\"}],\" and \",[\"$\",\"code\",null,{\"children\":\"join\"}],\" event handlers.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Stop tracking\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can stop tracking presence using the \",[\"$\",\"code\",null,{\"children\":\"untrack()\"}],\" method. This will trigger the \",[\"$\",\"code\",null,{\"children\":\"sync\"}],\" and \",[\"$\",\"code\",null,{\"children\":\"leave\"}],\" event handlers.\"]}],\"\\n\",[\"$\",\"$L2c\",null,{\"scrollable\":true,\"size\":\"small\",\"type\":\"underlined\",\"defaultActiveId\":\"js\",\"queryGroup\":\"language\",\"children\":[[\"$\",\"$L2d\",null,{\"id\":\"js\",\"label\":\"JavaScript\",\"children\":\"$L3d\"}],[\"$\",\"$L2d\",null,{\"id\":\"dart\",\"label\":\"Dart\",\"children\":\"$L3e\"}],[\"$\",\"$L2d\",null,{\"id\":\"swift\",\"label\":\"Swift\",\"children\":\"$L3f\"}],[\"$\",\"$L2d\",null,{\"id\":\"kotlin\",\"label\":\"Kotlin\",\"children\":\"$L40\"}],[\"$\",\"$L2d\",null,{\"id\":\"python\",\"label\":\"Python\",\"children\":\"$L41\"}]]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Presence options\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"You can pass configuration options while initializing the Supabase Client.\"}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Presence key\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"By default, Presence will generate a unique \",[\"$\",\"code\",null,{\"children\":\"UUIDv1\"}],\" key on the server to track a client channel's state. If you prefer, you can provide a custom key when creating the channel. This key should be unique among clients.\"]}],\"\\n\",[\"$\",\"$L2c\",null,{\"scrollable\":true,\"size\":\"small\",\"type\":\"underlined\",\"defaultActiveId\":\"js\",\"queryGroup\":\"language\",\"children\":[[\"$\",\"$L2d\",null,{\"id\":\"js\",\"label\":\"JavaScript\",\"children\":\"$L42\"}],[\"$\",\"$L2d\",null,{\"id\":\"dart\",\"label\":\"Dart\",\"children\":\"$L43\"}],[\"$\",\"$L2d\",null,{\"id\":\"swift\",\"label\":\"Swift\",\"children\":\"$L44\"}],[\"$\",\"$L2d\",null,{\"id\":\"kotlin\",\"label\":\"Kotlin\",\"children\":\"$L45\"}],[\"$\",\"$L2d\",null,{\"id\":\"python\",\"label\":\"Python\",\"children\":\"$L46\"}]]}]]\n"])</script><script>self.__next_f.push([1,"47:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AnnotatedSpan\"]\n48:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks"])</script><script>self.__next_f.push([1,"/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"9\",{\"token\":{\"content\":\"createClient\",\"offset\":9,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(alias) const createClient: \u003cDatabase = any, SchemaName extends string \u0026 keyof Database = \\\"public\\\" extends keyof Database ? \\\"public\\\" : string \u0026 keyof Database, Schema extends GenericSchema = Database[SchemaName] extends GenericSchema ? Database[SchemaName] : any\u003e(supabaseUrl: string, supabaseKey: string, options?: SupabaseClientOptions\u003cSchemaName\u003e | undefined) =\u003e SupabaseClient\u003cDatabase, SchemaName, Schema\u003e\\nimport createClient\",\"docs\":\"Creates a new Supabase Client.\",\"tags\":\"$undefined\",\"start\":9,\"length\":12,\"target\":\"createClient\",\"line\":0,\"character\":9}]}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@supabase/supabase-js\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"SUPABASE_URL\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const SUPABASE_URL: \\\"https://\u003cproject\u003e.supabase.co\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":60,\"length\":12,\"target\":\"SUPABASE_URL\",\"line\":2,\"character\":6}]}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://\u003cproject\u003e.supabase.co\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"SUPABASE_KEY\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const SUPABASE_KEY: \\\"\u003cyour-anon-key\u003e\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":113,\"length\":12,\"target\":\"SUPABASE_KEY\",\"line\":3,\"character\":6}]}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"\u003cyour-anon-key\u003e\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"supabase\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":153,\"length\":8,\"target\":\"supabase\",\"line\":5,\"character\":6}]}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"17\",{\"token\":{\"content\":\"createClient\",\"offset\":17,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(alias) createClient\u003cany, \\\"public\\\", any\u003e(supabaseUrl: string, supabaseKey: string, options?: SupabaseClientOptions\u003c\\\"public\\\"\u003e | undefined): SupabaseClient\u003cany, \\\"public\\\", any\u003e\\nimport createClient\",\"docs\":\"Creates a new Supabase Client.\",\"tags\":\"$undefined\",\"start\":164,\"length\":12,\"target\":\"createClient\",\"line\":5,\"character\":17}]}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L47\",\"30\",{\"token\":{\"content\":\"SUPABASE_URL\",\"offset\":30,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const SUPABASE_URL: \\\"https://\u003cproject\u003e.supabase.co\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":177,\"length\":12,\"target\":\"SUPABASE_URL\",\"line\":5,\"character\":30}]}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"44\",{\"token\":{\"content\":\"SUPABASE_KEY\",\"offset\":44,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const SUPABASE_KEY: \\\"\u003cyour-anon-key\u003e\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":191,\"length\":12,\"target\":\"SUPABASE_KEY\",\"line\":5,\"character\":44}]}],[\"$\",\"span\",\"56\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"import { createClient } from '@supabase/supabase-js'\\n\\nconst SUPABASE_URL = 'https://\u003cproject\u003e.supabase.co'\\nconst SUPABASE_KEY = '\u003cyour-anon-key\u003e'\\n\\nconst supabase = createClient(SUPABASE_URL, SUPABASE_KEY)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"void\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"main\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"() {\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Supabase\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"initialize\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    url\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'https://\u003cproject\u003e.supabase.co'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    anonKey\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'\u003cyour-anon-key\u003e'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"runApp\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"MyApp\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"())\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Supabase\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"instance\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"client\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"void main() {\\n  Supabase.initialize(\\n    url: 'https://\u003cproject\u003e.supabase.co',\\n    anonKey: '\u003cyour-anon-key\u003e',\\n  );\\n\\n  runApp(MyApp());\\n}\\n\\nfinal supabase = Supabase.instance.client;\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"30:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabaseURL \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"https://\u003cproject\u003e.supabase.co\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabaseKey \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\u003cyour-anon-key\u003e\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"SupabaseClient\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"supabaseURL\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" URL\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"string\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" supabaseURL\"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",\"68\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\", supabaseKey\"}],[\"$\",\"span\",\"81\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"82\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" supabaseKey\"}],[\"$\",\"span\",\"94\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" realtime \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"realtime\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"let supabaseURL = \\\"https://\u003cproject\u003e.supabase.co\\\"\\nlet supabaseKey = \\\"\u003cyour-anon-key\u003e\\\"\\nlet supabase = SupabaseClient(supabaseURL: URL(string: supabaseURL)!, supabaseKey: supabaseKey)\\n\\nlet realtime = supabase.realtime\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"31:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabaseUrl \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"https://\u003cproject\u003e.supabase.co\\\"\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabaseKey \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"\u003cyour-anon-key\u003e\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"createSupabaseClient\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(supabaseUrl, supabaseKey) {\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"install\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(Realtime)\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"val supabaseUrl = \\\"https://\u003cproject\u003e.supabase.co\\\"\\nval supabaseKey = \\\"\u003cyour-anon-key\u003e\\\"\\nval supabase = createSupabaseClient(supabaseUrl, supabaseKey) {\\n    install(Realtime)\\n}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"32:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" create_client\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_URL\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"https://\u003cproject\u003e.supabase.co\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_KEY\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\u003cyour-anon-key\u003e\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"supabase \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"create_client\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_URL\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUPABASE_KEY\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"from supabase import create_client\\n\\nSUPABASE_URL = 'https://\u003cproject\u003e.supabase.co'\\nSUPABASE_KEY = '\u003cyour-anon-key\u003e'\\n\\nsupabase = create_client(SUPABASE_URL, SUPABASE_KEY)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"49:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"4a:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"4b:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"33:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"roomOne\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":7,\"target\":\"roomOne\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"16\",{\"token\":{\"content\":\"supabase\",\"offset\":16,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":16,\"length\":8,\"target\":\"supabase\",\"line\":0,\"character\":16}]}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"25\",{\"token\":{\"content\":\"channel\",\"offset\":25,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":25,\"length\":7,\"target\":\"channel\",\"line\":0,\"character\":25}]}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"$L47\",\"0\",{\"token\":{\"content\":\"roomOne\",\"offset\":0,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":45,\"length\":7,\"target\":\"roomOne\",\"line\":2,\"character\":0}]}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on(type: `${REALTIME_LISTEN_TYPES.PRESENCE}`, filter: {\\n    event: `${REALTIME_PRESENCE_LISTEN_EVENTS.SYNC}`;\\n}, callback: () =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"Creates an event handler that listens to changes.\",\"tags\":\"$undefined\",\"start\":56,\"length\":2,\"target\":\"on\",\"line\":3,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"20\",{\"token\":{\"content\":\"event\",\"offset\":20,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: \\\"sync\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":73,\"length\":5,\"target\":\"event\",\"line\":3,\"character\":20}]}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"sync\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"10\",{\"token\":{\"content\":\"newState\",\"offset\":10,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const newState: RealtimePresenceState\u003c{}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":108,\"length\":8,\"target\":\"newState\",\"line\":4,\"character\":10}]}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"21\",{\"token\":{\"content\":\"roomOne\",\"offset\":21,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":119,\"length\":7,\"target\":\"roomOne\",\"line\":4,\"character\":21}]}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"29\",{\"token\":{\"content\":\"presenceState\",\"offset\":29,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.presenceState\u003c{}\u003e(): RealtimePresenceState\u003c{}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":127,\"length\":13,\"target\":\"presenceState\",\"line\":4,\"character\":29}]}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"$L47\",\"4\",{\"token\":{\"content\":\"console\",\"offset\":4,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$49\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":147,\"length\":7,\"target\":\"console\",\"line\":5,\"character\":4}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"12\",{\"token\":{\"content\":\"log\",\"offset\":12,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":155,\"length\":3,\"target\":\"log\",\"line\":5,\"character\":12}]}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"sync\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"24\",{\"token\":{\"content\":\"newState\",\"offset\":24,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const newState: RealtimePresenceState\u003c{}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":167,\"length\":8,\"target\":\"newState\",\"line\":5,\"character\":24}]}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on\u003c{\\n    [key: string]: any;\\n}\u003e(type: `${REALTIME_LISTEN_TYPES.PRESENCE}`, filter: {\\n    event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}`;\\n}, callback: (payload: RealtimePresenceJoinPayload\u003c...\u003e) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"Creates an event handler that listens to changes.\",\"tags\":\"$undefined\",\"start\":185,\"length\":2,\"target\":\"on\",\"line\":7,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"20\",{\"token\":{\"content\":\"event\",\"offset\":20,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: \\\"join\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":202,\"length\":5,\"target\":\"event\",\"line\":7,\"character\":20}]}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"join\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"({\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"40\",{\"token\":{\"content\":\"key\",\"offset\":40,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) key: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":222,\"length\":3,\"target\":\"key\",\"line\":7,\"character\":40}]}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"45\",{\"token\":{\"content\":\"newPresences\",\"offset\":45,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) newPresences: Presence\u003c{\\n    [key: string]: any;\\n}\u003e[]\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":227,\"length\":12,\"target\":\"newPresences\",\"line\":7,\"character\":45}]}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"})\"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"$L47\",\"4\",{\"token\":{\"content\":\"console\",\"offset\":4,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$4a\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":252,\"length\":7,\"target\":\"console\",\"line\":8,\"character\":4}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"12\",{\"token\":{\"content\":\"log\",\"offset\":12,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":260,\"length\":3,\"target\":\"log\",\"line\":8,\"character\":12}]}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"join\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"24\",{\"token\":{\"content\":\"key\",\"offset\":24,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) key: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":272,\"length\":3,\"target\":\"key\",\"line\":8,\"character\":24}]}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"29\",{\"token\":{\"content\":\"newPresences\",\"offset\":29,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) newPresences: Presence\u003c{\\n    [key: string]: any;\\n}\u003e[]\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":277,\"length\":12,\"target\":\"newPresences\",\"line\":8,\"character\":29}]}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on\u003c{\\n    [key: string]: any;\\n}\u003e(type: `${REALTIME_LISTEN_TYPES.PRESENCE}`, filter: {\\n    event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}`;\\n}, callback: (payload: RealtimePresenceLeavePayload\u003c...\u003e) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"Creates an event handler that listens to changes.\",\"tags\":\"$undefined\",\"start\":299,\"length\":2,\"target\":\"on\",\"line\":10,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"20\",{\"token\":{\"content\":\"event\",\"offset\":20,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: \\\"leave\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":316,\"length\":5,\"target\":\"event\",\"line\":10,\"character\":20}]}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"leave\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"({\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"41\",{\"token\":{\"content\":\"key\",\"offset\":41,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) key: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":337,\"length\":3,\"target\":\"key\",\"line\":10,\"character\":41}]}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"46\",{\"token\":{\"content\":\"leftPresences\",\"offset\":46,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) leftPresences: Presence\u003c{\\n    [key: string]: any;\\n}\u003e[]\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":342,\"length\":13,\"target\":\"leftPresences\",\"line\":10,\"character\":46}]}],[\"$\",\"span\",\"59\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"})\"}],[\"$\",\"span\",\"62\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"66\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"$L47\",\"4\",{\"token\":{\"content\":\"console\",\"offset\":4,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$4b\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":368,\"length\":7,\"target\":\"console\",\"line\":11,\"character\":4}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"12\",{\"token\":{\"content\":\"log\",\"offset\":12,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":376,\"length\":3,\"target\":\"log\",\"line\":11,\"character\":12}]}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"leave\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"25\",{\"token\":{\"content\":\"key\",\"offset\":25,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) key: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":389,\"length\":3,\"target\":\"key\",\"line\":11,\"character\":25}]}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"30\",{\"token\":{\"content\":\"leftPresences\",\"offset\":30,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) leftPresences: Presence\u003c{\\n    [key: string]: any;\\n}\u003e[]\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":394,\"length\":13,\"target\":\"leftPresences\",\"line\":11,\"character\":30}]}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"3\",{\"token\":{\"content\":\"subscribe\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.subscribe(callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) =\u003e void, timeout?: number): RealtimeChannel\",\"docs\":\"Subscribe registers your client with the server\",\"tags\":\"$undefined\",\"start\":417,\"length\":9,\"target\":\"subscribe\",\"line\":13,\"character\":3}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"const roomOne = supabase.channel('room_01')\\n\\nroomOne\\n  .on('presence', { event: 'sync' }, () =\u003e {\\n    const newState = roomOne.presenceState()\\n    console.log('sync', newState)\\n  })\\n  .on('presence', { event: 'join' }, ({ key, newPresences }) =\u003e {\\n    console.log('join', key, newPresences)\\n  })\\n  .on('presence', { event: 'leave' }, ({ key, leftPresences }) =\u003e {\\n    console.log('leave', key, leftPresences)\\n  })\\n  .subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"34:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Supabase\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"instance\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"client\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'room_01'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"roomOne\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onPresenceSync\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"((_) {\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" newState \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presenceState\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'sync: \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"$$\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"newState\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"})\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onPresenceJoin\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"((payload) {\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'join: \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"$$\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"payload\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"})\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onPresenceLeave\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"((payload) {\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'leave: \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"$$\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"payload\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"})\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"final supabase = Supabase.instance.client;\\n\\nfinal roomOne = supabase.channel('room_01');\\n\\nroomOne.onPresenceSync((_) {\\n  final newState = roomOne.presenceState();\\n  print('sync: $newState');\\n}).onPresenceJoin((payload) {\\n  print('join: $payload');\\n}).onPresenceLeave((payload) {\\n  print('leave: $payload');\\n}).subscribe();\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"35:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presenceStream \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presenceChange\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presence \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"in\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presenceStream \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presence.\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"join\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// You can also use presence.decodeJoins(as: MyType.self)\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presence.\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"leaves\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// You can also use presence.decodeLeaves(as: MyType.self)\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"let roomOne = await supabase.channel(\\\"room_01\\\")\\nlet presenceStream = await roomOne.presenceChange()\\n\\nawait roomOne.subscribe()\\n\\nfor await presence in presenceStream {\\n  print(presence.join) // You can also use presence.decodeJoins(as: MyType.self)\\n  print(presence.leaves) // You can also use presence.decodeLeaves(as: MyType.self)\\n}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"36:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"room_01\\\"\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presenceFlow: \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Flow\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"PresenceAction\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"\u003e \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presenceChangeFlow\"}],[\"$\",\"span\",\"67\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"presenceFlow\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    .\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"onEach\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" {\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"println\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(it.joins) \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"//You can also use it.decodeJoinsAs\u003cYourType\u003e()\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"println\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(it.leaves) \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"//You can also use it.decodeLeavesAs\u003cYourType\u003e()\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    }\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    .\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"launchIn\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(yourCoroutineScope) \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"//You can also use .collect { } here\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"roomOne.\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"val roomOne = supabase.channel(\\\"room_01\\\")\\nval presenceFlow: Flow\u003cPresenceAction\u003e = roomOne.presenceChangeFlow()\\npresenceFlow\\n    .onEach {\\n        println(it.joins) //You can also use it.decodeJoinsAs\u003cYourType\u003e()\\n        println(it.leaves) //You can also use it.decodeLeavesAs\u003cYourType\u003e()\\n    }\\n    .launchIn(yourCoroutineScope) //You can also use .collect { } here\\n\\nroomOne.subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"37:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"room_one \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"room_one\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"on_presence_sync\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"lambda\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" print\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"sync\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" room_one\"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presenceState\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()))\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"on_presence_join\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"lambda\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"key\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"curr_presences\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"joined_presences\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" print\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"73\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"join\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" key\"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" curr_presences\"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" joined_presences\"}],[\"$\",\"span\",\"117\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"on_presence_leave\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"lambda\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"key\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"curr_presences\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"left_presences\"}],[\"$\",\"span\",\"63\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" print\"}],[\"$\",\"span\",\"70\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"71\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"leave\"}],[\"$\",\"span\",\"77\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"78\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"79\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" key\"}],[\"$\",\"span\",\"83\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"84\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" curr_presences\"}],[\"$\",\"span\",\"99\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"100\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" left_presences\"}],[\"$\",\"span\",\"115\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"room_one = supabase.channel('room_01')\\n\\nroom_one\\n  .on_presence_sync(lambda: print('sync', room_one.presenceState()))\\n  .on_presence_join(lambda key, curr_presences, joined_presences: print('join', key, curr_presences, joined_presences))\\n  .on_presence_leave(lambda key, curr_presences, left_presences: print('leave', key, curr_presences, left_presences))\\n  .subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4c:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"38:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"roomOne\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":7,\"target\":\"roomOne\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"16\",{\"token\":{\"content\":\"supabase\",\"offset\":16,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":16,\"length\":8,\"target\":\"supabase\",\"line\":0,\"character\":16}]}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"25\",{\"token\":{\"content\":\"channel\",\"offset\":25,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":25,\"length\":7,\"target\":\"channel\",\"line\":0,\"character\":25}]}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"userStatus\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const userStatus: {\\n    user: string;\\n    online_at: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":51,\"length\":10,\"target\":\"userStatus\",\"line\":2,\"character\":6}]}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"$L47\",\"2\",{\"token\":{\"content\":\"user\",\"offset\":2,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) user: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":68,\"length\":4,\"target\":\"user\",\"line\":3,\"character\":2}]}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"user-1\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"$L47\",\"2\",{\"token\":{\"content\":\"online_at\",\"offset\":2,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) online_at: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":86,\"length\":9,\"target\":\"online_at\",\"line\":4,\"character\":2}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"17\",{\"token\":{\"content\":\"Date\",\"offset\":17,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"var Date: DateConstructor\\nnew () =\u003e Date (+3 overloads)\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":101,\"length\":4,\"target\":\"Date\",\"line\":4,\"character\":17}]}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"24\",{\"token\":{\"content\":\"toISOString\",\"offset\":24,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Date.toISOString(): string\",\"docs\":\"Returns a date as a string value in ISO format.\",\"tags\":\"$undefined\",\"start\":108,\"length\":11,\"target\":\"toISOString\",\"line\":4,\"character\":24}]}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"$L47\",\"0\",{\"token\":{\"content\":\"roomOne\",\"offset\":0,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":126,\"length\":7,\"target\":\"roomOne\",\"line\":7,\"character\":0}]}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"8\",{\"token\":{\"content\":\"subscribe\",\"offset\":8,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.subscribe(callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) =\u003e void, timeout?: number): RealtimeChannel\",\"docs\":\"Subscribe registers your client with the server\",\"tags\":\"$undefined\",\"start\":134,\"length\":9,\"target\":\"subscribe\",\"line\":7,\"character\":8}]}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L47\",\"25\",{\"token\":{\"content\":\"status\",\"offset\":25,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) status: REALTIME_SUBSCRIBE_STATES\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":151,\"length\":6,\"target\":\"status\",\"line\":7,\"character\":25}]}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"status\",\"offset\":6,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) status: REALTIME_SUBSCRIBE_STATES\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":170,\"length\":6,\"target\":\"status\",\"line\":8,\"character\":6}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!==\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"SUBSCRIBED\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"8\",{\"token\":{\"content\":\"presenceTrackStatus\",\"offset\":8,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const presenceTrackStatus: RealtimeChannelSendResponse\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":215,\"length\":19,\"target\":\"presenceTrackStatus\",\"line\":10,\"character\":8}]}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"36\",{\"token\":{\"content\":\"roomOne\",\"offset\":36,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":243,\"length\":7,\"target\":\"roomOne\",\"line\":10,\"character\":36}]}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"44\",{\"token\":{\"content\":\"track\",\"offset\":44,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.track(payload: {\\n    [key: string]: any;\\n}, opts?: {\\n    [key: string]: any;\\n}): Promise\u003cRealtimeChannelSendResponse\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":251,\"length\":5,\"target\":\"track\",\"line\":10,\"character\":44}]}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L47\",\"50\",{\"token\":{\"content\":\"userStatus\",\"offset\":50,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const userStatus: {\\n    user: string;\\n    online_at: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":257,\"length\":10,\"target\":\"userStatus\",\"line\":10,\"character\":50}]}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"$L47\",\"2\",{\"token\":{\"content\":\"console\",\"offset\":2,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$4c\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":271,\"length\":7,\"target\":\"console\",\"line\":11,\"character\":2}]}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"10\",{\"token\":{\"content\":\"log\",\"offset\":10,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":279,\"length\":3,\"target\":\"log\",\"line\":11,\"character\":10}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L47\",\"14\",{\"token\":{\"content\":\"presenceTrackStatus\",\"offset\":14,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const presenceTrackStatus: RealtimeChannelSendResponse\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":283,\"length\":19,\"target\":\"presenceTrackStatus\",\"line\":11,\"character\":14}]}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"const roomOne = supabase.channel('room_01')\\n\\nconst userStatus = {\\n  user: 'user-1',\\n  online_at: new Date().toISOString(),\\n}\\n\\nroomOne.subscribe(async (status) =\u003e {\\n  if (status !== 'SUBSCRIBED') { return }\\n\\n  const presenceTrackStatus = await roomOne.track(userStatus)\\n  console.log(presenceTrackStatus)\\n})\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"39:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'room_01'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" userStatus \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" {\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'user'\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'user-1'\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'online_at'\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"DateTime\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"now\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"toIso8601String\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"roomOne\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"((status\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" error) \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" {\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (status \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RealtimeSubscribeStatus\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"subscribed) \"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"58\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presenceTrackStatus \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"track\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(userStatus)\"}],[\"$\",\"span\",\"61\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(presenceTrackStatus)\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"})\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"final roomOne = supabase.channel('room_01');\\n\\nfinal userStatus = {\\n  'user': 'user-1',\\n  'online_at': DateTime.now().toIso8601String(),\\n};\\n\\nroomOne.subscribe((status, error) async {\\n  if (status != RealtimeSubscribeStatus.subscribed) return;\\n\\n  final presenceTrackStatus = await roomOne.track(userStatus);\\n  print(presenceTrackStatus);\\n});\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3a:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}],[\"$\",\"div\",\"18\",{\"className\":\"w-full\",\"children\":19}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Using a custom type\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" userStatus \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"UserStatus\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    user\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"user-1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    onlineAt\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" Date\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"timeIntervalSince1970\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"try\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"track\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"userStatus\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Or using a raw JSONObject.\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"track\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  [\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"user\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" .string\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"user-1\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"onlineAt\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" .\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"double\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Date\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\".\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"timeIntervalSince1970\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  ]\"}]]}],[\"$\",\"span\",\"18\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"let roomOne = await supabase.channel(\\\"room_01\\\")\\n\\n// Using a custom type\\nlet userStatus = UserStatus(\\n    user: \\\"user-1\\\",\\n    onlineAt: Date().timeIntervalSince1970\\n)\\n\\nawait roomOne.subscribe()\\n\\ntry await roomOne.track(userStatus)\\n\\n// Or using a raw JSONObject.\\nawait roomOne.track(\\n  [\\n    \\\"user\\\": .string(\\\"user-1\\\"),\\n    \\\"onlineAt\\\": .double(Date().timeIntervalSince1970)\\n  ]\\n)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3b:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"room_01\\\"\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" userStatus \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"UserStatus\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"( \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"//Your custom class\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    user \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"user-1\\\"\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    onlineAt \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" Clock.System.\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"now\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"().\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"toEpochMilliseconds\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"roomOne.\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(blockUntilSubscribed \"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"true\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"//You can also use the roomOne.status flow instead, but this parameter will block the coroutine until the status is joined.\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"roomOne.\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"track\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(userStatus)\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"val roomOne = supabase.channel(\\\"room_01\\\")\\n\\nval userStatus = UserStatus( //Your custom class\\n    user = \\\"user-1\\\",\\n    onlineAt = Clock.System.now().toEpochMilliseconds()\\n)\\n\\nroomOne.subscribe(blockUntilSubscribed = true) //You can also use the roomOne.status flow instead, but this parameter will block the coroutine until the status is joined.\\n\\nroomOne.track(userStatus)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"room_one \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"room_01\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"user_status \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"user\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"user-1\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"online_at\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" datetime\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"datetime\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"now\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"().\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"isoformat\"}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(),\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"def\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"on_subscribe\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"status\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"err\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"):\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" status \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" RealtimeSubscribeStates\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"SUBSCRIBED\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  room_one\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"track\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"user_status\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"room_one\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"subscribe\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"on_subscribe\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"room_one = supabase.channel('room_01')\\n\\nuser_status = {\\n  \\\"user\\\": 'user-1',\\n  \\\"online_at\\\": datetime.datetime.now().isoformat(),\\n}\\n\\ndef on_subscribe(status, err):\\n  if status != RealtimeSubscribeStates.SUBSCRIBED:\\n    return\\n\\n  room_one.track(user_status)\\n\\nroom_one.subscribe(on_subscribe)\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"4d:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"3d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"untrackPresence\",\"offset\":6,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const untrackPresence: () =\u003e Promise\u003cvoid\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":15,\"target\":\"untrackPresence\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"8\",{\"token\":{\"content\":\"presenceUntrackStatus\",\"offset\":8,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const presenceUntrackStatus: RealtimeChannelSendResponse\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":46,\"length\":21,\"target\":\"presenceUntrackStatus\",\"line\":1,\"character\":8}]}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"38\",{\"token\":{\"content\":\"roomOne\",\"offset\":38,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const roomOne: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":76,\"length\":7,\"target\":\"roomOne\",\"line\":1,\"character\":38}]}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"46\",{\"token\":{\"content\":\"untrack\",\"offset\":46,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.untrack(opts?: {\\n    [key: string]: any;\\n}): Promise\u003cRealtimeChannelSendResponse\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":84,\"length\":7,\"target\":\"untrack\",\"line\":1,\"character\":46}]}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"$L47\",\"2\",{\"token\":{\"content\":\"console\",\"offset\":2,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$4d\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":96,\"length\":7,\"target\":\"console\",\"line\":2,\"character\":2}]}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"10\",{\"token\":{\"content\":\"log\",\"offset\":10,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":104,\"length\":3,\"target\":\"log\",\"line\":2,\"character\":10}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L47\",\"14\",{\"token\":{\"content\":\"presenceUntrackStatus\",\"offset\":14,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const presenceUntrackStatus: RealtimeChannelSendResponse\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":108,\"length\":21,\"target\":\"presenceUntrackStatus\",\"line\":2,\"character\":14}]}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"$L47\",\"0\",{\"token\":{\"content\":\"untrackPresence\",\"offset\":0,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const untrackPresence: () =\u003e Promise\u003cvoid\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":134,\"length\":15,\"target\":\"untrackPresence\",\"line\":5,\"character\":0}]}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"const untrackPresence = async () =\u003e {\\n  const presenceUntrackStatus = await roomOne.untrack()\\n  console.log(presenceUntrackStatus)\\n}\\n\\nuntrackPresence()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'room_01'\"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrackPresence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"() \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"async\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" {\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" presenceUntrackStatus \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrack\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"print\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(presenceUntrackStatus)\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrackPresence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"final roomOne = supabase.channel('room_01');\\n\\nuntrackPresence() async {\\n  final presenceUntrackStatus = await roomOne.untrack();\\n  print(presenceUntrackStatus);\\n}\\n\\nuntrackPresence();\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"3f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" roomOne.\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrack\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"await roomOne.untrack()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"40:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"suspend\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"fun\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrackPresence\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"() {\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"\\troomOne.\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrack\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrackPresence\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"suspend fun untrackPresence() {\\n\\troomOne.untrack()\\n}\\n\\nuntrackPresence()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"41:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"room_one\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"untrack\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"room_one.untrack()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"42:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"9\",{\"token\":{\"content\":\"createClient\",\"offset\":9,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(alias) const createClient: \u003cDatabase = any, SchemaName extends string \u0026 keyof Database = \\\"public\\\" extends keyof Database ? \\\"public\\\" : string \u0026 keyof Database, Schema extends GenericSchema = Database[SchemaName] extends GenericSchema ? Database[SchemaName] : any\u003e(supabaseUrl: string, supabaseKey: string, options?: SupabaseClientOptions\u003cSchemaName\u003e | undefined) =\u003e SupabaseClient\u003cDatabase, SchemaName, Schema\u003e\\nimport createClient\",\"docs\":\"Creates a new Supabase Client.\",\"tags\":\"$undefined\",\"start\":9,\"length\":12,\"target\":\"createClient\",\"line\":0,\"character\":9}]}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"from\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\" \"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@supabase/supabase-js\"}],[\"$\",\"span\",\"51\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"supabase\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":59,\"length\":8,\"target\":\"supabase\",\"line\":1,\"character\":6}]}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"17\",{\"token\":{\"content\":\"createClient\",\"offset\":17,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(alias) createClient\u003cany, \\\"public\\\", any\u003e(supabaseUrl: string, supabaseKey: string, options?: SupabaseClientOptions\u003c\\\"public\\\"\u003e | undefined): SupabaseClient\u003cany, \\\"public\\\", any\u003e\\nimport createClient\",\"docs\":\"Creates a new Supabase Client.\",\"tags\":\"$undefined\",\"start\":70,\"length\":12,\"target\":\"createClient\",\"line\":1,\"character\":17}]}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"SUPABASE_URL\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"47\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"SUPABASE_ANON_KEY\"}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"65\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"channelC\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const channelC: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":127,\"length\":8,\"target\":\"channelC\",\"line\":3,\"character\":6}]}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L47\",\"17\",{\"token\":{\"content\":\"supabase\",\"offset\":17,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":138,\"length\":8,\"target\":\"supabase\",\"line\":3,\"character\":17}]}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L47\",\"26\",{\"token\":{\"content\":\"channel\",\"offset\":26,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":147,\"length\":7,\"target\":\"channel\",\"line\":3,\"character\":26}]}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"test\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"42\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"$L47\",\"2\",{\"token\":{\"content\":\"config\",\"offset\":2,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) config: {\\n    broadcast?: {\\n        self?: boolean;\\n        ack?: boolean;\\n    };\\n    presence?: {\\n        key?: string;\\n    };\\n    private?: boolean;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":167,\"length\":6,\"target\":\"config\",\"line\":4,\"character\":2}]}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"$L47\",\"4\",{\"token\":{\"content\":\"presence\",\"offset\":4,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) presence?: {\\n    key?: string;\\n} | undefined\",\"docs\":\"key option is used to track presence payload across clients\",\"tags\":\"$undefined\",\"start\":181,\"length\":8,\"target\":\"presence\",\"line\":5,\"character\":4}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"      \"}],[\"$\",\"$L47\",\"6\",{\"token\":{\"content\":\"key\",\"offset\":6,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) key?: string | undefined\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":199,\"length\":3,\"target\":\"key\",\"line\":6,\"character\":6}]}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"userId-123\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"import { createClient } from '@supabase/supabase-js'\\nconst supabase = createClient('SUPABASE_URL', 'SUPABASE_ANON_KEY')\\n\\nconst channelC = supabase.channel('test', {\\n  config: {\\n    presence: {\\n      key: 'userId-123',\\n    },\\n  },\\n})\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"43:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"final\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" channelC \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'test'\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  opts\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RealtimeChannelConfig\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(key\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"'userId-123'\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"54\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",\"1\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"final channelC = supabase.channel(\\n  'test',\\n  opts: const RealtimeChannelConfig(key: 'userId-123'),\\n);\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"44:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"let\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" channelC \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"test\"}],[\"$\",\"span\",\"43\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"44\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"46\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"$$0\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"key\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"userId-123\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"let channelC = await supabase.channel(\\\"test\\\") {\\n  $0.presence.key = \\\"userId-123\\\"\\n}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"45:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"val\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" channelC \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase.\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"test\\\"\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") {\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" {\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"        key \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"\\\"userId-123\\\"\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    }\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"}\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"val channelC = supabase.channel(\\\"test\\\") {\\n    presence {\\n        key = \\\"userId-123\\\"\\n    }\\n}\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"46:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"channel_c \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"channel\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"test\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"config\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"presence\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"      \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"key\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"userId-123\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"})\"}]]}]]}]]}]}],[\"$\",\"$L48\",null,{\"content\":\"channel_c = supabase.channel('test', {\\n  \\\"config\\\": {\\n    \\\"presence\\\": {\\n      \\\"key\\\": 'userId-123',\\n    },\\n  },\\n})\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>