<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Operational Error Codes | Supabase Docs</title><meta name="description" content="List of operational codes to help understand your deployment and usage."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="error_codes.html"/><meta property="og:title" content="Operational Error Codes | Supabase Docs"/><meta property="og:description" content="List of operational codes to help understand your deployment and usage."/><meta property="og:url" content="https://supabase.com/docs/guides/realtime/error_codes"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=realtime&amp;title=Operational%20Error%20Codes&amp;description=undefined"/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Operational Error Codes"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Operational Error Codes | Supabase Docs"/><meta name="twitter:description" content="List of operational codes to help understand your deployment and usage."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../realtime.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Realtime</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../realtime.html">Overview</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="concepts.html">Concepts</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Usage</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="broadcast.html">Broadcast</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="presence.html">Presence</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres-changes.html">Postgres Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="settings.html">Settings</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Security</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="authorization.html">Authorization</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Guides</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="subscribing-to-database-changes.html">Subscribing to Database Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-with-nextjs.html">Using Realtime with Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-user-presence.html">Using Realtime Presence with Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-listening-flutter.html">Listening to Postgres Changes with Flutter</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Deep dive</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quotas.html">Quotas</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="pricing.html">Pricing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="architecture.html">Architecture</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="protocol.html">Protocol</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="benchmarks.html">Benchmarks</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="error_codes.html">Operational Error Codes</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Realtime</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../realtime.html">Realtime</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Debugging</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="error_codes.html">Operational Error Codes</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Operational Error Codes</p></h1><h2 class="mt-3 text-xl text-foreground-light"><p>List of operational codes to help understand your deployment and usage.</p></h2><hr class="not-prose border-t-0 border-b my-8"/><div class="w-full overflow-auto"><table class="w-full caption-bottom text-sm"><thead class="[&amp;_tr]:border-b"><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><th class="h-12 px-4 text-left align-middle font-medium text-foreground-muted [&amp;:has([role=checkbox])]:pr-0">Error code</th><th class="h-12 px-4 text-left align-middle font-medium text-foreground-muted [&amp;:has([role=checkbox])]:pr-0">Description</th><th class="h-12 px-4 text-left align-middle font-medium text-foreground-muted [&amp;:has([role=checkbox])]:pr-0">Action</th></tr></thead><tbody class="[&amp;_tr:last-child]:border-0"><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ChannelRateLimitReached</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The number of channels you can create has reached its limit.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ClientJoinRateLimitReached</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The rate of joins per second from your clients has reached the channel limits.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ConnectionInitializing</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Database is initializing connection.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ConnectionRateLimitReached</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The number of connected clients has reached its limit.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">DatabaseConnectionIssue</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Database had connection issues and connection was not able to be established.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">DatabaseLackOfConnections</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Realtime was not able to connect to the tenant&#x27;s database due to not having enough available connections.</p><p>Learn more:</p><ul><li><a href="../database/connection-management.html">Connection management guide</a></li></ul></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Verify your database connection limits.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorAuthorizingWebsocket</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to authorize the WebSocket connection.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Verify user information on connect.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorConnectingToWebsocket</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to connect to the WebSocket server.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Verify user information on connect.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorExecutingTransaction</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error executing a database transaction in tenant database.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorOnRpcCall</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when calling another realtime node.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorStartingPostgresCDC</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when starting the Postgres CDC extension which is used for Postgres Changes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ErrorStartingPostgresCDCStream</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when starting the Postgres CDC stream which is used for Postgres Changes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">IncreaseConnectionPool</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The number of connections you have set for Realtime are not enough to handle your current use case.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">InitializingProjectConnection</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Connection against Tenant database is still starting.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">InvalidJWTExpiration</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>JWT exp claim value it&#x27;s incorrect.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">JanitorFailedToDeleteOldMessages</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Scheduled task for realtime.message cleanup was unable to run.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">JwtSignatureError</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>JWT signature was not able to be validated.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">MalformedJWT</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Token received does not comply with the JWT format.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">MigrationCheckFailed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Check to see if we require to run migrations fails.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">MigrationsFailedToRun</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when running the migrations against the Tenant database that are required by Realtime.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">PartitionCreationFailed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when creating partitions for realtime.messages.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">PoolingReplicationError</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when pooling the replication slot.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">PoolingReplicationPreparationError</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when preparing the replication slot.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">RealtimeDisabledForConfiguration</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The configuration provided to Realtime on connect will not be able to provide you any Postgres Changes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Verify your configuration on channel startup as you might not have your tables properly registered.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">RealtimeDisabledForTenant</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Realtime has been disabled for the tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">RealtimeNodeDisconnected</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Realtime is a distributed application and this means that one the system is unable to communicate with one of the distributed nodes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">RealtimeRestarting</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Realtime is currently restarting.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ReconnectSubscribeToPostgres</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Postgres changes still waiting to be subscribed.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ReplicationMaxWalSendersReached</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Maximum number of WAL senders reached in tenant database.</p><p>Learn more:</p><ul><li><a href="../database/custom-postgres-config.html#cli-configurable-settings">Configuring max WAL senders</a></li></ul></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">ReplicationSlotBeingUsed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The replication slot is being used by another transaction.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">RlsPolicyError</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error on RLS policy used for authorization.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">StartListenAndReplicationFailed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when starting the replication and listening of errors for database broadcasting.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">SubscriptionDeletionFailed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to delete a subscription for postgres changes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">SynInitializationError</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Our framework to syncronize processes has failed to properly startup a connection to the database.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">TableHasSpacesInName</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The table you are trying to listen to has spaces in its name which we are unable to support.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Change the table name to not have spaces in it.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">TenantNotFound</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>The tenant you are trying to connect to does not exist.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Verify the tenant name you are trying to connect to exists in the realtime.tenants table.</p></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">TimeoutOnRpcCall</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>RPC request within the Realtime server has timed out.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">TopicNameRequired</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>You are trying to use Realtime without a topic name set.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableCheckoutConnection</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to checkout a connection from the tenant pool.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToCheckProcessesOnRemoteNode</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to check the processes on a remote node.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToConnectToProject</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Unable to connect to Project database.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToConnectToTenantDatabase</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Realtime was not able to connect to the tenant&#x27;s database.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToCreateCounter</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to create a counter to track rate limits for a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToDecrementCounter</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to decrement a counter to track rate limits for a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToDeletePhantomSubscriptions</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to delete subscriptions that are no longer being used.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToDeleteTenant</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to delete a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToEncodeJson</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>An error were we are not handling correctly the response to be sent to the end user.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToFindCounter</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to find a counter to track rate limits for a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToIncrementCounter</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to increment a counter to track rate limits for a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToListenToTenantDatabase</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Unable to LISTEN for notifications against the Tenant Database.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToProcessListenPayload</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Payload sent in NOTIFY operation was JSON parsable.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToSetPolicies</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when setting up Authorization Policies.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToSubscribeToPostgres</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to subscribe to Postgres changes.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToTrackPresence</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when handling track presence for this socket.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnableToUpdateCounter</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Error when trying to update a counter to track rate limits for a tenant.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">Unauthorized</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Unauthorized access to Realtime channel.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnhandledProcessMessage</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Unhandled message received by a Realtime process.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnknownDataProcessed</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>An unknown data type was processed by the Realtime system.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnknownErrorOnChannel</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>An error we are not handling correctly was triggered on a channel.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnknownErrorOnController</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>An error we are not handling correctly was triggered on a controller.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnknownPresenceEvent</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Presence event type not recognized by service.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><code class="whitespace-nowrap">UnprocessableEntity</code></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><p>Received a HTTP request with a body that was not able to be processed by the endpoint.</p></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"></td></tr></tbody></table></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/error_codes.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\""])</script><script>self.__next_f.push([1,"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804"])</script><script>self.__next_f.push([1,"-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl"])</script><script>self.__next_f.push([1,"=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1"])</script><script>self.__next_f.push([1,"026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\""])</script><script>self.__next_f.push([1,",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GS"])</script><script>self.__next_f.push([1,"RZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v4"])</script><script>self.__next_f.push([1,"2MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\","])</script><script>self.__next_f.push([1,"\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,":HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"realtime\",\"error_codes\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"realtime\",{\"children\":[[\"slug\",\"error_codes\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"realtime\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"error_codes\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"ZTl3svOaEveooRiLjM0zY\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Operational Error Codes\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-3 text-xl text-foreground-light\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"List of operational codes to help understand your deployment and usage.\"]}]]}],[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/error_codes.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}],[\"$\",\"$L2a\",null,{\"video\":\"$u"])</script><script>self.__next_f.push([1,"ndefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Operational Error Codes | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"List of operational codes to help understand your deployment and usage.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/realtime/error_codes\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Operational Error Codes | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"List of operational codes to help understand your deployment and usage.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/realtime/error_codes\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=realtime\u0026title=Operational%20Error%20Codes\u0026description=undefined\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Operational Error Codes\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Operational Error Codes | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"List of operational codes to help understand your deployment and usage.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"29:[\"$\",\"div\",null,{\"className\":\"w-full overflow-auto\",\"children\":[\"$\",\"table\",null,{\"ref\":\"$undefined\",\"className\":\"w-full caption-bottom text-sm\",\"children\":[[\"$\",\"thead\",null,{\"ref\":\"$undefined\",\"className\":\"[\u0026_tr]:border-b\",\"children\":[\"$\",\"tr\",null,{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"th\",null,{\"ref\":\"$undefined\",\"className\":\"h-12 px-4 text-left align-middle font-medium text-foreground-muted [\u0026:has([role=checkbox])]:pr-0\",\"children\":\"Error code\"}],[\"$\",\"th\",null,{\"ref\":\"$undefined\",\"className\":\"h-12 px-4 text-left align-middle font-medium text-foreground-muted [\u0026:has([role=checkbox])]:pr-0\",\"children\":\"Description\"}],[\"$\",\"th\",null,{\"ref\":\"$undefined\",\"className\":\"h-12 px-4 text-left align-middle font-medium text-foreground-muted [\u0026:has([role=checkbox])]:pr-0\",\"children\":\"Action\"}]]}]}],[\"$\",\"tbody\",null,{\"ref\":\"$undefined\",\"className\":\"[\u0026_tr:last-child]:border-0\",\"children\":[[\"$\",\"tr\",\"ChannelRateLimitReached\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ChannelRateLimitReached\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The number of channels you can create has reached its limit.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ClientJoinRateLimitReached\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ClientJoinRateLimitReached\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The rate of joins per second from your clients has reached the channel limits.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ConnectionInitializing\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ConnectionInitializing\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Database is initializing connection.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ConnectionRateLimitReached\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ConnectionRateLimitReached\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The number of connected clients has reached its limit.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"DatabaseConnectionIssue\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"DatabaseConnectionIssue\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Database had connection issues and connection was not able to be established.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"DatabaseLackOfConnections\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"DatabaseLackOfConnections\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Realtime was not able to connect to the tenant's database due to not having enough available connections.\"}],[[\"$\",\"p\",null,{\"children\":\"Learn more:\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",\"https://supabase.com/docs/guides/database/connection-management\",{\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://supabase.com/docs/guides/database/connection-management\",\"children\":\"Connection management guide\"}]}]]}]]]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Verify your database connection limits.\"}]}]]}],[\"$\",\"tr\",\"ErrorAuthorizingWebsocket\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorAuthorizingWebsocket\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to authorize the WebSocket connection.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Verify user information on connect.\"}]}]]}],[\"$\",\"tr\",\"ErrorConnectingToWebsocket\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorConnectingToWebsocket\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to connect to the WebSocket server.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Verify user information on connect.\"}]}]]}],[\"$\",\"tr\",\"ErrorExecutingTransaction\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorExecutingTransaction\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error executing a database transaction in tenant database.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ErrorOnRpcCall\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorOnRpcCall\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when calling another realtime node.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ErrorStartingPostgresCDC\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorStartingPostgresCDC\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when starting the Postgres CDC extension which is used for Postgres Changes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ErrorStartingPostgresCDCStream\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ErrorStartingPostgresCDCStream\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when starting the Postgres CDC stream which is used for Postgres Changes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"IncreaseConnectionPool\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"IncreaseConnectionPool\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The number of connections you have set for Realtime are not enough to handle your current use case.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"InitializingProjectConnection\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"InitializingProjectConnection\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Connection against Tenant database is still starting.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"InvalidJWTExpiration\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"InvalidJWTExpiration\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"JWT exp claim value it's incorrect.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"JanitorFailedToDeleteOldMessages\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"JanitorFailedToDeleteOldMessages\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Scheduled task for realtime.message cleanup was unable to run.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"JwtSignatureError\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"JwtSignatureError\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"JWT signature was not able to be validated.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"MalformedJWT\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"MalformedJWT\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Token received does not comply with the JWT format.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"MigrationCheckFailed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"MigrationCheckFailed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Check to see if we require to run migrations fails.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"MigrationsFailedToRun\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"MigrationsFailedToRun\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when running the migrations against the Tenant database that are required by Realtime.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"PartitionCreationFailed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"PartitionCreationFailed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when creating partitions for realtime.messages.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"PoolingReplicationError\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"PoolingReplicationError\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when pooling the replication slot.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"PoolingReplicationPreparationError\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"PoolingReplicationPreparationError\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when preparing the replication slot.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"RealtimeDisabledForConfiguration\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"RealtimeDisabledForConfiguration\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The configuration provided to Realtime on connect will not be able to provide you any Postgres Changes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Verify your configuration on channel startup as you might not have your tables properly registered.\"}]}]]}],[\"$\",\"tr\",\"RealtimeDisabledForTenant\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"RealtimeDisabledForTenant\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Realtime has been disabled for the tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"RealtimeNodeDisconnected\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"RealtimeNodeDisconnected\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Realtime is a distributed application and this means that one the system is unable to communicate with one of the distributed nodes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"RealtimeRestarting\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"RealtimeRestarting\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Realtime is currently restarting.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ReconnectSubscribeToPostgres\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ReconnectSubscribeToPostgres\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Postgres changes still waiting to be subscribed.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ReplicationMaxWalSendersReached\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ReplicationMaxWalSendersReached\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Maximum number of WAL senders reached in tenant database.\"}],[[\"$\",\"p\",null,{\"children\":\"Learn more:\"}],[\"$\",\"ul\",null,{\"children\":[[\"$\",\"li\",\"https://supabase.com/docs/guides/database/custom-postgres-config#cli-configurable-settings\",{\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://supabase.com/docs/guides/database/custom-postgres-config#cli-configurable-settings\",\"children\":\"Configuring max WAL senders\"}]}]]}]]]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"ReplicationSlotBeingUsed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"ReplicationSlotBeingUsed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The replication slot is being used by another transaction.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"RlsPolicyError\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"RlsPolicyError\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error on RLS policy used for authorization.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"StartListenAndReplicationFailed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"StartListenAndReplicationFailed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when starting the replication and listening of errors for database broadcasting.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"SubscriptionDeletionFailed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"SubscriptionDeletionFailed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to delete a subscription for postgres changes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"SynInitializationError\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"SynInitializationError\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Our framework to syncronize processes has failed to properly startup a connection to the database.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"TableHasSpacesInName\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"TableHasSpacesInName\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The table you are trying to listen to has spaces in its name which we are unable to support.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Change the table name to not have spaces in it.\"}]}]]}],[\"$\",\"tr\",\"TenantNotFound\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"TenantNotFound\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"The tenant you are trying to connect to does not exist.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"p\",null,{\"children\":\"Verify the tenant name you are trying to connect to exists in the realtime.tenants table.\"}]}]]}],[\"$\",\"tr\",\"TimeoutOnRpcCall\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"TimeoutOnRpcCall\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"RPC request within the Realtime server has timed out.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"TopicNameRequired\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"TopicNameRequired\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"You are trying to use Realtime without a topic name set.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableCheckoutConnection\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableCheckoutConnection\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to checkout a connection from the tenant pool.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToCheckProcessesOnRemoteNode\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToCheckProcessesOnRemoteNode\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to check the processes on a remote node.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToConnectToProject\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToConnectToProject\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Unable to connect to Project database.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToConnectToTenantDatabase\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToConnectToTenantDatabase\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Realtime was not able to connect to the tenant's database.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToCreateCounter\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToCreateCounter\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to create a counter to track rate limits for a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToDecrementCounter\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToDecrementCounter\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to decrement a counter to track rate limits for a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToDeletePhantomSubscriptions\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToDeletePhantomSubscriptions\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to delete subscriptions that are no longer being used.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToDeleteTenant\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToDeleteTenant\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to delete a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToEncodeJson\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToEncodeJson\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"An error were we are not handling correctly the response to be sent to the end user.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToFindCounter\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToFindCounter\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to find a counter to track rate limits for a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToIncrementCounter\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToIncrementCounter\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to increment a counter to track rate limits for a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToListenToTenantDatabase\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToListenToTenantDatabase\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Unable to LISTEN for notifications against the Tenant Database.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToProcessListenPayload\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToProcessListenPayload\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Payload sent in NOTIFY operation was JSON parsable.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToSetPolicies\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToSetPolicies\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when setting up Authorization Policies.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToSubscribeToPostgres\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToSubscribeToPostgres\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to subscribe to Postgres changes.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToTrackPresence\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToTrackPresence\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when handling track presence for this socket.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnableToUpdateCounter\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnableToUpdateCounter\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Error when trying to update a counter to track rate limits for a tenant.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"Unauthorized\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"Unauthorized\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Unauthorized access to Realtime channel.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnhandledProcessMessage\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnhandledProcessMessage\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Unhandled message received by a Realtime process.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnknownDataProcessed\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnknownDataProcessed\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"An unknown data type was processed by the Realtime system.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnknownErrorOnChannel\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnknownErrorOnChannel\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"An error we are not handling correctly was triggered on a channel.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnknownErrorOnController\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnknownErrorOnController\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"An error we are not handling correctly was triggered on a controller.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnknownPresenceEvent\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnknownPresenceEvent\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Presence event type not recognized by service.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}],[\"$\",\"tr\",\"UnprocessableEntity\",{\"ref\":\"$undefined\",\"className\":\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\"children\":[[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[\"$\",\"code\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"UnprocessableEntity\"}]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":[[\"$\",\"p\",null,{\"children\":\"Received a HTTP request with a body that was not able to be processed by the endpoint.\"}],false]}],[\"$\",\"td\",null,{\"ref\":\"$undefined\",\"className\":\"p-4 align-middle [\u0026:has([role=checkbox])]:pr-0\",\"children\":false}]]}]]}]]}]}]\n"])</script></body></html>