<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="../../supabase-dark.svg"/><link rel="preload" as="image" href="../../supabase-light.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"/><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6845-5f881c8b60380d4a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Subscribing to Database Changes | Supabase Docs</title><meta name="description" content="Listen to database changes in real-time from your website or application."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="subscribing-to-database-changes.html"/><meta property="og:title" content="Subscribing to Database Changes | Supabase Docs"/><meta property="og:description" content="Listen to database changes in real-time from your website or application."/><meta property="og:url" content="https://supabase.com/docs/guides/realtime/subscribing-to-database-changes"/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs&amp;type=realtime&amp;title=Subscribing%20to%20Database%20Changes&amp;description=Listen%20to%20database%20changes%20in%20real-time%20from%20your%20website%20or%20application."/><meta property="og:image:width" content="800"/><meta property="og:image:height" content="600"/><meta property="og:image:alt" content="Subscribing to Database Changes"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:modified_time" content="2025-07-31T20:16:26.436Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Subscribing to Database Changes | Supabase Docs"/><meta name="twitter:description" content="Listen to database changes in real-time from your website or application."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><nav aria-labelledby="main-nav-title" class="fixed z-40 lg:z-auto w-0 -left-full lg:w-[420px] !lg:left-0 lg:top-[var(--header-height)] lg:sticky h-screen lg:h-[calc(100vh-var(--header-height))] lg:left-0 transition-all top-0 bottom-0 flex flex-col ml-0 border-r lg:overflow-y-auto"><div class="top-0 lg:top-[var(--header-height)] relative lg:sticky w-full lg:w-auto h-fit lg:h-screen overflow-y-scroll lg:overflow-auto [overscroll-behavior:contain] backdrop-blur backdrop-filter bg-background flex flex-col flex-grow"><span id="main-nav-title" class="sr-only">Main menu</span><div class="top-0 sticky h-0 z-10"><div class="bg-gradient-to-b from-background to-transparent h-4 w-full"></div></div><div class="transition-all ease-out duration-200 absolute left-0 right-0 px-5 pl-5 pt-6 pb-16 bg-background lg:relative lg:left-0 lg:pb-10 lg:px-10 lg:flex lg:opacity-100 lg:visible"><div class="transition-all duration-150 ease-out opacity-100 ml-0 delay-150" data-orientation="vertical"><ul class="relative w-full flex flex-col gap-0 pb-5"><a href="../realtime.html"><div class="flex items-center gap-3 my-3 text-brand-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z" fill="currentColor"></path></svg><span class="  false hover:text-brand text-foreground">Realtime</span></div></a><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="../realtime.html">Overview</a></li></div><div><li class="mb-1.5"><a class="cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="concepts.html">Concepts</a></li></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Usage</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="broadcast.html">Broadcast</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="presence.html">Presence</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="postgres-changes.html">Postgres Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="settings.html">Settings</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Security</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="authorization.html">Authorization</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Guides</span><div data-state="open" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm text-brand font-medium" href="subscribing-to-database-changes.html">Subscribing to Database Changes</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-with-nextjs.html">Using Realtime with Next.js</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-user-presence.html">Using Realtime Presence with Flutter</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="realtime-listening-flutter.html">Listening to Postgres Changes with Flutter</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Deep dive</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="quotas.html">Quotas</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="pricing.html">Pricing</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="architecture.html">Architecture</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="protocol.html">Protocol</a></li></div><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="benchmarks.html">Benchmarks</a></li></div></div></div><div><div class="flex flex-col gap-2.5"><div class="h-px w-full bg-border my-3"></div><span class="font-mono text-xs uppercase text-foreground font-medium tracking-wider">Debugging</span><div data-state="closed" data-orientation="vertical"><li><a class="flex items-center gap-2 cursor-pointer transition text-sm hover:text-foreground text-foreground-lighter" href="error_codes.html">Operational Error Codes</a></li></div></div></div></ul></div></div></div></nav><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="../getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R4skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R4skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R6skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R6skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«R8skqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«R8skqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Raskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Raskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«R4kqtqcrlb»-trigger-radix-«Rcskqtqcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«R4kqtqcrlb»-content-radix-«Rcskqtqcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="../../../docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="../../supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="../../supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«Rnkqtqcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"><div class="lg:hidden px-3.5 border-b z-10 transition-all ease-out top-0 flex items-center gap-1"><button class="h-8 w-8 flex group items-center justify-center mr-1"><div class="space-y-1 cursor-pointer relative w-4 h-[8px]"><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-4"></span><span class="transition-all ease-out block h-px bg-foreground-muted group-hover:bg-foreground w-3 group-hover:w-4"></span></div></button><span class="transition-all duration-200 text-foreground text-sm">Realtime</span></div></div><div class="grow"><div class="max-w-7xl px-5 mx-auto py-8 pb-0"><div class="grid grid-cols-12 relative gap-4"><div class="relative transition-all ease-out duration-100 col-span-12 md:col-span-9"><!--$--><nav aria-label="breadcrumb" class="mb-2"><ol class="flex flex-wrap items-center gap-0.5 break-words text-sm sm:gap-1.5 text-foreground-lighter p-0"><li class="inline-flex text-foreground-lighter items-center gap-1.5 leading-5"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="../realtime.html">Realtime</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden"><span role="link" aria-disabled="true" aria-current="page" class="no-underline text-foreground">Guides</span></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li><li class="text-foreground-lighter gap-1.5 leading-5 flex items-center overflow-hidden md:text-foreground-light"><a class="transition-colors underline lg:no-underline hover:text-foreground" href="subscribing-to-database-changes.html">Subscribing to Database Changes</a></li><li role="presentation" aria-hidden="true" class="[&amp;&gt;svg]:size-3.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></li></ol></nav><!--/$--><article id="sb-docs-guide-main-article" class="prose max-w-none"><h1 class="mb-0 [&amp;&gt;p]:m-0"><p>Subscribing to Database Changes</p></h1><h2 class="mt-3 text-xl text-foreground-light"><p>Listen to database changes in real-time from your website or application.</p></h2><hr class="not-prose border-t-0 border-b my-8"/><p>You can use Supabase to subscribe to real-time database changes. There are two options available:</p>
<ol>
<li><a href="broadcast.html">Broadcast</a>. This is the recommended method for scalability and security.</li>
<li><a href="postgres-changes.html">Postgres Changes</a>. This is a simpler method. It requires less setup, but does not scale as well as Broadcast.</li>
</ol>
<h2 id="using-broadcast" class="group scroll-mt-24">Using Broadcast<a href="#using-broadcast" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To automatically send messages when a record is created, updated, or deleted, we can attach a <a href="../database/postgres/triggers.html">Postgres trigger</a> to any table. Supabase Realtime provides a <code>realtime.broadcast_changes()</code> function which we can use in conjunction with a trigger. This function will use a private channel and needs broadcast authorization RLS policies to be met.</p>
<h3 id="broadcast-authorization" class="group scroll-mt-24">Broadcast authorization<a href="#broadcast-authorization" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="authorization.html">Realtime Authorization</a> is required for receiving Broadcast messages. This is an example of a policy that allows authenticated users to listen to messages from topics:</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">policy</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">Authenticated users can receive broadcasts</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">realtime</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string)">messages</span><span style="color:var(--code-token-punctuation)">&quot;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">select</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">to</span><span style="color:var(--code-foreground)"> authenticated</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">using</span><span style="color:var(--code-foreground)"> ( true );</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="create-a-trigger-function" class="group scroll-mt-24">Create a trigger function<a href="#create-a-trigger-function" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Let&#x27;s create a function that we can call any time a record is created, updated, or deleted. This function will make use of some of Postgres&#x27;s native <a href="https://www.postgresql.org/docs/current/plpgsql-trigger.html#PLPGSQL-DML-TRIGGER">trigger variables</a>. For this example, we want to have a topic with the name <code>topic:&lt;record id&gt;</code> to which we&#x27;re going to broadcast events.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div><div class="w-full">16</div><div class="w-full">17</div><div class="w-full">18</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">create or replace</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">function</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">public</span><span style="color:var(--code-foreground)">.your_table_changes</span><span style="color:var(--code-token-punctuation)">()</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">returns</span><span style="color:var(--code-foreground)"> trigger</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">security</span><span style="color:var(--code-foreground)"> definer</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">language</span><span style="color:var(--code-foreground)"> plpgsql</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">as</span><span style="color:var(--code-foreground)"> $$</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">begin</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  perform </span><span style="color:var(--code-token-constant)">realtime</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">broadcast_changes</span><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string)">topic:</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">||</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">coalesce</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">NEW</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">topic</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-constant)">OLD</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">topic</span><span style="color:var(--code-foreground)">) ::</span><span style="color:var(--code-token-keyword)">text</span><span style="color:var(--code-foreground)">, </span><span style="color:var(--code-token-comment);font-style:italic">-- topic - the topic to which we&#x27;re broadcasting</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    TG_OP,                                             </span><span style="color:var(--code-token-comment);font-style:italic">-- event - the event that triggered the function</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    TG_OP,                                             </span><span style="color:var(--code-token-comment);font-style:italic">-- operation - the operation that triggered the function</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    TG_TABLE_NAME,                                     </span><span style="color:var(--code-token-comment);font-style:italic">-- table - the table that caused the trigger</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    TG_TABLE_SCHEMA,                                   </span><span style="color:var(--code-token-comment);font-style:italic">-- schema - the schema of the table that caused the trigger</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    NEW,                                               </span><span style="color:var(--code-token-comment);font-style:italic">-- new record - the record after the change</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    OLD                                                </span><span style="color:var(--code-token-comment);font-style:italic">-- old record - the record before the change</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  );</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">null</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">end</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"><span style="color:var(--code-foreground)">$$;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="create-a-trigger" class="group scroll-mt-24">Create a trigger<a href="#create-a-trigger" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Let&#x27;s set up a trigger so the function is executed after any changes to the table.</p>
<!-- -->
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">trigger</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-function)">handle_your_table_changes</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">after</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">insert</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">or</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">update</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">or</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">delete</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">on</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">public</span><span style="color:var(--code-foreground)">.</span><span style="color:var(--code-token-constant)">your_table</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">for</span><span style="color:var(--code-foreground)"> each </span><span style="color:var(--code-token-keyword)">row</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">execute</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">function</span><span style="color:var(--code-foreground)"> your_table_changes </span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-foreground)">;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h4 id="listening-on-client-side" class="group scroll-mt-24">Listening on client side<a href="#listening-on-client-side" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Finally, on the client side, listen to the topic <code>topic:&lt;record_id&gt;</code> to receive the events. Remember to set the channel as a private channel, since <code>realtime.broadcast_changes</code> uses Realtime Authorization.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">gameId</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">id</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">await</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">supabase</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">realtime</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">setAuth</button><span style="color:var(--code-foreground)">() </span><span style="color:var(--code-token-comment);font-style:italic">// Needed for Realtime Authorization</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">changes</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-token-string)">topic:</span><span style="color:var(--code-token-punctuation)">${</span><button class="" data-state="closed">gameId</button><span style="color:var(--code-token-punctuation)">}`</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><button class="" data-state="closed">config</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">private</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-constant)">true</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">broadcast</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">INSERT</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">broadcast</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">UPDATE</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">broadcast</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">DELETE</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">},</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-foreground)">))</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">subscribe</button><span style="color:var(--code-foreground)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h2 id="using-postgres-changes" class="group scroll-mt-24">Using Postgres Changes<a href="#using-postgres-changes" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Postgres Changes are simple to use, but have some <a href="postgres-changes.html#limitations">limitations</a> as your application scales. We recommend using Broadcast for most use cases.</p>
<div class="video-container"><iframe src="https://www.youtube-nocookie.com/embed/2rUjcmgZDwQ" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen=""></iframe></div>
<h3 id="enable-postgres-changes" class="group scroll-mt-24">Enable Postgres Changes<a href="#enable-postgres-changes" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You&#x27;ll first need to create a <code>supabase_realtime</code> publication and add your tables (that you want to subscribe to) to the publication:</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div><div class="w-full">12</div><div class="w-full">13</div><div class="w-full">14</div><div class="w-full">15</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">begin</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- remove the supabase_realtime publication</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">drop</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  publication </span><span style="color:var(--code-token-keyword)">if</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">exists</span><span style="color:var(--code-foreground)"> supabase_realtime;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- re-create the supabase_realtime publication with no tables</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">create</span><span style="color:var(--code-foreground)"> publication supabase_realtime;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-keyword)">commit</span><span style="color:var(--code-foreground)">;</span></span><span class="block h-5"></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- add a table called &#x27;messages&#x27; to the publication</span></span><span class="block h-5"><span style="color:var(--code-token-comment);font-style:italic">-- (update this to match your tables)</span></span><span class="block h-5"><span style="color:var(--code-token-keyword)">alter</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  publication supabase_realtime </span><span style="color:var(--code-token-keyword)">add</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">table</span><span style="color:var(--code-foreground)"> messages;</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="streaming-inserts" class="group scroll-mt-24">Streaming inserts<a href="#streaming-inserts" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can use the <code>INSERT</code> event to stream all new rows.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">channel</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">schema-db-changes</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">postgres_changes</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">INSERT</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><button class="" data-state="closed">schema</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">public</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  )</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">subscribe</button><span style="color:var(--code-foreground)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div>
<h3 id="streaming-updates" class="group scroll-mt-24">Streaming updates<a href="#streaming-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can use the <code>UPDATE</code> event to stream all updated rows.</p>
<div class="shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm"><pre><code class="flex"><div class="flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2"><div class="w-full">1</div><div class="w-full">2</div><div class="w-full">3</div><div class="w-full">4</div><div class="w-full">5</div><div class="w-full">6</div><div class="w-full">7</div><div class="w-full">8</div><div class="w-full">9</div><div class="w-full">10</div><div class="w-full">11</div></div><div class="p-6 overflow-x-auto flex-grow"><span class="block h-5"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">channel</button><span style="color:var(--code-token-variable)"> </span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-variable)"> </span><button class="" data-state="closed">supabase</button></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">channel</button><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">schema-db-changes</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">on</button><span style="color:var(--code-foreground)">(</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">postgres_changes</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">{</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><button class="" data-state="closed">event</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">UPDATE</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">      </span><button class="" data-state="closed">schema</button><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">public</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">},</span></span><span class="block h-5"><span style="color:var(--code-foreground)">    </span><span style="color:var(--code-token-punctuation)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-foreground)"> </span><span style="color:var(--code-token-keyword)">=&gt;</span><span style="color:var(--code-foreground)"> </span><button class="" data-state="closed">console</button><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">log</button><span style="color:var(--code-foreground)">(</span><button class="" data-state="closed">payload</button><span style="color:var(--code-foreground)">)</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  )</span></span><span class="block h-5"><span style="color:var(--code-foreground)">  </span><span style="color:var(--code-token-punctuation)">.</span><button class="" data-state="closed">subscribe</button><span style="color:var(--code-foreground)">()</span></span></div></code></pre><button class="border rounded-md p-1 hover:bg-selection transition hidden group-hover:block absolute top-2 right-2"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-lighter"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><footer class="mt-16 not-prose"><a href="https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/subscribing-to-database-changes.mdx" class="w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors" target="_blank" rel="noreferrer noopener edit">Edit this page on GitHub <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></footer></article></div><div class="thin-scrollbar overflow-y-auto h-fit px-px hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]"><div class="w-full relative border-l flex flex-col gap-6 lg:gap-8 px-2 h-fit"><div class="pl-5"><section class="@container" aria-labelledby="feedback-title"><h3 id="feedback-title" class="block font-mono text-xs text-foreground-light mb-3">Is this helpful?</h3><div class="relative flex flex-col gap-2 @[12rem]:gap-4 @[12rem]:flex-row @[12rem]:items-center"><div style="--container-flex-gap:0.5rem" class="relative flex gap-2 items-center"><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-warning-600 hover:border-warning-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-current"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">No</span></span> </button><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs py-1 rounded-full px-1 w-7 h-7 text-foreground-light [transition-property:opacity,transform,color] [transition-duration:150ms,250ms,250ms] motion-reduce:[transition-duration:150ms,1ms,300ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] hover:text-brand-600 hover:border-brand-500"> <span class="truncate"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check"><path d="M20 6 9 17l-5-5"></path></svg><span class="sr-only">Yes</span></span> </button></div><div class="flex flex-col gap-0.5 @[12rem]:absolute @[12rem]:left-9 text-xs opacity-0 invisible text-left -translate-x-2 [transition-property:opacity,transform] [transition-duration:450ms,300ms] [transition-delay:200ms,0ms] [transition-timing-function:cubic-bezier(.76,0,.23,1)] motion-reduce:[transition-duration:150ms,1ms] !ease-out"></div></div></section></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></div><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="../../../changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="../../../index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="../../../open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div></div></div></div><script src="https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/chunks/webpack-38f88d6c1bb9a7d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\""])</script><script>self.__next_f.push([1,"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804"])</script><script>self.__next_f.push([1,"-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl"])</script><script>self.__next_f.push([1,"=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1"])</script><script>self.__next_f.push([1,"026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667"])</script><script>self.__next_f.push([1,"\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\""])</script><script>self.__next_f.push([1,",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4398\",\"static/chunks/4398-c5338953de60daa2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3197\",\"static/chunks/app/guides/realtime/layout-89f210e08199ec85.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GS"])</script><script>self.__next_f.push([1,"RZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v4"])</script><script>self.__next_f.push([1,"2MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\","])</script><script>self.__next_f.push([1,"\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"SonnerToaster\"]\n1a:I[18206,[],\"MetadataBoundary\"]\n1c:I[18206,[],\"OutletBoundary\"]\n1f:I[38670,[],\"AsyncMetadataOutlet\"]\n21:I[18206,[],\"ViewportBoundary\"]\n23:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,":HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JCK1_Q8MPrFKluVE6IGz9\",\"p\":\"https://frontend-assets.supabase.com/docs/768f7819c750\",\"c\":[\"\",\"guides\",\"realtime\",\"subscribing-to-database-changes\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"guides\",{\"children\":[\"realtime\",{\"children\":[[\"slug\",\"subscribing-to-database-changes\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/616ab8a563c5d73b.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/075d453bd5f7301f.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/db78b4e916a69931.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/c912681474966412.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"guides\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"realtime\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/768f7819c750/_next/static/css/d46d3073e0d464d8.css?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L18\"]}],{\"children\":[[\"slug\",\"subscribing-to-database-changes\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"children\":\"$L1b\"}],null,[\"$\",\"$L1c\",null,{\"children\":[\"$L1d\",\"$L1e\",[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"XtWxPQYIItMrA0kL_EVau\",{\"children\":[[\"$\",\"$L21\",null,{\"children\":\"$L22\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$23\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"24:\"$Sreact.suspense\"\n25:I[38670,[],\"AsyncMetadata\"]\n1b:[\"$\",\"$24\",null,{\"fallback\":null,\"children\":[\"$\",\"$L25\",null,{\"promise\":\"$@26\"}]}]\n"])</script><script>self.__next_f.push([1,"1e:null\n"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L10\",null,{\"NavigationMenu\":\"$undefined\",\"additionalNavItems\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8 pb-0\",\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]\n"])</script><script>self.__next_f.push([1,"22:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n1d:null\n"])</script><script>self.__next_f.push([1,"27:I[89597,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"TocAnchorsProvider\"]\n28:I[63233,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/c"])</script><script>self.__next_f.push([1,"hunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n2a:I[30213,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_"])</script><script>self.__next_f.push([1,"GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n19:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-12 relative gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-100 col-span-12 md:col-span-9\",\"children\":[[\"$\",\"$L28\",null,{\"className\":\"mb-2\"}],[\"$\",\"article\",null,{\"id\":\"sb-docs-guide-main-article\",\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"mb-0 [\u0026\u003ep]:m-0\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Subscribing to Database Changes\"]}]]}],[\"$\",\"h2\",null,{\"className\":\"mt-3 text-xl text-foreground-light\",\"children\":[[\"$\",\"p\",\"0\",{\"children\":[\"Listen to database changes in real-time from your website or application.\"]}]]}],[\"$\",\"hr\",null,{\"className\":\"not-prose border-t-0 border-b my-8\"}],\"$L29\",\"$undefined\",[\"$\",\"footer\",null,{\"className\":\"mt-16 not-prose\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/subscribing-to-database-changes.mdx\",\"className\":\"w-fit flex items-center gap-1 text-sm text-scale-1000 hover:text-scale-1200 transition-colors\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener edit\",\"children\":[\"Edit this page on GitHub \",[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":14,\"height\":14,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":1.5,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}]]}]}]]}]]}]"])</script><script>self.__next_f.push([1,",[\"$\",\"$L2a\",null,{\"video\":\"$undefined\",\"className\":\"hidden md:flex col-span-3 self-start sticky top-[calc(var(--header-height)+1px+2rem)] max-h-[calc(100vh-var(--header-height)-3rem)]\"}]]}]}]\n"])</script><script>self.__next_f.push([1,"26:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Subscribing to Database Changes | Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Listen to database changes in real-time from your website or application.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs/guides/realtime/subscribing-to-database-changes\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Subscribing to Database Changes | Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Listen to database changes in real-time from your website or application.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs/guides/realtime/subscribing-to-database-changes\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://obuldanrptloktxcffvn.supabase.co/functions/v1/og-images?site=docs\u0026type=realtime\u0026title=Subscribing%20to%20Database%20Changes\u0026description=Listen%20to%20database%20changes%20in%20real-time%20from%20your%20website%20or%20application.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"800\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"600\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"Subscribing to Database Changes\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"13\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"14\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T20:16:26.436Z\"}],[\"$\",\"meta\",\"15\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"Subscribing to Database Changes | Supabase Docs\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Listen to database changes in real-time from your website or application.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"22\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"30\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"31\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"32\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"37\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"38\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"39\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":\"$26:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2b:I[86692,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"29:[[\"$\",\"p\",null,{\"children\":\"You can use Supabase to subscribe to real-time database changes. There are two options available:\"}],\"\\n\",[\"$\",\"ol\",null,{\"children\":[\"\\n\",[\"$\",\"li\",null,{\"children\":[[\"$\",\"a\",null,{\"href\":\"/docs/guides/realtime/broadcast\",\"children\":\"Broadcast\"}],\". This is the recommended method for scalability and security.\"]}],\"\\n\",[\"$\",\"li\",null,{\"children\":[[\"$\",\"a\",null,{\"href\":\"/docs/guides/realtime/postgres-changes\",\"children\":\"Postgres Changes\"}],\". This is a simpler method. It requires less setup, but does not scale as well as Broadcast.\"]}],\"\\n\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Using Broadcast\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"To automatically send messages when a record is created, updated, or deleted, we can attach a \",[\"$\",\"a\",null,{\"href\":\"/docs/guides/database/postgres/triggers\",\"children\":\"Postgres trigger\"}],\" to any table. Supabase Realtime provides a \",[\"$\",\"code\",null,{\"children\":\"realtime.broadcast_changes()\"}],\" function which we can use in conjunction with a trigger. This function will use a private channel and needs broadcast authorization RLS policies to be met.\"]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Broadcast authorization\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[[\"$\",\"a\",null,{\"href\":\"/docs/guides/realtime/authorization\",\"children\":\"Realtime Authorization\"}],\" is required for receiving Broadcast messages. This is an example of a policy that allows authenticated users to listen to messages from topics:\"]}],\"\\n\",\"\\n\",\"$L2c\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Create a trigger function\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Let's create a function that we can call any time a record is created, updated, or deleted. This function will make use of some of Postgres's native \",[\"$\",\"a\",null,{\"href\":\"https://www.postgresql.org/docs/current/plpgsql-trigger.html#PLPGSQL-DML-TRIGGER\",\"children\":\"trigger variables\"}],\". For this example, we want to have a topic with the name \",[\"$\",\"code\",null,{\"children\":\"topic:\u003crecord id\u003e\"}],\" to which we're going to broadcast events.\"]}],\"\\n\",\"\\n\",\"$L2d\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Create a trigger\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":\"Let's set up a trigger so the function is executed after any changes to the table.\"}],\"\\n\",\"\\n\",\"$L2e\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h4\",\"children\":\"Listening on client side\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Finally, on the client side, listen to the topic \",[\"$\",\"code\",null,{\"children\":\"topic:\u003crecord_id\u003e\"}],\" to receive the events. Remember to set the channel as a private channel, since \",[\"$\",\"code\",null,{\"children\":\"realtime.broadcast_changes\"}],\" uses Realtime Authorization.\"]}],\"\\n\",\"$L2f\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h2\",\"children\":\"Using Postgres Changes\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"Postgres Changes are simple to use, but have some \",[\"$\",\"a\",null,{\"href\":\"/docs/guides/realtime/postgres-changes#limitations\",\"children\":\"limitations\"}],\" as your application scales. We recommend using Broadcast for most use cases.\"]}],\"\\n\",[\"$\",\"div\",null,{\"className\":\"video-container\",\"children\":[\"$\",\"iframe\",null,{\"src\":\"https://www.youtube-nocookie.com/embed/2rUjcmgZDwQ\",\"allow\":\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\"allowFullScreen\":true}]}],\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Enable Postgres Changes\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You'll first need to create a \",[\"$\",\"code\",null,{\"children\":\"supabase_realtime\"}],\" publication and add your tables (that you want to subscribe to) to the publication:\"]}],\"\\n\",\"$L30\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Streaming inserts\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can use the \",[\"$\",\"code\",null,{\"children\":\"INSERT\"}],\" event to stream all new rows.\"]}],\"\\n\",\"$L31\",\"\\n\",[\"$\",\"$L2b\",null,{\"tag\":\"h3\",\"children\":\"Streaming updates\"}],\"\\n\",[\"$\",\"p\",null,{\"children\":[\"You can use the \",[\"$\",\"code\",null,{\"children\":\"UPDATE\"}],\" event to stream all updated rows.\"]}],\"\\n\",\"$L32\"]\n"])</script><script>self.__next_f.push([1,"33:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunks/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"CodeCopyButton\"]\n34:I[18983,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8736\",\"static/chunks/8736-4f0827621964c1b2.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"9142\",\"static/chunk"])</script><script>self.__next_f.push([1,"s/9142-d8c7b6d5e2a844e1.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"4503\",\"static/chunks/4503-3a987e2c9e82dc60.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"860\",\"static/chunks/860-049e1d204d95ebc4.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"5393\",\"static/chunks/5393-6812db371b6c99aa.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"150\",\"static/chunks/150-9733218a44650787.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"3714\",\"static/chunks/3714-07c0dad944e7fe30.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\",\"72\",\"static/chunks/app/guides/realtime/%5B%5B...slug%5D%5D/page-5165de2bace803b9.js?dpl=dpl_GSRZdB1v42MeC7L76FNhybEfz667\"],\"AnnotatedSpan\"]\n"])</script><script>self.__next_f.push([1,"2c:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"policy\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"Authenticated users can receive broadcasts\"}],[\"$\",\"span\",\"57\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"realtime\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"messages\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"select\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"to\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" authenticated\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"using\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" ( true );\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"create policy \\\"Authenticated users can receive broadcasts\\\"\\non \\\"realtime\\\".\\\"messages\\\"\\nfor select\\nto authenticated\\nusing ( true );\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2d:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}],[\"$\",\"div\",\"15\",{\"className\":\"w-full\",\"children\":16}],[\"$\",\"div\",\"16\",{\"className\":\"w-full\",\"children\":17}],[\"$\",\"div\",\"17\",{\"className\":\"w-full\",\"children\":18}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create or replace\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"function\"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"public\"}],[\"$\",\"span\",\"33\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".your_table_changes\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"returns\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" trigger\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"security\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" definer\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"language\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" plpgsql\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"as\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" $$\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"begin\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  perform \"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"realtime\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"broadcast_changes\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"topic:\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"||\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"coalesce\"}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"NEW\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"topic\"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"OLD\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"topic\"}],[\"$\",\"span\",\"45\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") ::\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"text\"}],[\"$\",\"span\",\"53\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\", \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- topic - the topic to which we're broadcasting\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    TG_OP,                                             \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- event - the event that triggered the function\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    TG_OP,                                             \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- operation - the operation that triggered the function\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    TG_TABLE_NAME,                                     \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- table - the table that caused the trigger\"}]]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    TG_TABLE_SCHEMA,                                   \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- schema - the schema of the table that caused the trigger\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    NEW,                                               \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- new record - the record after the change\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    OLD                                                \"}],[\"$\",\"span\",\"55\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- old record - the record before the change\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  );\"}]]}],[\"$\",\"span\",\"15\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"null\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"16\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"end\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"17\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"$$$;\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"create or replace function public.your_table_changes()\\nreturns trigger\\nsecurity definer\\nlanguage plpgsql\\nas $$\\nbegin\\n  perform realtime.broadcast_changes(\\n    'topic:' || coalesce(NEW.topic, OLD.topic) ::text, -- topic - the topic to which we're broadcasting\\n    TG_OP,                                             -- event - the event that triggered the function\\n    TG_OP,                                             -- operation - the operation that triggered the function\\n    TG_TABLE_NAME,                                     -- table - the table that caused the trigger\\n    TG_TABLE_SCHEMA,                                   -- schema - the schema of the table that caused the trigger\\n    NEW,                                               -- new record - the record after the change\\n    OLD                                                -- old record - the record before the change\\n  );\\n  return null;\\nend;\\n$$;\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"2e:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"trigger\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"handle_your_table_changes\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"after\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"insert\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"or\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"update\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"or\"}],[\"$\",\"span\",\"25\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"delete\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"on\"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"public\"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\".\"}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"your_table\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"for\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" each \"}],[\"$\",\"span\",\"9\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"row\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"execute\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"8\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"function\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" your_table_changes \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",\"38\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"create trigger handle_your_table_changes\\nafter insert or update or delete\\non public.your_table\\nfor each row\\nexecute function your_table_changes ();\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"35:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"36:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"37:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"2f:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"gameId\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const gameId: \\\"id\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":6,\"target\":\"gameId\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"id\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"await\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"supabase\",\"offset\":6,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":26,\"length\":8,\"target\":\"supabase\",\"line\":1,\"character\":6}]}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"15\",{\"token\":{\"content\":\"realtime\",\"offset\":15,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) SupabaseClient\u003cany, \\\"public\\\", any\u003e.realtime: RealtimeClient\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":35,\"length\":8,\"target\":\"realtime\",\"line\":1,\"character\":15}]}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"24\",{\"token\":{\"content\":\"setAuth\",\"offset\":24,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeClient.setAuth(token?: string | null): Promise\u003cvoid\u003e\",\"docs\":\"Sets the JWT access token used for channel subscription authorization and Realtime RLS.\\n\\nIf param is null it will use the `accessToken` callback function or the token set on the client.\\n\\nOn callback used, it will set the value of the token internal to the client.\",\"tags\":[[\"param\",\"token A JWT string to override the token set on the client.\"]],\"start\":44,\"length\":7,\"target\":\"setAuth\",\"line\":1,\"character\":24}]}],[\"$\",\"span\",\"31\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"() \"}],[\"$\",\"span\",\"34\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Needed for Realtime Authorization\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"changes\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const changes: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":97,\"length\":7,\"target\":\"changes\",\"line\":2,\"character\":6}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"16\",{\"token\":{\"content\":\"supabase\",\"offset\":16,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":107,\"length\":8,\"target\":\"supabase\",\"line\":2,\"character\":16}]}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"channel\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":119,\"length\":7,\"target\":\"channel\",\"line\":3,\"character\":3}]}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"topic:\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"$${\"}],[\"$\",\"$L34\",\"20\",{\"token\":{\"content\":\"gameId\",\"offset\":20,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const gameId: \\\"id\\\"\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":136,\"length\":6,\"target\":\"gameId\",\"line\":3,\"character\":20}]}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}`\"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"$L34\",\"4\",{\"token\":{\"content\":\"config\",\"offset\":4,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) config: {\\n    broadcast?: {\\n        self?: boolean;\\n        ack?: boolean;\\n    };\\n    presence?: {\\n        key?: string;\\n    };\\n    private?: boolean;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":152,\"length\":6,\"target\":\"config\",\"line\":4,\"character\":4}]}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"14\",{\"token\":{\"content\":\"private\",\"offset\":14,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) private?: boolean | undefined\",\"docs\":\"defines if the channel is private or not and if RLS policies will be used to check data\",\"tags\":\"$undefined\",\"start\":162,\"length\":7,\"target\":\"private\",\"line\":4,\"character\":14}]}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"true\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",\"3\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on(type: `${REALTIME_LISTEN_TYPES.BROADCAST}`, filter: {\\n    event: string;\\n}, callback: (payload: {\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n    [key: string]: any;\\n}) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\",\"tags\":[[\"param\",\"type One of \\\"broadcast\\\", \\\"presence\\\", or \\\"postgres_changes\\\".\"],[\"param\",\"filter Custom object specific to the Realtime feature detailing which payloads to receive.\"],[\"param\",\"callback Function to be invoked when event handler is triggered.\"]],\"start\":187,\"length\":2,\"target\":\"on\",\"line\":6,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"broadcast\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"21\",{\"token\":{\"content\":\"event\",\"offset\":21,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":205,\"length\":5,\"target\":\"event\",\"line\":6,\"character\":21}]}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"INSERT\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"41\",{\"token\":{\"content\":\"payload\",\"offset\":41,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":225,\"length\":7,\"target\":\"payload\",\"line\":6,\"character\":41}]}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"53\",{\"token\":{\"content\":\"console\",\"offset\":53,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$35\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":237,\"length\":7,\"target\":\"console\",\"line\":6,\"character\":53}]}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"61\",{\"token\":{\"content\":\"log\",\"offset\":61,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":245,\"length\":3,\"target\":\"log\",\"line\":6,\"character\":61}]}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"65\",{\"token\":{\"content\":\"payload\",\"offset\":65,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":249,\"length\":7,\"target\":\"payload\",\"line\":6,\"character\":65}]}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on(type: `${REALTIME_LISTEN_TYPES.BROADCAST}`, filter: {\\n    event: string;\\n}, callback: (payload: {\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n    [key: string]: any;\\n}) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\",\"tags\":[[\"param\",\"type One of \\\"broadcast\\\", \\\"presence\\\", or \\\"postgres_changes\\\".\"],[\"param\",\"filter Custom object specific to the Realtime feature detailing which payloads to receive.\"],[\"param\",\"callback Function to be invoked when event handler is triggered.\"]],\"start\":262,\"length\":2,\"target\":\"on\",\"line\":7,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"broadcast\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"21\",{\"token\":{\"content\":\"event\",\"offset\":21,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":280,\"length\":5,\"target\":\"event\",\"line\":7,\"character\":21}]}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"UPDATE\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"41\",{\"token\":{\"content\":\"payload\",\"offset\":41,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":300,\"length\":7,\"target\":\"payload\",\"line\":7,\"character\":41}]}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"53\",{\"token\":{\"content\":\"console\",\"offset\":53,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$36\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":312,\"length\":7,\"target\":\"console\",\"line\":7,\"character\":53}]}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"61\",{\"token\":{\"content\":\"log\",\"offset\":61,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":320,\"length\":3,\"target\":\"log\",\"line\":7,\"character\":61}]}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"65\",{\"token\":{\"content\":\"payload\",\"offset\":65,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":324,\"length\":7,\"target\":\"payload\",\"line\":7,\"character\":65}]}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on(type: `${REALTIME_LISTEN_TYPES.BROADCAST}`, filter: {\\n    event: string;\\n}, callback: (payload: {\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n    [key: string]: any;\\n}) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\",\"tags\":[[\"param\",\"type One of \\\"broadcast\\\", \\\"presence\\\", or \\\"postgres_changes\\\".\"],[\"param\",\"filter Custom object specific to the Realtime feature detailing which payloads to receive.\"],[\"param\",\"callback Function to be invoked when event handler is triggered.\"]],\"start\":337,\"length\":2,\"target\":\"on\",\"line\":8,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"7\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"broadcast\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",\"18\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"19\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"21\",{\"token\":{\"content\":\"event\",\"offset\":21,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: string\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":355,\"length\":5,\"target\":\"event\",\"line\":8,\"character\":21}]}],[\"$\",\"span\",\"26\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"27\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"DELETE\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"37\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}],[\"$\",\"span\",\"39\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"40\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"41\",{\"token\":{\"content\":\"payload\",\"offset\":41,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":375,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":41}]}],[\"$\",\"span\",\"48\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"49\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"50\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"52\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"53\",{\"token\":{\"content\":\"console\",\"offset\":53,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$37\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":387,\"length\":7,\"target\":\"console\",\"line\":8,\"character\":53}]}],[\"$\",\"span\",\"60\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"61\",{\"token\":{\"content\":\"log\",\"offset\":61,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":395,\"length\":3,\"target\":\"log\",\"line\":8,\"character\":61}]}],[\"$\",\"span\",\"64\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"65\",{\"token\":{\"content\":\"payload\",\"offset\":65,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: {\\n    [key: string]: any;\\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`;\\n    event: string;\\n}\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":399,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":65}]}],[\"$\",\"span\",\"72\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"subscribe\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.subscribe(callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) =\u003e void, timeout?: number): RealtimeChannel\",\"docs\":\"Subscribe registers your client with the server\",\"tags\":\"$undefined\",\"start\":412,\"length\":9,\"target\":\"subscribe\",\"line\":9,\"character\":3}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"const gameId = 'id'\\nawait supabase.realtime.setAuth() // Needed for Realtime Authorization\\nconst changes = supabase\\n  .channel(`topic:${gameId}`, {\\n    config: { private: true },\\n  })\\n  .on('broadcast', { event: 'INSERT' }, (payload) =\u003e console.log(payload))\\n  .on('broadcast', { event: 'UPDATE' }, (payload) =\u003e console.log(payload))\\n  .on('broadcast', { event: 'DELETE' }, (payload) =\u003e console.log(payload))\\n  .subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"30:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}],[\"$\",\"div\",\"11\",{\"className\":\"w-full\",\"children\":12}],[\"$\",\"div\",\"12\",{\"className\":\"w-full\",\"children\":13}],[\"$\",\"div\",\"13\",{\"className\":\"w-full\",\"children\":14}],[\"$\",\"div\",\"14\",{\"className\":\"w-full\",\"children\":15}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"begin\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- remove the supabase_realtime publication\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"drop\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  publication \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"if\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"17\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"exists\"}],[\"$\",\"span\",\"23\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" supabase_realtime;\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- re-create the supabase_realtime publication with no tables\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"create\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" publication supabase_realtime;\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"commit\"}],[\"$\",\"span\",\"6\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\";\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[]}],[\"$\",\"span\",\"11\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- add a table called 'messages' to the publication\"}]]}],[\"$\",\"span\",\"12\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"-- (update this to match your tables)\"}]]}],[\"$\",\"span\",\"13\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"alter\"}]]}],[\"$\",\"span\",\"14\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  publication supabase_realtime \"}],[\"$\",\"span\",\"32\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"add\"}],[\"$\",\"span\",\"35\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"table\"}],[\"$\",\"span\",\"41\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" messages;\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"begin;\\n\\n-- remove the supabase_realtime publication\\ndrop\\n  publication if exists supabase_realtime;\\n\\n-- re-create the supabase_realtime publication with no tables\\ncreate publication supabase_realtime;\\n\\ncommit;\\n\\n-- add a table called 'messages' to the publication\\n-- (update this to match your tables)\\nalter\\n  publication supabase_realtime add table messages;\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"38:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"31:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"channel\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const channel: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":7,\"target\":\"channel\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"16\",{\"token\":{\"content\":\"supabase\",\"offset\":16,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":16,\"length\":8,\"target\":\"supabase\",\"line\":0,\"character\":16}]}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"channel\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":28,\"length\":7,\"target\":\"channel\",\"line\":1,\"character\":3}]}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"schema-db-changes\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on\u003c{\\n    [key: string]: any;\\n}\u003e(type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`, filter: RealtimePostgresChangesFilter\u003c`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`\u003e, callback: (payload: RealtimePostgresInsertPayload\u003c...\u003e) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"Creates an event handler that listens to changes.\",\"tags\":\"$undefined\",\"start\":60,\"length\":2,\"target\":\"on\",\"line\":2,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"postgres_changes\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"event\",\"offset\":6,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: \\\"INSERT\\\"\",\"docs\":\"The type of database change to listen to.\",\"tags\":\"$undefined\",\"start\":100,\"length\":5,\"target\":\"event\",\"line\":5,\"character\":6}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"INSERT\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"schema\",\"offset\":6,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) schema: string\",\"docs\":\"The database schema to listen to.\",\"tags\":\"$undefined\",\"start\":123,\"length\":6,\"target\":\"schema\",\"line\":6,\"character\":6}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"public\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"5\",{\"token\":{\"content\":\"payload\",\"offset\":5,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: RealtimePostgresInsertPayload\u003c{\\n    [key: string]: any;\\n}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":153,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":5}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"17\",{\"token\":{\"content\":\"console\",\"offset\":17,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$38\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":165,\"length\":7,\"target\":\"console\",\"line\":8,\"character\":17}]}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"25\",{\"token\":{\"content\":\"log\",\"offset\":25,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":173,\"length\":3,\"target\":\"log\",\"line\":8,\"character\":25}]}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"29\",{\"token\":{\"content\":\"payload\",\"offset\":29,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: RealtimePostgresInsertPayload\u003c{\\n    [key: string]: any;\\n}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":177,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":29}]}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"subscribe\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.subscribe(callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) =\u003e void, timeout?: number): RealtimeChannel\",\"docs\":\"Subscribe registers your client with the server\",\"tags\":\"$undefined\",\"start\":193,\"length\":9,\"target\":\"subscribe\",\"line\":10,\"character\":3}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"const channel = supabase\\n  .channel('schema-db-changes')\\n  .on(\\n    'postgres_changes',\\n    {\\n      event: 'INSERT',\\n      schema: 'public',\\n    },\\n    (payload) =\u003e console.log(payload)\\n  )\\n  .subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script><script>self.__next_f.push([1,"39:T8d7,"])</script><script>self.__next_f.push([1,"The `console` module provides a simple debugging console that is similar to the\nJavaScript console mechanism provided by web browsers.\n\nThe module exports two specific components:\n\n* A `Console` class with methods such as `console.log()`, `console.error()` and `console.warn()` that can be used to write to any Node.js stream.\n* A global `console` instance configured to write to [`process.stdout`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstdout) and\n[`process.stderr`](https://nodejs.org/docs/latest-v22.x/api/process.html#processstderr). The global `console` can be used without importing the `node:console` module.\n\n_**Warning**_: The global console object's methods are neither consistently\nsynchronous like the browser APIs they resemble, nor are they consistently\nasynchronous like all other Node.js streams. See the [`note on process I/O`](https://nodejs.org/docs/latest-v22.x/api/process.html#a-note-on-process-io) for\nmore information.\n\nExample using the global `console`:\n\n```js\nconsole.log('hello world');\n// Prints: hello world, to stdout\nconsole.log('hello %s', 'world');\n// Prints: hello world, to stdout\nconsole.error(new Error('Whoops, something bad happened'));\n// Prints error message and stack trace to stderr:\n//   Error: Whoops, something bad happened\n//     at [eval]:5:15\n//     at Script.runInThisContext (node:vm:132:18)\n//     at Object.runInThisContext (node:vm:309:38)\n//     at node:internal/process/execution:77:19\n//     at [eval]-wrapper:6:22\n//     at evalScript (node:internal/process/execution:76:60)\n//     at node:internal/main/eval_string:23:3\n\nconst name = 'Will Robinson';\nconsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to stderr\n```\n\nExample using the `Console` class:\n\n```js\nconst out = getStreamSomehow();\nconst err = getStreamSomehow();\nconst myConsole = new console.Console(out, err);\n\nmyConsole.log('hello world');\n// Prints: hello world, to out\nmyConsole.log('hello %s', 'world');\n// Prints: hello world, to out\nmyConsole.error(new Error('Whoops, something bad happened'));\n// Prints: [Error: Whoops, something bad happened], to err\n\nconst name = 'Will Robinson';\nmyConsole.warn(`Danger ${name}! Danger!`);\n// Prints: Danger Will Robinson! Danger!, to err\n```"])</script><script>self.__next_f.push([1,"32:[\"$\",\"div\",null,{\"className\":\"shiki group relative not-prose w-full overflow-hidden border border-default rounded-lg bg-200 text-sm\",\"children\":[[\"$\",\"pre\",null,{\"children\":[\"$\",\"code\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex-shrink-0 select-none text-right text-muted bg-control py-6 px-2\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"w-full\",\"children\":1}],[\"$\",\"div\",\"1\",{\"className\":\"w-full\",\"children\":2}],[\"$\",\"div\",\"2\",{\"className\":\"w-full\",\"children\":3}],[\"$\",\"div\",\"3\",{\"className\":\"w-full\",\"children\":4}],[\"$\",\"div\",\"4\",{\"className\":\"w-full\",\"children\":5}],[\"$\",\"div\",\"5\",{\"className\":\"w-full\",\"children\":6}],[\"$\",\"div\",\"6\",{\"className\":\"w-full\",\"children\":7}],[\"$\",\"div\",\"7\",{\"className\":\"w-full\",\"children\":8}],[\"$\",\"div\",\"8\",{\"className\":\"w-full\",\"children\":9}],[\"$\",\"div\",\"9\",{\"className\":\"w-full\",\"children\":10}],[\"$\",\"div\",\"10\",{\"className\":\"w-full\",\"children\":11}]]}],[\"$\",\"div\",null,{\"className\":\"p-6 overflow-x-auto flex-grow\",\"children\":[[\"$\",\"span\",\"0\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"channel\",\"offset\":6,\"color\":\"var(--code-token-constant)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const channel: RealtimeChannel\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":6,\"length\":7,\"target\":\"channel\",\"line\":0,\"character\":6}]}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-variable)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"16\",{\"token\":{\"content\":\"supabase\",\"offset\":16,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"const supabase: SupabaseClient\u003cany, \\\"public\\\", any\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":16,\"length\":8,\"target\":\"supabase\",\"line\":0,\"character\":16}]}]]}],[\"$\",\"span\",\"1\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"channel\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) SupabaseClient\u003cany, \\\"public\\\", any\u003e.channel(name: string, opts?: RealtimeChannelOptions): RealtimeChannel\",\"docs\":\"Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\",\"tags\":[[\"param\",\"name - The name of the Realtime channel.\"],[\"param\",\"opts - The options to pass to the Realtime channel.\"]],\"start\":28,\"length\":7,\"target\":\"channel\",\"line\":1,\"character\":3}]}],[\"$\",\"span\",\"10\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"schema-db-changes\"}],[\"$\",\"span\",\"29\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"30\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"2\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"on\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.on\u003c{\\n    [key: string]: any;\\n}\u003e(type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`, filter: RealtimePostgresChangesFilter\u003c`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`\u003e, callback: (payload: RealtimePostgresUpdatePayload\u003c...\u003e) =\u003e void): RealtimeChannel (+9 overloads)\",\"docs\":\"Creates an event handler that listens to changes.\",\"tags\":\"$undefined\",\"start\":60,\"length\":2,\"target\":\"on\",\"line\":2,\"character\":3}]}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],[\"$\",\"span\",\"3\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"5\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"postgres_changes\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"4\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],[\"$\",\"span\",\"5\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"event\",\"offset\":6,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) event: \\\"UPDATE\\\"\",\"docs\":\"The type of database change to listen to.\",\"tags\":\"$undefined\",\"start\":100,\"length\":5,\"target\":\"event\",\"line\":5,\"character\":6}]}],[\"$\",\"span\",\"11\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"UPDATE\"}],[\"$\",\"span\",\"20\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"6\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      \"}],[\"$\",\"$L34\",\"6\",{\"token\":{\"content\":\"schema\",\"offset\":6,\"color\":\"var(--code-token-property)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(property) schema: string\",\"docs\":\"The database schema to listen to.\",\"tags\":\"$undefined\",\"start\":123,\"length\":6,\"target\":\"schema\",\"line\":6,\"character\":6}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"15\",{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"public\"}],[\"$\",\"span\",\"21\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",\"22\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],[\"$\",\"span\",\"7\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"},\"}]]}],[\"$\",\"span\",\"8\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    \"}],[\"$\",\"span\",\"4\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"5\",{\"token\":{\"content\":\"payload\",\"offset\":5,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: RealtimePostgresUpdatePayload\u003c{\\n    [key: string]: any;\\n}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":153,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":5}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",\"13\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"span\",\"14\",{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\u003e\"}],[\"$\",\"span\",\"16\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" \"}],[\"$\",\"$L34\",\"17\",{\"token\":{\"content\":\"console\",\"offset\":17,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"namespace console\\nvar console: Console\",\"docs\":\"$39\",\"tags\":[[\"category\",\"I/O\"],[\"see\",\"[source](https://github.com/nodejs/node/blob/v22.x/lib/console.js)\"]],\"start\":165,\"length\":7,\"target\":\"console\",\"line\":8,\"character\":17}]}],[\"$\",\"span\",\"24\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"25\",{\"token\":{\"content\":\"log\",\"offset\":25,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) Console.log(message?: any, ...optionalParams: any[]): void (+1 overload)\",\"docs\":\"Prints to `stdout` with newline. Multiple arguments can be passed, with the\\nfirst used as the primary message and all additional used as substitution\\nvalues similar to [`printf(3)`](http://man7.org/linux/man-pages/man3/printf.3.html)\\n(the arguments are all passed to [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args)).\\n\\n```js\\nconst count = 5;\\nconsole.log('count: %d', count);\\n// Prints: count: 5, to stdout\\nconsole.log('count:', count);\\n// Prints: count: 5, to stdout\\n```\\n\\nSee [`util.format()`](https://nodejs.org/docs/latest-v22.x/api/util.html#utilformatformat-args) for more information.\",\"tags\":[[\"since\",\"v0.1.100\"]],\"start\":173,\"length\":3,\"target\":\"log\",\"line\":8,\"character\":25}]}],[\"$\",\"span\",\"28\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"$L34\",\"29\",{\"token\":{\"content\":\"payload\",\"offset\":29,\"color\":\"var(--code-token-parameter)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(parameter) payload: RealtimePostgresUpdatePayload\u003c{\\n    [key: string]: any;\\n}\u003e\",\"docs\":\"$undefined\",\"tags\":\"$undefined\",\"start\":177,\"length\":7,\"target\":\"payload\",\"line\":8,\"character\":29}]}],[\"$\",\"span\",\"36\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],[\"$\",\"span\",\"9\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]]}],[\"$\",\"span\",\"10\",{\"className\":\"block h-5\",\"children\":[[\"$\",\"span\",\"0\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  \"}],[\"$\",\"span\",\"2\",{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"$L34\",\"3\",{\"token\":{\"content\":\"subscribe\",\"offset\":3,\"color\":\"var(--code-token-function)\",\"fontStyle\":0},\"annotations\":[{\"type\":\"hover\",\"text\":\"(method) RealtimeChannel.subscribe(callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) =\u003e void, timeout?: number): RealtimeChannel\",\"docs\":\"Subscribe registers your client with the server\",\"tags\":\"$undefined\",\"start\":193,\"length\":9,\"target\":\"subscribe\",\"line\":10,\"character\":3}]}],[\"$\",\"span\",\"12\",{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}]]}]]}]}],[\"$\",\"$L33\",null,{\"content\":\"const channel = supabase\\n  .channel('schema-db-changes')\\n  .on(\\n    'postgres_changes',\\n    {\\n      event: 'UPDATE',\\n      schema: 'public',\\n    },\\n    (payload) =\u003e console.log(payload)\\n  )\\n  .subscribe()\",\"className\":\"hidden group-hover:block absolute top-2 right-2\"}]]}]\n"])</script></body></html>