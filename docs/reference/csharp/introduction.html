<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>C#: Introduction | Supabase Docs</title><meta name="description" content="Supabase API reference for C#: Introduction"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="introduction.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/containedby">Contained by value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/storage-from-getpublicurl">Retrieve public URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp">Misc</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/csharp/release-notes">Release Notes</a></li></ul></nav><h1>C#: Introduction</h1><p>This reference documents every object and method available in Supabase's C# library, <a href="https://www.nuget.org/packages/supabase">supabase</a>. You can use <code>Supabase</code> to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.</p>
<div><p>The C# client library is created and maintained by the Supabase community, and is not an official library. Please be tolerant of areas where the library is still being developed, and — as with all the libraries — feel free to contribute wherever you find issues.</p><p>Huge thanks to official maintainer, <a href="https://github.com/acupofjose">Joseph Schultz</a>. As well as <a href="https://github.com/wiverson">Will Iverson</a>, <a href="https://github.com/veleek">Ben Randall</a>, and <a href="https://github.com/rhuanbarros">Rhuan Barros</a> for their help.</p></div></body></html>