<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Swift: Introduction | Supabase Docs</title><meta name="description" content="Supabase API reference for <PERSON>: Introduction"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="introduction.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/db-modifiers-select">Return data after inserting</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-exchangecodeforsession">Exchange an auth code for a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/auth-admin-mfa-listfactors">List all factors for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-copy">Copy an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-createsignedurls">Create signed URLs</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/swift/storage-from-getpublicurl">Retrieve public URL</a></li></ul></nav><h1>Swift: Introduction</h1><p>This reference documents every object and method available in Supabase's Swift library, <a href="https://github.com/supabase/supabase-swift">supabase-swift</a>. You can use supabase-swift to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.</p></body></html>