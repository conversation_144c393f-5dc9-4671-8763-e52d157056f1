<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>JavaScript: Update a user | Supabase Docs</title><meta name="description" content="Supabase API reference for JavaScript: Update a user"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="auth-admin-updateuserbyid.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/typescript-support">TypeScript support</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/containedby">Contained by value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangegt">Greater than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangegte">Greater than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangelt">Less than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangelte">Less than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangeadjacent">Mutually exclusive to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/overlaps">With a common element</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-modifiers-select">Return data after inserting</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-abortsignal">Set an abort signal</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/maybesingle">Retrieve zero or one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-returns">Override type of successful response</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-overrideTypes">Partially override or replace type of successful response</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getclaims">Get user claims from verified JWT</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-resetpasswordforemail">Send a password reset request</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-exchangecodeforsession">Exchange an auth code for a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-startautorefresh">Start auto-refresh session (non-browser)</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-stopautorefresh">Stop auto-refresh session (non-browser)</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-getuserbyid">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-listusers">List all users</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-createuser">Create a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-inviteuserbyemail">Send an email invite link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-generatelink">Generate an email link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-updateuserbyid">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-mfa-deletefactor">Delete a factor for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/broadcastmessage">Broadcast a message</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-copy">Copy an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsignedurls">Create signed URLs</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsigneduploadurl">Create signed upload URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-uploadtosignedurl">Upload to a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-getpublicurl">Retrieve public URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Misc</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/release-notes">Release Notes</a></li></ul></nav><h1>JavaScript: Update a user</h1><p>Updates the user data.</p><h2 id="parameters">Parameters</h2><ul><li><h3>uid</h3><span>(Required)</span><p></p></li><li><h3>attributes</h3><span>(Required)</span><p>The data you want to update.

This function should only be called on a server. Never expose your `service_role` key in the browser.
</p></li></ul><h2 id="examples">Examples</h2><h3>Updates a user's email</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '11111111-1111-1111-1111-111111111111',
  { email: '<EMAIL>' }
)
</code></pre><h3>Updates a user's password</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
  { password: 'new_password' }
)
</code></pre><h3>Updates a user's metadata</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
  { user_metadata: { hello: 'world' } }
)
</code></pre><h3>Updates a user's app_metadata</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
  { app_metadata: { plan: 'trial' } }
)
</code></pre><h3>Confirms a user's email address</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
  { email_confirm: true }
)
</code></pre><h3>Confirms a user's phone number</h3><pre><code class="language-js">const { data: user, error } = await supabase.auth.admin.updateUserById(
  '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
  { phone_confirm: true }
)
</code></pre></body></html>