<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>JavaScript: Fetch data | Supabase Docs</title><meta name="description" content="Supabase API reference for JavaScript: Fetch data"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="select.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/typescript-support">TypeScript support</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/containedby">Contained by value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangegt">Greater than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangegte">Greater than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangelt">Less than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangelte">Less than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/rangeadjacent">Mutually exclusive to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/overlaps">With a common element</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-modifiers-select">Return data after inserting</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-abortsignal">Set an abort signal</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/maybesingle">Retrieve zero or one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-returns">Override type of successful response</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/db-overrideTypes">Partially override or replace type of successful response</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getclaims">Get user claims from verified JWT</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-resetpasswordforemail">Send a password reset request</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-exchangecodeforsession">Exchange an auth code for a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-startautorefresh">Start auto-refresh session (non-browser)</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-stopautorefresh">Stop auto-refresh session (non-browser)</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-getuserbyid">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-listusers">List all users</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-createuser">Create a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-inviteuserbyemail">Send an email invite link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-generatelink">Generate an email link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-updateuserbyid">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/auth-admin-mfa-deletefactor">Delete a factor for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/broadcastmessage">Broadcast a message</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-copy">Copy an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsignedurls">Create signed URLs</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-createsigneduploadurl">Create signed upload URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-uploadtosignedurl">Upload to a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/storage-from-getpublicurl">Retrieve public URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript">Misc</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/javascript/release-notes">Release Notes</a></li></ul></nav><h1>JavaScript: Fetch data</h1><p>Perform a SELECT query on the table or view.</p><ul>
<li>By default, Supabase projects return a maximum of 1,000 rows. This setting can be changed in your project's <a href="https://supabase.com/dashboard/project/_/settings/api">API settings</a>. It's recommended that you keep it low to limit the payload size of accidental or malicious requests. You can use <code>range()</code> queries to paginate through your data.</li>
<li><code>select()</code> can be combined with <a href="https://supabase.com/docs/reference/javascript/using-filters">Filters</a></li>
<li><code>select()</code> can be combined with <a href="https://supabase.com/docs/reference/javascript/using-modifiers">Modifiers</a></li>
<li><code>apikey</code> is a reserved keyword if you're using the <a href="../../guides/platform.html">Supabase Platform</a> and <a href="https://github.com/supabase/supabase/issues/5465">should be avoided as a column name</a>.</li>
</ul><h2 id="parameters">Parameters</h2><ul><li><h3>columns</h3><span>(Optional)</span><p>The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`
</p></li><li><h3>options</h3><span>(Required)</span><p>Named parameters
</p></li></ul><h2 id="examples">Examples</h2><h3>Getting your data</h3><pre><code class="language-js">const { data, error } = await supabase
  .from('characters')
  .select()
</code></pre><h3>Selecting specific columns</h3><pre><code class="language-js">const { data, error } = await supabase
  .from('characters')
  .select('name')
</code></pre><h3>Query referenced tables</h3><pre><code class="language-js">const { data, error } = await supabase
  .from('orchestal_sections')
  .select(`
    name,
    instruments (
      name
    )
  `)
</code></pre><h3>Query referenced tables with spaces in their names</h3><pre><code class="language-js">const { data, error } = await supabase
  .from('orchestal sections')
  .select(`
    name,
    "musical instruments" (
      name
    )
  `)
</code></pre><h3>Query referenced tables through a join table</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('users')
  .select(`
    name,
    teams (
      name
    )
  `)
</code></pre><h3>Query the same referenced table multiple times</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('messages')
  .select(`
    content,
    from:sender_id(name),
    to:receiver_id(name)
  `)

// To infer types, use the name of the table (in this case `users`) and
// the name of the foreign key constraint.
const { data, error } = await supabase
  .from('messages')
  .select(`
    content,
    from:users!messages_sender_id_fkey(name),
    to:users!messages_receiver_id_fkey(name)
  `)
</code></pre><h3>Query nested foreign tables through a join table</h3><pre><code class="language-ts">  const { data, error } = await supabase
    .from('games')
    .select(`
      game_id:id,
      away_team:teams!games_away_team_fkey (
        users (
          id,
          name
        )
      )
    `)
</code></pre><h3>Filtering through referenced tables</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('instruments')
  .select('name, orchestral_sections(*)')
  .eq('orchestral_sections.name', 'percussion')
</code></pre><h3>Querying referenced table with count</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('orchestral_sections')
  .select(`*, instruments(count)`)
</code></pre><h3>Querying with count option</h3><pre><code class="language-ts">const { count, error } = await supabase
  .from('characters')
  .select('*', { count: 'exact', head: true })
</code></pre><h3>Querying JSON data</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('users')
  .select(`
    id, name,
    address->city
  `)
</code></pre><h3>Querying referenced table with inner join</h3><pre><code class="language-ts">const { data, error } = await supabase
  .from('instruments')
  .select('name, orchestral_sections!inner(name)')
  .eq('orchestral_sections.name', 'woodwinds')
  .limit(1)
</code></pre><h3>Switching schemas per query</h3><pre><code class="language-ts">const { data, error } = await supabase
  .schema('myschema')
  .from('mytable')
  .select()
</code></pre></body></html>