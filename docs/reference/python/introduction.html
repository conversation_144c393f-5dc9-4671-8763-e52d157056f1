<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Python: Introduction | Supabase Docs</title><meta name="description" content="Supabase API reference for Python: Introduction"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="introduction.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/python/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/containedby">Contained by value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rangegt">Greater than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rangegte">Greater than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rangelt">Less than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rangelte">Less than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/rangeadjacent">Mutually exclusive to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/overlaps">With a common element</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/maybesingle">Retrieve zero or one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-resetpasswordforemail">Send a password reset request</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-exchangecodeforsession">Exchange an auth code for a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-getuserbyid">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-listusers">List all users</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-createuser">Create a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-inviteuserbyemail">Send an email invite link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-generatelink">Generate an email link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-updateuserbyid">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/auth-admin-mfa-deletefactor">Delete a factor for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/realtime-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/broadcastmessage">Broadcast a message</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-copy">Copy an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-createsignedurls">Create signed URLs</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-createsigneduploadurl">Create signed upload URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-uploadtosignedurl">Upload to a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/python/storage-from-getpublicurl">Retrieve public URL</a></li></ul></nav><h1>Python: Introduction</h1><p>This reference documents every object and method available in Supabase's Python library, <a href="https://github.com/supabase/supabase-py">supabase-py</a>. You can use supabase-py to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.</p></body></html>