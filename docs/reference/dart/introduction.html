<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Flutter: Introduction | Supabase Docs</title><meta name="description" content="Supabase API reference for Flutter: Introduction"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="introduction.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/upgrade-guide">Upgrade guide</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/containedby">Contained by value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rangegt">Greater than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rangegte">Greater than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rangelt">Less than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rangelte">Less than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/rangeadjacent">Mutually exclusive to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/overlaps">With a common element</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/match">Match an associated value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/db-modifiers-select">Return data after inserting</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/maybesingle">Retrieve zero or one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-getuserbyid">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-listusers">List all users</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-createuser">Create a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-inviteuserbyemail">Send an email invite link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-generatelink">Generate an email link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/auth-admin-updateuserbyid">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/stream">Listen to database changes</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/dart/storage-from-getpublicurl">Retrieve public URL</a></li></ul></nav><h1>Flutter: Introduction</h1><p>This reference documents every object and method available in Supabase's Flutter library, <a href="https://pub.dev/packages/supabase_flutter">supabase-flutter</a>. You can use supabase-flutter to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.</p>
<p>We also provide a <a href="https://pub.dev/packages/supabase">supabase</a> package for non-Flutter projects.</p></body></html>