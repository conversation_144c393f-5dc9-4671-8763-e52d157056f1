<!doctype html><html><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Kotlin: Introduction | Supabase Docs</title><meta name="description" content="Supabase API reference for Kotlin: Introduction"><meta name="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"><link rel="canonical" href="introduction.html"></head><body><nav><ul><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/introduction">Introduction</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/installing">Installing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/initializing">Initializing</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin">Database</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/select">Fetch data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/insert">Insert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/update">Update data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/upsert">Upsert data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/delete">Delete data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rpc">Call a Postgres function</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/using-filters">Using filters</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/eq">Column is equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/neq">Column is not equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/gt">Column is greater than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/gte">Column is greater than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/lt">Column is less than a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/lte">Column is less than or equal to a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/like">Column matches a pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/ilike">Column matches a case-insensitive pattern</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/is">Column is a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/in">Column is in an array</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/contains">Column contains every element in a value</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rangegt">Greater than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rangegte">Greater than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rangelt">Less than a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rangelte">Less than or equal to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/rangeadjacent">Mutually exclusive to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/overlaps">With a common element</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/textsearch">Match a string</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/not">Don't match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/or">Match at least one filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/filter">Match the filter</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/using-modifiers">Using modifiers</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/db-modifiers-select">Return data after inserting</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/order">Order the results</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/limit">Limit the number of rows returned</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/range">Limit the query to a range</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/single">Retrieve one row of data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/db-csv">Retrieve as a CSV</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/explain">Using explain</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin">Auth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-api">Overview</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signup">Create a new user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-onauthstatechange">Listen to auth events</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinanonymously">Create an anonymous user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinwithpassword">Sign in a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinwithidtoken">Sign in with ID Token</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinwithotp">Sign in a user through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinwithoauth">Sign in a user through OAuth</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signinwithsso">Sign in a user through SSO</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-signout">Sign out a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-resetpasswordforemail">Send a password reset request</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-verifyotp">Verify and log in through OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-getsession">Retrieve a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-refreshsession">Retrieve a new session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-getuser">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-updateuser">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-getuseridentities">Retrieve identities linked to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-linkidentity">Link an identity to a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-unlinkidentity">Unlink an identity from a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-reauthentication">Send a password reauthentication nonce</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-resend">Resend an OTP</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-setsession">Set the session data</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-exchangecodeforsession">Exchange an auth code for a session</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-api">Auth MFA</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-enroll">Enroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-challenge">Create a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-verify">Verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-challengeandverify">Create and verify a challenge</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-unenroll">Unenroll a factor</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-mfa-getauthenticatorassurancelevel">Get Authenticator Assurance Level</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/admin-api">Auth Admin</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-getuserbyid">Retrieve a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-listusers">List all users</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-createuser">Create a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-deleteuser">Delete a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-inviteuserbyemail">Send an email invite link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-generatelink">Generate an email link</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-updateuserbyid">Update a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-mfa-listfactors">List all factors for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/auth-admin-mfa-deletefactor">Delete a factor for a user</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin">Edge Functions</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/functions-invoke">Invokes a Supabase Edge Function.</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin">Realtime</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/stream">Listen to database changes</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/subscribe">Subscribe to channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/removechannel">Unsubscribe from a channel</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/removeallchannels">Unsubscribe from all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/getchannels">Retrieve all channels</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/broadcastmessage">Broadcast a message</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin">Storage</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-createbucket">Create a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-getbucket">Retrieve a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-listbuckets">List all buckets</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-updatebucket">Update a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-deletebucket">Delete a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-emptybucket">Empty a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-upload">Upload a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-download">Download a file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-list">List all files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-update">Replace an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-move">Move an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-copy">Copy an existing file</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-remove">Delete files in a bucket</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-createsignedurl">Create a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-createsignedurls">Create signed URLs</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-createsigneduploadurl">Create signed upload URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-uploadtosignedurl">Upload to a signed URL</a></li><li><a href="https://docs-supabase.vercel.app/docs/reference/kotlin/storage-from-getpublicurl">Retrieve public URL</a></li></ul></nav><h1>Kotlin: Introduction</h1><p>This reference documents every object and method available in Supabase's Kotlin Multiplatform library, <a href="https://github.com/supabase-community/supabase-kt">supabase-kt</a>. You can use supabase-kt to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.</p>
<p>To see supported Kotlin targets, check the corresponding module README on <a href="https://github.com/supabase-community/supabase-kt">GitHub</a>.</p>
<p>To migrate from version 2.X to 3.0.0, see the <a href="https://github.com/supabase-community/supabase-kt/blob/master/MIGRATION.md">migration guide</a></p>
<div><p>The Kotlin client library is created and maintained by the Supabase community, and is not an official library. Please be tolerant of areas where the library is still being developed, and — as with all the libraries — feel free to contribute wherever you find issues.</p><p>Huge thanks to official maintainer, <a href="https://github.com/jan-tennert">jan-tennert</a>.</p></div></body></html>