<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="rss.xml" data-next-head=""/><link rel="manifest" href="favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/og/supabase-og.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Features</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="From authentication to storage, everything you need to build and ship your next project." data-next-head=""/><meta property="og:title" content="Supabase Features" data-next-head=""/><meta property="og:description" content="From authentication to storage, everything you need to build and ship your next project." data-next-head=""/><meta property="og:url" content="/customers" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3094-eacc33a29df56925.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7375-e996a67eb3cff82d.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/features-f1d35fea784a0e29.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 sm:!px-0"><div class="border border-muted rounded-xl bg-alternative my-4 px-6 py-8 md:py-10 lg:px-16 lg:py-20 xl:px-20 bg-center bg-cover bg-[url(&#x27;/images/features/features-cover-light.svg&#x27;)] dark:bg-[url(&#x27;/images/features/features-cover-dark.svg&#x27;)]"><div class="mx-auto sm:max-w-xl text-center flex flex-col items-center gap-3" style="opacity:0;transform:translateY(10px)"><h1 class="h1 text-foreground !m-0">Supabase Features</h1><p class="text-foreground-light text-base">Everything you need <br class="md:hidden"/> to build and ship your next project.</p></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 relative grid md:grid-cols-4 md:gap-4 !pt-0"><div class="relative w-full h-full"><div class="mb-4 flex flex-col gap-4 sticky top-20"><div class="text-sm leading-4 grid gap-2 md:grid md:grid-cols-12 w-full [&amp;_input]:text-base [&amp;_input]:md:text-sm [&amp;_input]:!leading-4"><div class="col-span-12"><div class=""><div class="relative"><input data-size="small" autoComplete="off" id="" name="" placeholder="Search features" type="search" class="peer/input block box-border w-full rounded-md shadow-sm transition-all text-foreground focus-visible:shadow-md outline-none focus:ring-current focus:ring-2 focus-visible:border-foreground-muted focus-visible:ring-background-control placeholder-foreground-muted group bg-foreground/[.026] border border-control text-sm leading-4 px-3 py-2 pl-10" value=""/><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-foreground-light [&amp;_svg]:stroke-[1.5] [&amp;_svg]:h-[18px] [&amp;_svg]:w-[18px]"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></div></div></div><p data-state="hide" class="
        text-red-900
        transition-all
        data-show:mt-2
        data-show:animate-slide-down-normal
        data-hide:animate-slide-up-normal
       text-sm leading-4"></p></div></div><div class="hidden md:flex flex-col gap-4"><h2 class="text-sm text-foreground-lighter">Filter by tags:</h2><div class="flex flex-col gap-2.5"><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="authentication" name="authentication" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="authentication"/><label class="text-foreground-light cursor-pointer text-sm" for="authentication"><span></span></label></div><label for="authentication" class="text-sm !leading-none capitalize flex-1 text-left">authentication</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="database" name="database" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="database"/><label class="text-foreground-light cursor-pointer text-sm" for="database"><span></span></label></div><label for="database" class="text-sm !leading-none capitalize flex-1 text-left">database</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="functions" name="functions" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="functions"/><label class="text-foreground-light cursor-pointer text-sm" for="functions"><span></span></label></div><label for="functions" class="text-sm !leading-none capitalize flex-1 text-left">functions</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="platform" name="platform" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="platform"/><label class="text-foreground-light cursor-pointer text-sm" for="platform"><span></span></label></div><label for="platform" class="text-sm !leading-none capitalize flex-1 text-left">platform</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="realtime" name="realtime" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="realtime"/><label class="text-foreground-light cursor-pointer text-sm" for="realtime"><span></span></label></div><label for="realtime" class="text-sm !leading-none capitalize flex-1 text-left">realtime</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="storage" name="storage" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="storage"/><label class="text-foreground-light cursor-pointer text-sm" for="storage"><span></span></label></div><label for="storage" class="text-sm !leading-none capitalize flex-1 text-left">storage</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="studio" name="studio" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="studio"/><label class="text-foreground-light cursor-pointer text-sm" for="studio"><span></span></label></div><label for="studio" class="text-sm !leading-none capitalize flex-1 text-left">studio</label></div><div class="flex items-center gap-2 text-foreground-light hover:text-foreground !cursor-pointer hover:!cursor-pointer transition-colors"><div class="flex cursor-pointer leading-none [&amp;_input]:m-0"><input id="vector" name="vector" type="checkbox" class=" bg-transparent outline-none focus:ring-current focus:ring-2 focus:ring-border-muted text-brand border-strong shadow-sm rounded cursor-pointer  h-4 w-4 mt-0.5 mr-3.5" value="vector"/><label class="text-foreground-light cursor-pointer text-sm" for="vector"><span></span></label></div><label for="vector" class="text-sm !leading-none capitalize flex-1 text-left">vector</label></div></div><div class="text-foreground-muted text-xs">Features selected: <!-- -->62</div></div><button data-size="tiny" type="button" tabindex="-1" class="relative cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 text-foreground border border-dashed border-strong hover:border-stronger bg-transparent focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong w-full items-center justify-center text-xs px-2.5 py-1 h-[26px] opacity-0 transition-opacity hidden md:block"> <span class="truncate">Clear all filters</span> </button></div></div><div class="md:col-span-3 flex flex-col gap-4 md:gap-8"><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-4"><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/ai-integrations"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">AI Integrations</h3><p class="text-foreground-light text-sm line-clamp-2">Enhance applications with OpenAI and Hugging Face integrations.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/row-level-security"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Authorization via Row Level Security</h3><p class="text-foreground-light text-sm line-clamp-2">Control the data each user can access with Postgres Policies.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/auto-generated-graphql-api"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-braces w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M8 3H7a2 2 0 0 0-2 2v5a2 2 0 0 1-2 2 2 2 0 0 1 2 2v5c0 1.1.9 2 2 2h1"></path><path d="M16 21h1a2 2 0 0 0 2-2v-5c0-1.1.9-2 2-2a2 2 0 0 1-2-2V5a2 2 0 0 0-2-2h-1"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Auto-generated GraphQL API via pg_graphql</h3><p class="text-foreground-light text-sm line-clamp-2">Fast GraphQL APIs using our custom Postgres GraphQL extension.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/auto-generated-rest-api"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code2 w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m5 12-3 3 3 3"></path><path d="m9 18 3-3-3-3"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Auto-generated REST API via PostgREST</h3><p class="text-foreground-light text-sm line-clamp-2">RESTful APIs auto-generated from your database.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/branching"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-git-branch w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><line x1="6" x2="6" y1="3" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Branching</h3><p class="text-foreground-light text-sm line-clamp-2">Test and preview changes using Supabase Branches.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/cli"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">CLI</h3><p class="text-foreground-light text-sm line-clamp-2">Use our CLI to develop your project locally and deploy.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/auth-captcha-protection"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rectangle-ellipsis w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="20" height="12" x="2" y="6" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 12h.01"></path><path d="M7 12h.01"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Captcha protection</h3><p class="text-foreground-light text-sm line-clamp-2">Add Captcha to your sign-in, sign-up, and password reset forms.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/client-library-flutter"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg width="59" height="58" viewBox="0 0 59 58" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M42.7859 8.69144L36.306 2.21152C35.4475 1.34882 33.6544 0.316956 32.1405 0.316956C30.838 0.316956 29.5608 0.57915 28.732 1.07394L13.8787 8.50055H40.9419C41.5663 8.50055 42.184 8.56542 42.7859 8.69144Z" fill="currentColor"></path><path d="M9.28906 12.8012L1.46792 29.095C1.14229 29.7843 0.710938 30.9811 0.710938 31.7465C0.710938 33.4 1.43832 35.0916 2.60551 36.2926L9.30112 42.9383C9.2931 42.7955 9.28906 42.6522 9.28906 42.5084V12.8012Z" fill="currentColor"></path><path d="M16.6841 50.367L24.1985 57.8814H48.0582V50.4088H17.5245C17.2426 50.4088 16.9622 50.3947 16.6841 50.367Z" fill="currentColor"></path><path d="M50.1358 47.6558H58.2838V24.943L45.8917 12.5508C44.5789 11.238 42.7985 10.5005 40.9419 10.5005H12.7467L50.1358 47.6558Z" fill="currentColor"></path><path d="M11.2891 11.8716V42.5084C11.2891 44.0104 11.8857 45.4509 12.9478 46.513C14.1617 47.7268 15.8079 48.4088 17.5245 48.4088H48.0561L11.2891 11.8716Z" fill="currentColor"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Client Library - Flutter</h3><p class="text-foreground-light text-sm line-clamp-2">Integrate Supabase into your Flutter applications effortlessly.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/client-library-javascript"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg width="24" height="24" viewBox="0 0 55 55" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.730469 0.807739H54.2695V54.3468H0.730469V0.807739ZM41.6588 45.6917C39.1773 45.6917 37.7743 44.3974 36.6959 42.6365L32.6073 45.0118C34.0843 47.9301 37.1029 50.1566 41.7753 50.1566C46.5538 50.1566 50.1121 47.6752 50.1121 43.1456C50.1121 38.944 47.6986 37.0753 43.4239 35.2422L42.1662 34.7034C40.0076 33.7686 39.0728 33.1576 39.0728 31.6483C39.0728 30.4271 40.0068 29.4923 41.4804 29.4923C42.9251 29.4923 43.8556 30.1016 44.7182 31.6483L48.6359 29.1328C46.9788 26.2179 44.6791 25.1046 41.4804 25.1046C36.9873 25.1046 34.1124 27.977 34.1124 31.7503C34.1124 35.8464 36.5242 37.784 40.1546 39.3307L41.4124 39.8703C43.7069 40.874 45.0751 41.485 45.0751 43.2102C45.0751 44.6498 43.7435 45.6917 41.6588 45.6917ZM22.1613 45.6602C20.4327 45.6602 19.7138 44.4747 18.9234 43.0725L14.8281 45.5519C16.0145 48.0627 18.3473 50.1473 22.3754 50.1473C26.8336 50.1473 29.8879 47.7763 29.8879 42.5668V25.3919H24.8569V42.4989C24.8569 45.0135 23.8142 45.6602 22.1613 45.6602Z" fill="currentColor"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Client Library - JavaScript</h3><p class="text-foreground-light text-sm line-clamp-2">Easily integrate Supabase with your JavaScript applications.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/client-library-python"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg width="24" height="24" viewBox="0 0 56 57" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path fill-rule="evenodd" clip-rule="evenodd" d="M21.2861 1.17086C23.2052 0.83128 25.3862 0.636497 27.6701 0.625885C29.954 0.615273 32.3353 0.788831 34.6303 1.17086C38.2558 1.77512 41.3102 4.49488 41.3102 8.11546V20.8368C41.3102 24.5673 38.3448 27.6257 34.6303 27.6257H21.2861C16.7554 27.6257 12.9401 31.5154 12.9401 35.925V42.0287H8.3467C4.46398 42.0287 2.19606 39.2106 1.2464 35.2554C-0.034647 29.9419 0.0197613 26.7664 1.2464 21.6777C2.30982 17.2381 5.70965 14.9043 9.59237 14.9043H27.966V13.2071H14.6062V8.11546C14.6062 4.26017 15.6326 2.16964 21.2861 1.17086ZM22.9521 7.27463C22.9521 5.86635 21.8252 4.72101 20.4452 4.72101C19.0603 4.72101 17.9383 5.86635 17.9383 7.27463C17.9383 8.67792 19.0603 9.81268 20.4452 9.81268C21.8252 9.81268 22.9521 8.67792 22.9521 7.27463Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M42.9762 20.8371V14.9046H47.99C51.8777 14.9046 53.7103 17.8126 54.6699 21.6779C56.0053 27.0463 56.0647 31.0708 54.6699 35.2556C53.3196 39.3207 51.8727 42.029 47.99 42.029H27.9659V43.7262H41.3101V48.8178C41.3101 52.6731 37.9936 54.6329 34.6302 55.6067C29.5703 57.0749 25.515 56.8502 21.286 55.6067C17.7545 54.568 14.6061 52.4384 14.6061 48.8178V36.0965C14.6061 32.4359 17.6308 29.3076 21.286 29.3076H34.6302C39.0768 29.3076 42.9762 25.4364 42.9762 20.8371ZM37.9779 49.6587C37.9779 48.2554 36.856 47.1206 35.471 47.1206C34.0911 47.1206 32.9641 48.2554 32.9641 49.6587C32.9641 51.0669 34.0911 52.2123 35.471 52.2123C36.856 52.2123 37.9779 51.0669 37.9779 49.6587Z" fill="currentColor"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Client Library - Python</h3><p class="text-foreground-light text-sm line-clamp-2">Integrate Supabase easily into your Python applications.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/client-library-swift"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg width="24" height="24" viewBox="0 0 58 57" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path fill-rule="evenodd" clip-rule="evenodd" d="M57.3407 14.0476V42.6821C57.3407 43.9642 57.2338 45.1929 57.0201 46.4217C56.8064 47.6504 56.4325 48.8257 55.8448 49.9475C55.2572 51.016 54.5627 52.031 53.6545 52.9392C52.7997 53.8474 51.7847 54.5419 50.6628 55.1295C49.541 55.7172 48.3657 56.0912 47.1369 56.3048C45.9256 56.5155 44.6623 56.5704 43.4495 56.6231L43.3974 56.6254H14.7094C13.4273 56.6254 12.1986 56.5185 10.9698 56.3048C9.74112 56.0912 8.56582 55.7172 7.44394 55.1295C6.37549 54.5419 5.36046 53.8474 4.45228 52.9392C3.54409 52.0845 2.8496 51.0694 2.26195 49.9475C1.6743 48.8257 1.30034 47.6504 1.08665 46.4217C0.875966 45.2102 0.82107 43.9468 0.768367 42.7339L0.766113 42.6821V13.9941C0.819536 12.712 0.872962 11.4833 1.08665 10.2545C1.30034 9.02583 1.6743 7.85052 2.26195 6.72865C2.8496 5.6602 3.54409 4.64516 4.45228 3.73698C4.55912 3.63013 4.67932 3.52329 4.79952 3.41645C4.91972 3.30961 5.03992 3.20276 5.14677 3.09592C5.89468 2.50827 6.6426 1.97405 7.49737 1.54667C7.63092 1.49324 7.77784 1.42646 7.92475 1.35968C8.07166 1.2929 8.21857 1.22612 8.35212 1.1727C9.20689 0.852165 10.1151 0.585055 11.0233 0.424787C11.9062 0.268965 12.8397 0.214142 13.7255 0.162122C13.7508 0.160638 13.776 0.159156 13.8012 0.157674C14.0683 0.104251 14.3889 0.104248 14.7094 0.104248H43.3974C44.6795 0.104248 45.9082 0.211097 47.1369 0.424787C48.3657 0.638478 49.541 1.01243 50.6628 1.60007C51.7313 2.18772 52.7463 2.88222 53.6545 3.79041C54.5627 4.64517 55.2572 5.6602 55.8448 6.78208C56.4325 7.90395 56.8064 9.07925 57.0201 10.308C57.2308 11.5194 57.2857 12.7827 57.3384 13.9956L57.3407 14.0476ZM45.6946 34.4016L45.5343 35.0426C50.4492 41.026 49.1136 47.4367 48.3657 46.3148C45.8014 41.3999 41.1002 42.6287 38.6962 43.8574C38.5894 43.9108 38.4825 43.9776 38.3757 44.0444C38.2688 44.1111 38.162 44.1779 38.0551 44.2313C38.0551 44.2848 38.0017 44.2848 38.0017 44.2848C33.0334 46.9559 26.3022 47.1162 19.5709 44.2313C13.9081 41.7739 9.26034 37.6069 6.26868 32.7989C7.81793 33.9208 9.47404 34.9358 11.237 35.7371C18.3422 39.1028 25.5008 38.8357 30.576 35.7371C23.3639 30.1812 17.3272 22.9691 12.7328 17.0926C11.8781 16.0776 11.0767 14.9557 10.3822 13.8339C15.9382 18.8556 24.6461 25.2129 27.798 26.9758C21.1736 19.9775 15.2971 11.323 15.5642 11.5901C26.035 22.1144 35.7045 28.0977 35.7045 28.0977C36.0785 28.258 36.3456 28.4182 36.5593 28.5785C36.773 28.0443 36.9333 27.51 37.0935 26.9758C38.7496 20.8856 36.8798 13.8873 32.606 8.11765C42.3289 13.9407 48.0452 25.0526 45.6946 34.4016Z" fill="currentColor"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Client Library - Swift</h3><p class="text-foreground-light text-sm line-clamp-2">Effortlessly connect your Swift applications to Supabase.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/cdn"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Content Delivery Network</h3><p class="text-foreground-light text-sm line-clamp-2">Cache large files using the Supabase CDN.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/supabase-cron"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Cron</h3><p class="text-foreground-light text-sm line-clamp-2">Schedule recurring Jobs in Postgres.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/custom-domains"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Custom domains</h3><p class="text-foreground-light text-sm line-clamp-2">White-label the Supabase APIs for a branded experience.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/database-webhooks"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Database Webhooks</h3><p class="text-foreground-light text-sm line-clamp-2">Trigger external payloads on database events.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/database-backups"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-backup w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 12a9 3 0 0 0 5 2.69"></path><path d="M21 9.3V5"></path><path d="M3 5v14a9 3 0 0 0 6.47 2.88"></path><path d="M12 12v4h4"></path><path d="M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Database backups</h3><p class="text-foreground-light text-sm line-clamp-2">Projects are backed up daily with Point in Time recovery options.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/declarative-schemas"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Declarative Schemas</h3><p class="text-foreground-light text-sm line-clamp-2">Simplify database management with declarative schema files.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/deno-edge-functions"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code2 w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m5 12-3 3 3 3"></path><path d="m9 18 3-3-3-3"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Deno Edge Functions</h3><p class="text-foreground-light text-sm line-clamp-2">Globally distributed TypeScript functions to execute custom business logic.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/email-login"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Email login</h3><p class="text-foreground-light text-sm line-clamp-2">Build email logins for your application or website.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/file-storage"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folders w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 17a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3.9a2 2 0 0 1-1.69-.9l-.81-1.2a2 2 0 0 0-1.67-.9H8a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2Z"></path><path d="M2 8v11a2 2 0 0 0 2 2h14"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">File storage</h3><p class="text-foreground-light text-sm line-clamp-2">Supabase Storage makes it simple to store and serve files.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/foreign-key-selector"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-zap w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 15 21.84"></path><path d="M21 5V8"></path><path d="M21 12L18 17H22L19 22"></path><path d="M3 12A9 3 0 0 0 14.59 14.87"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Foreign Key Selector</h3><p class="text-foreground-light text-sm line-clamp-2">Easily manage foreign key relationships between tables.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/image-transformations"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><circle cx="9" cy="9" r="2"></circle><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Image transformations</h3><p class="text-foreground-light text-sm line-clamp-2">Optimize and resize images on-the-fly directly from your Supabase storage buckets.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/log-drains"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Log Drains</h3><p class="text-foreground-light text-sm line-clamp-2">Export logs to external destinations for enhanced monitoring.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/logs-analytics"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Logs &amp; Analytics</h3><p class="text-foreground-light text-sm line-clamp-2">Gain insights into your application’s performance and usage.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/mcp-server"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-cog w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><circle cx="12" cy="17" r="3"></circle><path d="M4.2 15.1A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2"></path><path d="m15.7 18.4-.9-.3"></path><path d="m9.2 15.9-.9-.3"></path><path d="m10.6 20.7.3-.9"></path><path d="m13.1 14.2.3-.9"></path><path d="m13.6 20.7-.4-1"></path><path d="m10.8 14.3-.4-1"></path><path d="m8.3 18.6 1-.4"></path><path d="m14.7 15.8 1-.4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">MCP Server</h3><p class="text-foreground-light text-sm line-clamp-2">Connect your AI tools using the official Supabase Model Context Protocol (MCP) server.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/management-api"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code2 w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m5 12-3 3 3 3"></path><path d="m9 18 3-3-3-3"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Management API</h3><p class="text-foreground-light text-sm line-clamp-2">Manage your projects programmatically.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/multi-factor-authentication"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Multi-Factor Authentication (MFA)</h3><p class="text-foreground-light text-sm line-clamp-2">Add an extra layer of security to your application with MFA.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/npm-compatibility"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code2 w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m5 12-3 3 3 3"></path><path d="m9 18 3-3-3-3"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">NPM compatibility</h3><p class="text-foreground-light text-sm line-clamp-2">Edge Functions natively support NPM modules and Node built-in APIs.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/network-restrictions"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-x w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><line x1="17" x2="22" y1="8" y2="13"></line><line x1="22" x2="17" y1="8" y2="13"></line></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Network restrictions</h3><p class="text-foreground-light text-sm line-clamp-2">Restrict IP ranges that can connect to your database.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/orioledb"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">OrioleDB</h3><p class="text-foreground-light text-sm line-clamp-2">New Postgres storage engine that&#x27;s better than Heap storage.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/passwordless-login-via-magicklink"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Passwordless login via Magic Links</h3><p class="text-foreground-light text-sm line-clamp-2">Build passwordless logins via magic links for your application or website.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/phone-logins"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-smartphone w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Phone logins</h3><p class="text-foreground-light text-sm line-clamp-2">Provide phone logins using a third-party SMS provider.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/policy-templates"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-plus w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M9 12h6"></path><path d="M12 9v6"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Policy Templates</h3><p class="text-foreground-light text-sm line-clamp-2">Quickly implement common security policies.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/postgres-extensions"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-puzzle w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Postgres Extensions</h3><p class="text-foreground-light text-sm line-clamp-2">Enhance your database with popular Postgres extensions.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/postgres-roles"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Postgres Roles</h3><p class="text-foreground-light text-sm line-clamp-2">Managing access to your Postgres database and configuring permissions.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/postgres-database"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Postgres database</h3><p class="text-foreground-light text-sm line-clamp-2">Every project is a full Postgres database.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/queues"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Queues</h3><p class="text-foreground-light text-sm line-clamp-2">Durable messages with guaranteed delivery.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/read-replicas"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Read replicas</h3><p class="text-foreground-light text-sm line-clamp-2">Deploy read-only databases across multiple regions for lower latency.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/realtime-broadcast"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Realtime - Broadcast</h3><p class="text-foreground-light text-sm line-clamp-2">Send messages between connected users through websockets.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/realtime-broadcast-authorization"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Realtime - Broadcast Authorization</h3><p class="text-foreground-light text-sm line-clamp-2">Control access to broadcast channels in real-time.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/realtime-postgres-changes"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-zap w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 15 21.84"></path><path d="M21 5V8"></path><path d="M21 12L18 17H22L19 22"></path><path d="M3 12A9 3 0 0 0 14.59 14.87"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Realtime - Postgres changes</h3><p class="text-foreground-light text-sm line-clamp-2">Receive your database changes through websockets.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/realtime-presence"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Realtime - Presence</h3><p class="text-foreground-light text-sm line-clamp-2">Synchronize shared state between users through websockets.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/realtime-presence-authorization"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Realtime - Presence Authorization</h3><p class="text-foreground-light text-sm line-clamp-2">Manage presence information securely in real-time.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/regional-invocations"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Regional invocations</h3><p class="text-foreground-light text-sm line-clamp-2">Execute an Edge Function in a region close to your database.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/reports-and-metrics"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-no-axes-column-increasing w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><line x1="12" x2="12" y1="20" y2="10"></line><line x1="18" x2="18" y1="20" y2="4"></line><line x1="6" x2="6" y1="20" y2="16"></line></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Reports &amp; Metrics</h3><p class="text-foreground-light text-sm line-clamp-2">Monitor your project&#x27;s health with usage insights.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/resumable-uploads"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-upload w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M12 13v8"></path><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path><path d="m8 17 4-4 4 4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Resumable uploads</h3><p class="text-foreground-light text-sm line-clamp-2">Upload large files using resumable uploads.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/role-based-access-control"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-plus w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M9 12h6"></path><path d="M12 9v6"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Role-Based Access Control (RBAC)</h3><p class="text-foreground-light text-sm line-clamp-2">Define and manage user roles securely</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/s3-compatibility"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-upload w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M12 13v8"></path><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path><path d="m8 17 4-4 4 4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">S3 compatibility</h3><p class="text-foreground-light text-sm line-clamp-2">Interact with Storage from tools which support the S3 protocol.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="features/soc-2-compliance.html"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-check w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="m9 12 2 2 4-4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">SOC 2 Compliance</h3><p class="text-foreground-light text-sm line-clamp-2">Build with confidence on a SOC 2 compliant platform.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/sql-editor"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code2 w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m5 12-3 3 3 3"></path><path d="m9 18 3-3-3-3"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">SQL Editor</h3><p class="text-foreground-light text-sm line-clamp-2">A powerful interface for writing and executing SQL queries.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/ssl-enforcement"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-check w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="m9 12 2 2 4-4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">SSL enforcement</h3><p class="text-foreground-light text-sm line-clamp-2">Enforce secure connections to your Postgres clients.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/security-and-performance-advisor"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-plus w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M9 12h6"></path><path d="M12 9v6"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Security &amp; Performance Advisor</h3><p class="text-foreground-light text-sm line-clamp-2">Optimize your database security and performance effortlessly.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/server-side-auth"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-server w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect><rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect><line x1="6" x2="6.01" y1="6" y2="6"></line><line x1="6" x2="6.01" y1="18" y2="18"></line></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Server-side Auth</h3><p class="text-foreground-light text-sm line-clamp-2">Helpers for implementing user authentication in popular server-side languages.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/smart-cdn"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Smart Content Delivery Network</h3><p class="text-foreground-light text-sm line-clamp-2">Automatically revalidate assets at the edge via the Smart CDN.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/social-login"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Social login</h3><p class="text-foreground-light text-sm line-clamp-2">Provide social logins from platforms like Apple, GitHub, and Slack.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/ai-assistant"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d="M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d="M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d="M19.938 10.5a4 4 0 0 1 .585.396"></path><path d="M6 18a4 4 0 0 1-1.967-.516"></path><path d="M19.967 17.484A4 4 0 0 1 18 18"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Supabase AI Assistant</h3><p class="text-foreground-light text-sm line-clamp-2">Your intelligent companion for managing Postgres databases.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/supavisor"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Supavisor</h3><p class="text-foreground-light text-sm line-clamp-2">A scalable connection pooler for Postgres.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/terraform-provider"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-package w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="m7.5 4.27 9 5.15"></path><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path><path d="m3.3 7 8.7 5 8.7-5"></path><path d="M12 22V12"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Terraform provider</h3><p class="text-foreground-light text-sm line-clamp-2">Manage Supabase infrastructure via Terraform.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/user-impersonation"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">User Impersonation</h3><p class="text-foreground-light text-sm line-clamp-2">Experience your application as any user.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/vault"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Vault</h3><p class="text-foreground-light text-sm line-clamp-2">Manage secrets safely in Postgres.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/vector-database"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-scatter w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle><circle cx="18.5" cy="5.5" r=".5" fill="currentColor"></circle><circle cx="11.5" cy="11.5" r=".5" fill="currentColor"></circle><circle cx="7.5" cy="16.5" r=".5" fill="currentColor"></circle><circle cx="17.5" cy="14.5" r=".5" fill="currentColor"></circle><path d="M3 3v16a2 2 0 0 0 2 2h16"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Vector database</h3><p class="text-foreground-light text-sm line-clamp-2">Store vector embeddings right next to the rest of your data.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a class="flex flex-col justify-start items-stretch group cursor-pointer transition rounded-xl focus-visible:ring-2 focus-visible:ring-foreground-lighter outline-none outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" href="https://supabase.com/features/visual-schema-designer"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger h-full"><div class="relative z-10 w-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light flex md:flex-col gap-3 sm:gap-2 h-full items-start p-2"><div class="relative rounded-lg min-h-[80px] max-h-[80px] md:max-h-[140px] h-full md:h-auto aspect-square md:w-full md:!aspect-video bg-alternative flex items-center justify-center shadow-inner border border-muted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rectangle-ellipsis w-5 h-5 text-foreground-light group-hover:text-foreground transition-colors"><rect width="20" height="12" x="2" y="6" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 12h.01"></path><path d="M7 12h.01"></path></svg></div><div class="md:p-2 md:pt-1 flex flex-col h-full md:h-auto flex-grow gap-0.5 md:gap-1.5 justify-center md:justify-start"><h3 class="text-sm md:text-base text-foreground !leading-5">Visual Schema Designer</h3><p class="text-foreground-light text-sm line-clamp-2">Design your Postgres database schema with an intuitive interface.</p></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/features","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>