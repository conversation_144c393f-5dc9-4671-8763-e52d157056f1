# Azure Resource Inventory — 2025-08-09

Summary
- Scope: Active Azure subscription (user already authenticated)
- Region focus: southafricanorth
- Resources found: 12 across 3 resource groups (note RG name casing inconsistency)
- Observation: Resource group name mismatch detected — `rg-Aubsz26-0135_ai` vs `RG-AUBSZ26-0135_AI` (consider standardizing)

Inventory (from az resource list)

| Name | Type | Resource Group | Location |
|---|---|---|---|
| kv-aubsz261299396431324 | Microsoft.KeyVault/vaults | rg-Aubsz26-0135_ai | southafricanorth |
| ai-aubsz261231ai299396431324 | Microsoft.CognitiveServices/accounts | rg-Aubsz26-0135_ai | southafricanorth |
| staubsz26123299396431324 | Microsoft.Storage/storageAccounts | rg-Aubsz26-0135_ai | southafricanorth |
| aubsz26-1231_ai | Microsoft.MachineLearningServices/workspaces | rg-Aubsz26-0135_ai | southafricanorth |
| aubsz26-1553 | Microsoft.MachineLearningServices/workspaces | rg-Aubsz26-0135_ai | southafricanorth |
| testvm-nsg | Microsoft.Network/networkSecurityGroups | rg-Aubsz26-0135_ai | southafricanorth |
| vnet-southafricanorth | Microsoft.Network/virtualNetworks | rg-Aubsz26-0135_ai | southafricanorth |
| NetworkWatcher_southafricanorth | Microsoft.Network/networkWatchers | NetworkWatcherRG | southafricanorth |
| testvm146 | Microsoft.Network/networkInterfaces | rg-Aubsz26-0135_ai | southafricanorth |
| testvm | Microsoft.Compute/virtualMachines | rg-Aubsz26-0135_ai | southafricanorth |
| testvm_OsDisk_1_a4fddbe8fe334feda668dc8f7d29a7e8 | Microsoft.Compute/disks | RG-AUBSZ26-0135_AI | southafricanorth |
| pip-vpn | Microsoft.Network/publicIPAddresses | rg-Aubsz26-0135_ai | southafricanorth |

Commands used
- Table view: az resource list --query "[].{Name:name, Type:type, ResourceGroup:resourceGroup, Location:location}" -o table
- JSON view: az resource list --query "[].{Name:name, Type:type, ResourceGroup:resourceGroup, Location:location}" -o json

Quick follow-ups
- Export JSON: az resource list -o json > ~/azure-resources-2025-08-09.json
- Export CSV (requires jq): az resource list -o json | jq -r '.[] | [.name, .type, .resourceGroup, .location] | @csv' > ~/azure-resources-2025-08-09.csv
- Filter by tag: az resource list --tag Environment=Prod -o table
- List VMs (detailed): az vm list -d -o table
- List by resource group: az resource list --resource-group rg-Aubsz26-0135_ai -o table

Notes
- Consider aligning resource group casing and naming conventions.
- Tagging recommendation: Environment, Owner, CostCenter, Application, DataClassification.
- Lock critical resources (CanNotDelete) where appropriate.

Next actions
- [ ] Standardize resource group naming
- [ ] Add/verify tags on all resources
- [ ] Review NSG `testvm-nsg` rules
- [ ] Confirm if VM `testvm` and NIC `testvm146` are still needed
- [ ] Enable soft delete and purge protection on Key Vault
